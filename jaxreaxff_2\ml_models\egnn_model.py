import jax
import jax.numpy as jnp
import flax.linen as nn
from typing import Dict, Any, Tuple, Optional, List, Callable
from functools import partial
import numpy as np
from .base_model import BaseModel

class EGNN_Layer(nn.Module):
    """
    等变图神经网络层 (E-GNN)
    
    保持旋转、平移和排列不变性的图神经网络层
    """
    hidden_dim: int
    edge_dim: int = 0
    dropout_rate: float = 0.0
    activation: Callable = nn.silu
    update_coords: bool = True
    norm_feats: bool = True
    norm_coords: bool = True
    tanh: bool = False
    coords_range: float = 10.0
    
    @nn.compact
    def __call__(
        self,
        h: jnp.ndarray,  # 节点特征 [batch_size, n_nodes, hidden_dim]
        x: jnp.ndarray,  # 节点坐标 [batch_size, n_nodes, 3]
        edge_index: jnp.ndarray,  # 边索引 [batch_size, n_edges, 2]
        edge_attr: Optional[jnp.ndarray] = None,  # 边特征 [batch_size, n_edges, edge_dim]
        mask: Optional[jnp.ndarray] = None,  # 节点掩码 [batch_size, n_nodes]
        training: bool = False
    ) -> Tuple[jnp.ndarray, jnp.ndarray]:
        """
        前向传播
        
        Args:
            h: 节点特征 [batch_size, n_nodes, hidden_dim]
            x: 节点坐标 [batch_size, n_nodes, 3]
            edge_index: 边索引 [batch_size, n_edges, 2]
            edge_attr: 边特征 [batch_size, n_edges, edge_dim]
            mask: 节点掩码 [batch_size, n_nodes]
            training: 是否处于训练模式
            
        Returns:
            h_updated: 更新后的节点特征
            x_updated: 更新后的节点坐标
        """
        batch_size, n_nodes, _ = h.shape
        n_edges = edge_index.shape[1]
        
        # 获取边的源节点和目标节点
        src_idx = edge_index[..., 0]  # [batch_size, n_edges]
        dst_idx = edge_index[..., 1]  # [batch_size, n_edges]
        
        # 为每条边收集源节点和目标节点的特征和坐标
        h_src = jnp.zeros((batch_size, n_edges, self.hidden_dim))
        h_dst = jnp.zeros((batch_size, n_edges, self.hidden_dim))
        x_src = jnp.zeros((batch_size, n_edges, 3))
        x_dst = jnp.zeros((batch_size, n_edges, 3))
        
        # 使用高级索引收集特征
        for b in range(batch_size):
            h_src = h_src.at[b].set(h[b][src_idx[b]])
            h_dst = h_dst.at[b].set(h[b][dst_idx[b]])
            x_src = x_src.at[b].set(x[b][src_idx[b]])
            x_dst = x_dst.at[b].set(x[b][dst_idx[b]])
        
        # 计算相对位置
        rel_pos = x_src - x_dst  # [batch_size, n_edges, 3]
        rel_pos_norm = jnp.sqrt(jnp.sum(rel_pos**2, axis=-1, keepdims=True) + 1e-8)  # [batch_size, n_edges, 1]
        
        # 节点特征更新
        # 消息传递网络
        edge_features_list = [h_src, h_dst, rel_pos_norm]
        if edge_attr is not None:
            edge_features_list.append(edge_attr)
        
        edge_features = jnp.concatenate(edge_features_list, axis=-1)
        
        # 边消息MLP
        edge_msg = nn.Sequential([
            nn.Dense(features=self.hidden_dim),
            self.activation,
            nn.Dense(features=self.hidden_dim),
            self.activation
        ])(edge_features)
        
        # 聚合消息
        aggr_msgs = jnp.zeros((batch_size, n_nodes, self.hidden_dim))
        
        # 使用高级索引聚合消息
        for b in range(batch_size):
            msgs_b = edge_msg[b]
            dst_idx_b = dst_idx[b]
            
            # 按目标节点索引聚合
            for i in range(n_edges):
                node_idx = dst_idx_b[i]
                aggr_msgs = aggr_msgs.at[b, node_idx].add(msgs_b[i])
        
        # 节点特征更新MLP
        node_inputs = jnp.concatenate([h, aggr_msgs], axis=-1)
        h_updated = nn.Sequential([
            nn.Dense(features=self.hidden_dim),
            self.activation,
            nn.Dense(features=self.hidden_dim)
        ])(node_inputs)
        
        # 应用残差连接
        h_updated = h + h_updated
        
        # 特征归一化（如果需要）
        if self.norm_feats:
            h_updated = nn.LayerNorm()(h_updated)
        
        # 节点坐标更新（如果需要）
        if self.update_coords:
            # 计算坐标偏移量
            coord_mlp = nn.Sequential([
                nn.Dense(features=self.hidden_dim),
                self.activation,
                nn.Dense(features=1, kernel_init=nn.initializers.zeros)
            ])
            
            # 为每条边计算坐标偏移权重
            edge_weight = coord_mlp(edge_features)  # [batch_size, n_edges, 1]
            
            # 按相对位置加权
            weighted_rel_pos = edge_weight * rel_pos  # [batch_size, n_edges, 3]
            
            # 聚合坐标更新
            coord_update = jnp.zeros((batch_size, n_nodes, 3))
            
            # 使用高级索引聚合坐标更新
            for b in range(batch_size):
                rel_pos_b = weighted_rel_pos[b]
                dst_idx_b = dst_idx[b]
                
                # 按目标节点索引聚合
                for i in range(n_edges):
                    node_idx = dst_idx_b[i]
                    coord_update = coord_update.at[b, node_idx].add(rel_pos_b[i])
            
            # 更新坐标
            x_updated = x + coord_update
            
            # 坐标归一化（如果需要）
            if self.norm_coords:
                mean = jnp.mean(x_updated, axis=1, keepdims=True)
                x_updated = x_updated - mean
                
            # 限制坐标范围（如果需要）
            if self.tanh:
                x_updated = self.coords_range * nn.tanh(x_updated / self.coords_range)
        else:
            x_updated = x
        
        # 应用掩码（如果提供）
        if mask is not None:
            mask = mask[..., None]  # [batch_size, n_nodes, 1]
            h_updated = h_updated * mask
            x_updated = x_updated * mask
        
        return h_updated, x_updated


class EGNN(BaseModel):
    """
    等变图神经网络模型
    
    保持旋转、平移和排列不变性的图神经网络模型，适用于物理系统建模
    """
    hidden_dim: int = 128
    out_dim: int = 1
    edge_dim: int = 0
    num_layers: int = 4
    dropout_rate: float = 0.0
    activation: Callable = nn.silu
    norm_feats: bool = True
    norm_coords: bool = True
    tanh: bool = False
    coords_range: float = 10.0
    
    def setup(self):
        # EGNN层
        self.layers = [
            EGNN_Layer(
                hidden_dim=self.hidden_dim,
                edge_dim=self.edge_dim,
                dropout_rate=self.dropout_rate,
                activation=self.activation,
                update_coords=(i < self.num_layers - 1),  # 最后一层不更新坐标
                norm_feats=self.norm_feats,
                norm_coords=self.norm_coords,
                tanh=self.tanh,
                coords_range=self.coords_range
            )
            for i in range(self.num_layers)
        ]
        
        # 输出MLP
        self.output_mlp = nn.Sequential([
            nn.Dense(features=self.hidden_dim),
            self.activation,
            nn.Dense(features=self.out_dim)
        ])
    
    def __call__(
        self,
        h: jnp.ndarray,  # 节点特征 [batch_size, n_nodes, hidden_dim]
        x: jnp.ndarray,  # 节点坐标 [batch_size, n_nodes, 3]
        edge_index: jnp.ndarray,  # 边索引 [batch_size, n_edges, 2]
        edge_attr: Optional[jnp.ndarray] = None,  # 边特征 [batch_size, n_edges, edge_dim]
        mask: Optional[jnp.ndarray] = None,  # 节点掩码 [batch_size, n_nodes]
        training: bool = False,
        return_node_features: bool = False
    ) -> Dict[str, jnp.ndarray]:
        """
        前向传播
        
        Args:
            h: 节点特征 [batch_size, n_nodes, hidden_dim]
            x: 节点坐标 [batch_size, n_nodes, 3]
            edge_index: 边索引 [batch_size, n_edges, 2]
            edge_attr: 边特征 [batch_size, n_edges, edge_dim]
            mask: 节点掩码 [batch_size, n_nodes]
            training: 是否处于训练模式
            return_node_features: 是否返回节点特征
            
        Returns:
            outputs: 包含预测能量和可选节点特征的字典
        """
        # 通过EGNN层
        h_current, x_current = h, x
        
        for layer in self.layers:
            h_current, x_current = layer(
                h_current, 
                x_current, 
                edge_index, 
                edge_attr=edge_attr, 
                mask=mask, 
                training=training
            )
        
        # 如果提供了掩码，应用掩码
        if mask is not None:
            h_current = h_current * mask[..., None]
        
        # 聚合节点特征
        if mask is not None:
            # 计算有效节点数
            n_nodes = jnp.sum(mask, axis=1, keepdims=True)
            # 平均池化
            h_graph = jnp.sum(h_current, axis=1) / (n_nodes + 1e-8)
        else:
            # 没有掩码，简单平均
            h_graph = jnp.mean(h_current, axis=1)
        
        # 预测输出
        energy = self.output_mlp(h_graph)  # [batch_size, out_dim]
        
        # 准备输出
        outputs = {'energy': energy.squeeze(-1)}  # [batch_size]
        
        if return_node_features:
            outputs['node_features'] = h_current
            outputs['node_coords'] = x_current
        
        return outputs
    
    def predict_energy_and_forces(
        self,
        h: jnp.ndarray,  # 节点特征 [batch_size, n_nodes, hidden_dim]
        x: jnp.ndarray,  # 节点坐标 [batch_size, n_nodes, 3]
        edge_index: jnp.ndarray,  # 边索引 [batch_size, n_edges, 2]
        edge_attr: Optional[jnp.ndarray] = None,  # 边特征 [batch_size, n_edges, edge_dim]
        mask: Optional[jnp.ndarray] = None,  # 节点掩码 [batch_size, n_nodes]
    ) -> Dict[str, jnp.ndarray]:
        """
        预测能量和力
        
        Args:
            h: 节点特征 [batch_size, n_nodes, hidden_dim]
            x: 节点坐标 [batch_size, n_nodes, 3]
            edge_index: 边索引 [batch_size, n_edges, 2]
            edge_attr: 边特征 [batch_size, n_edges, edge_dim]
            mask: 节点掩码 [batch_size, n_nodes]
            
        Returns:
            outputs: 包含预测能量和力的字典
        """
        # 定义能量预测函数
        def energy_fn(positions):
            return self(
                h, 
                positions, 
                edge_index, 
                edge_attr=edge_attr, 
                mask=mask, 
                training=False
            )['energy']
        
        # 预测能量
        energy = energy_fn(x)
        
        # 计算力（能量对坐标的负梯度）
        forces = -1.0 * jax.grad(lambda pos: jnp.sum(energy_fn(pos)))(x)
        
        # 应用掩码（如果提供）
        if mask is not None:
            forces = forces * mask[..., None]
        
        return {
            'energy': energy,
            'forces': forces
        }


def create_egnn_features(
    atom_types: jnp.ndarray,
    positions: jnp.ndarray,
    bonds: Optional[jnp.ndarray] = None,
    cutoff: float = 5.0
) -> Tuple[jnp.ndarray, jnp.ndarray, jnp.ndarray]:
    """
    为EGNN模型创建特征
    
    Args:
        atom_types: 原子类型 [batch_size, n_atoms]
        positions: 原子坐标 [batch_size, n_atoms, 3]
        bonds: 化学键类型 [batch_size, n_atoms, n_atoms]
        cutoff: 截断距离
        
    Returns:
        node_features: 节点特征 [batch_size, n_atoms, hidden_dim]
        edge_index: 边索引 [batch_size, n_edges, 2]
        edge_attr: 边特征 [batch_size, n_edges, edge_dim]
    """
    batch_size, n_atoms = atom_types.shape
    
    # 创建节点特征（一热编码原子类型）
    atom_types_unique = jnp.unique(atom_types)
    n_atom_types = len(atom_types_unique)
    
    node_features = jnp.zeros((batch_size, n_atoms, n_atom_types))
    for i, atom_type in enumerate(atom_types_unique):
        node_features = node_features.at[:, atom_types == atom_type, i].set(1.0)
    
    # 创建边索引和特征
    edge_indices = []
    edge_attrs = []
    
    for b in range(batch_size):
        # 计算原子间距离
        diffs = positions[b, :, None, :] - positions[b, None, :, :]  # [n_atoms, n_atoms, 3]
        dists = jnp.sqrt(jnp.sum(diffs**2, axis=-1) + 1e-8)  # [n_atoms, n_atoms]
        
        # 创建邻接矩阵（基于距离截断）
        adj = (dists < cutoff).astype(jnp.float32)
        adj = adj - jnp.eye(n_atoms)  # 移除自环
        adj = jnp.where(adj > 0, 1.0, 0.0)
        
        # 如果提供了化学键信息，使用它
        if bonds is not None:
            adj = jnp.where(bonds[b] > 0, 1.0, adj)
        
        # 找到所有边
        src, dst = jnp.where(adj > 0)
        
        # 计算每条边的特征
        edge_features = []
        
        if bonds is not None:
            # 使用化学键类型作为特征
            bond_types = bonds[b, src, dst]
            
            # 一热编码化学键类型
            bond_types_unique = jnp.unique(bonds[b])
            n_bond_types = len(bond_types_unique)
            
            bond_feats = jnp.zeros((len(src), n_bond_types))
            for i, bond_type in enumerate(bond_types_unique):
                if bond_type > 0:  # 跳过0（无键）
                    bond_feats = bond_feats.at[bond_types == bond_type, i].set(1.0)
            
            edge_features.append(bond_feats)
        
        # 计算距离特征
        dist_feats = dists[src, dst][..., None]  # [n_edges, 1]
        edge_features.append(dist_feats)
        
        # 组合所有边特征
        if edge_features:
            edge_attr = jnp.concatenate(edge_features, axis=-1)
        else:
            edge_attr = jnp.ones((len(src), 1))  # 默认特征
        
        # 存储边索引和特征
        edge_index = jnp.stack([src, dst], axis=-1)
        
        edge_indices.append(edge_index)
        edge_attrs.append(edge_attr)
    
    # 找到最大边数
    max_edges = max(edge.shape[0] for edge in edge_indices)
    
    # 填充边索引和特征
    edge_dim = edge_attrs[0].shape[-1]
    batched_edge_index = jnp.zeros((batch_size, max_edges, 2), dtype=jnp.int32)
    batched_edge_attr = jnp.zeros((batch_size, max_edges, edge_dim))
    
    for b in range(batch_size):
        n_edges = edge_indices[b].shape[0]
        batched_edge_index = batched_edge_index.at[b, :n_edges].set(edge_indices[b])
        batched_edge_attr = batched_edge_attr.at[b, :n_edges].set(edge_attrs[b])
    
    return node_features, batched_edge_index, batched_edge_attr 