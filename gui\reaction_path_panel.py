#!/usr/bin/env python3
"""
ReaxFFOpt 反应路径分析面板
用于分析和可视化化学反应路径
"""

import os
import numpy as np

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                            QComboBox, QTableWidget, QTableWidgetItem, QGroupBox,
                            QSplitter, QTabWidget, QTreeWidget, QTreeWidgetItem,
                            QFileDialog, QMessageBox, QTextEdit, QSpinBox,
                            QDoubleSpinBox, QCheckBox, QProgressBar, QFormLayout)
from PyQt5.QtCore import Qt, pyqtSignal, QThread, pyqtSlot
from PyQt5.QtGui import QFont

# 🔧 修复NumPy兼容性问题 - 临时禁用matplotlib
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    MATPLOTLIB_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 反应路径面板导入失败: {e}")
    MATPLOTLIB_AVAILABLE = False
    # 创建占位符类
    class FigureCanvas(QWidget):
        def __init__(self, *args, **kwargs):
            super().__init__()
            self.setMinimumSize(400, 300)
            # 添加提示标签
            layout = QVBoxLayout(self)
            label = QLabel("⚠️ matplotlib不可用\n图表功能暂时禁用")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)

    class Figure:
        def __init__(self, *args, **kwargs):
            pass
    class plt:
        @staticmethod
        def figure(*args, **kwargs):
            return Figure()
        @staticmethod
        def show():
            pass


class ReactionPathWorker(QThread):
    """反应路径计算工作线程"""
    
    progress_updated = pyqtSignal(int)
    path_calculated = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, start_structure, end_structure, method='NEB'):
        super().__init__()
        self.start_structure = start_structure
        self.end_structure = end_structure
        self.method = method
        self.num_images = 8
        
    def run(self):
        """执行反应路径计算"""
        try:
            self.progress_updated.emit(10)
            
            # 模拟反应路径计算
            path_data = self._calculate_reaction_path()
            
            self.progress_updated.emit(100)
            self.path_calculated.emit(path_data)
            
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def _calculate_reaction_path(self):
        """计算反应路径（模拟实现）"""
        import time
        
        # 模拟计算过程
        energies = []
        structures = []
        
        for i in range(self.num_images):
            time.sleep(0.1)  # 模拟计算时间
            
            # 模拟能量曲线（过渡态在中间）
            reaction_coordinate = i / (self.num_images - 1)
            if reaction_coordinate < 0.5:
                # 上升段
                energy = 10 * reaction_coordinate + 2 * np.random.random()
            else:
                # 下降段  
                energy = 10 * (1 - reaction_coordinate) + 2 * np.random.random()
            
            energies.append(energy)
            structures.append(f"structure_{i}")
            
            progress = int((i + 1) / self.num_images * 90)
            self.progress_updated.emit(progress)
        
        return {
            'method': self.method,
            'num_images': self.num_images,
            'energies': energies,
            'structures': structures,
            'reaction_coordinate': list(np.linspace(0, 1, self.num_images)),
            'activation_energy': max(energies) - energies[0],
            'reaction_energy': energies[-1] - energies[0]
        }


class ReactionPathPanel(QWidget):
    """反应路径分析面板"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.reaction_paths = []  # 存储计算的反应路径
        self.current_path = None
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        
        # 路径计算选项卡
        calc_tab = self.create_calculation_tab()
        tab_widget.addTab(calc_tab, "路径计算")
        
        # 结果分析选项卡
        analysis_tab = self.create_analysis_tab()
        tab_widget.addTab(analysis_tab, "结果分析")
        
        # 可视化选项卡
        viz_tab = self.create_visualization_tab()
        tab_widget.addTab(viz_tab, "路径可视化")
        
        layout.addWidget(tab_widget)
    
    def create_calculation_tab(self):
        """创建路径计算选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 输入结构组
        input_group = QGroupBox("输入结构")
        input_layout = QFormLayout(input_group)
        
        self.start_structure_edit = QComboBox()
        self.start_structure_edit.addItem("请选择起始结构")
        input_layout.addRow("起始结构:", self.start_structure_edit)
        
        self.end_structure_edit = QComboBox()
        self.end_structure_edit.addItem("请选择终止结构")
        input_layout.addRow("终止结构:", self.end_structure_edit)
        
        layout.addWidget(input_group)
        
        # 计算参数组
        param_group = QGroupBox("计算参数")
        param_layout = QFormLayout(param_group)
        
        self.method_combo = QComboBox()
        self.method_combo.addItems(["NEB (Nudged Elastic Band)", "String Method", "Growing String"])
        param_layout.addRow("计算方法:", self.method_combo)
        
        self.num_images_spin = QSpinBox()
        self.num_images_spin.setRange(3, 20)
        self.num_images_spin.setValue(8)
        param_layout.addRow("中间像数量:", self.num_images_spin)
        
        self.force_constant_spin = QDoubleSpinBox()
        self.force_constant_spin.setRange(0.1, 10.0)
        self.force_constant_spin.setValue(1.0)
        self.force_constant_spin.setSuffix(" eV/Å²")
        param_layout.addRow("弹簧常数:", self.force_constant_spin)
        
        self.convergence_spin = QDoubleSpinBox()
        self.convergence_spin.setRange(1e-6, 1e-2)
        self.convergence_spin.setValue(1e-4)
        self.convergence_spin.setDecimals(6)
        param_layout.addRow("收敛标准:", self.convergence_spin)
        
        layout.addWidget(param_group)
        
        # 计算控制
        control_layout = QHBoxLayout()
        
        self.calculate_button = QPushButton("开始计算")
        self.calculate_button.clicked.connect(self.start_calculation)
        control_layout.addWidget(self.calculate_button)
        
        self.stop_button = QPushButton("停止计算")
        self.stop_button.setEnabled(False)
        control_layout.addWidget(self.stop_button)
        
        control_layout.addStretch()
        
        # 进度条
        self.progress_bar = QProgressBar()
        control_layout.addWidget(self.progress_bar)
        
        layout.addLayout(control_layout)
        
        # 状态显示
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(100)
        self.status_text.setPlainText("准备就绪。请选择起始和终止结构，然后开始计算。")
        layout.addWidget(self.status_text)
        
        layout.addStretch()
        
        return widget
    
    def create_analysis_tab(self):
        """创建结果分析选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 路径选择
        path_group = QGroupBox("反应路径选择")
        path_layout = QHBoxLayout(path_group)
        
        self.path_combo = QComboBox()
        self.path_combo.addItem("无可用路径")
        self.path_combo.currentTextChanged.connect(self.on_path_selected)
        path_layout.addWidget(QLabel("选择路径:"))
        path_layout.addWidget(self.path_combo)
        
        self.delete_path_button = QPushButton("删除路径")
        self.delete_path_button.setEnabled(False)
        path_layout.addWidget(self.delete_path_button)
        
        layout.addWidget(path_group)
        
        # 分析结果表格
        results_group = QGroupBox("分析结果")
        results_layout = QVBoxLayout(results_group)
        
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(2)
        self.results_table.setHorizontalHeaderLabels(["属性", "值"])
        self.results_table.horizontalHeader().setStretchLastSection(True)
        results_layout.addWidget(self.results_table)
        
        layout.addWidget(results_group)
        
        # 详细信息
        details_group = QGroupBox("路径详细信息")
        details_layout = QVBoxLayout(details_group)
        
        self.details_table = QTableWidget()
        self.details_table.setColumnCount(4)
        self.details_table.setHorizontalHeaderLabels(["像编号", "反应坐标", "能量 (eV)", "相对能量 (eV)"])
        details_layout.addWidget(self.details_table)
        
        layout.addWidget(details_group)
        
        return widget
    
    def create_visualization_tab(self):
        """创建可视化选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 可视化控制
        control_group = QGroupBox("可视化控制")
        control_layout = QHBoxLayout(control_group)
        
        self.plot_type_combo = QComboBox()
        self.plot_type_combo.addItems(["能量曲线", "力分析", "结构变化"])
        self.plot_type_combo.currentTextChanged.connect(self.update_plot)
        control_layout.addWidget(QLabel("图表类型:"))
        control_layout.addWidget(self.plot_type_combo)
        
        self.save_plot_button = QPushButton("保存当前图表")
        self.save_plot_button.clicked.connect(self.save_plot)
        control_layout.addWidget(self.save_plot_button)
        
        self.export_all_button = QPushButton("批量导出所有图表")
        self.export_all_button.clicked.connect(self.export_all_plots)
        control_layout.addWidget(self.export_all_button)
        
        control_layout.addStretch()
        
        layout.addWidget(control_group)
        
        # matplotlib图表
        self.figure = Figure(figsize=(10, 6))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)
        
        return widget
    
    def start_calculation(self):
        """开始反应路径计算"""
        # 检查输入
        start_idx = self.start_structure_edit.currentIndex()
        end_idx = self.end_structure_edit.currentIndex()
        
        if start_idx == 0 or end_idx == 0:
            QMessageBox.warning(self, "输入错误", "请选择有效的起始和终止结构")
            return
        
        # 获取计算参数
        method = self.method_combo.currentText().split()[0]  # 提取方法名
        
        # 创建工作线程
        self.worker = ReactionPathWorker(
            start_structure=self.start_structure_edit.currentText(),
            end_structure=self.end_structure_edit.currentText(),
            method=method
        )
        
        # 连接信号
        self.worker.progress_updated.connect(self.progress_bar.setValue)
        self.worker.path_calculated.connect(self.on_path_calculated)
        self.worker.error_occurred.connect(self.on_error)
        
        # 更新UI状态
        self.calculate_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setValue(0)
        self.status_text.append("开始计算反应路径...")
        
        # 启动计算
        self.worker.start()
    
    def on_path_calculated(self, path_data):
        """处理计算完成的反应路径"""
        # 保存路径数据
        self.reaction_paths.append(path_data)
        self.current_path = path_data
        
        # 更新路径选择器
        path_name = f"{path_data['method']} - {len(self.reaction_paths)}"
        self.path_combo.addItem(path_name)
        self.path_combo.setCurrentText(path_name)
        
        # 更新UI状态
        self.calculate_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.delete_path_button.setEnabled(True)
        
        # 显示结果
        self.update_results_display()
        self.update_plot()
        
        self.status_text.append(f"计算完成！活化能: {path_data['activation_energy']:.2f} eV")
    
    def on_error(self, error_message):
        """处理计算错误"""
        QMessageBox.critical(self, "计算错误", f"反应路径计算失败：\n{error_message}")
        
        self.calculate_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_text.append(f"计算失败: {error_message}")
    
    def on_path_selected(self, path_name):
        """选择路径时的处理"""
        if path_name == "无可用路径":
            self.current_path = None
            return
        
        # 根据名称找到对应的路径数据
        try:
            path_index = int(path_name.split(" - ")[-1]) - 1
            self.current_path = self.reaction_paths[path_index]
            self.update_results_display()
            self.update_plot()
        except (ValueError, IndexError):
            pass
    
    def update_results_display(self):
        """更新结果显示"""
        if not self.current_path:
            return
        
        # 更新总体结果表格
        self.results_table.setRowCount(6)
        
        results = [
            ("计算方法", self.current_path['method']),
            ("中间像数量", str(self.current_path['num_images'])),
            ("活化能 (eV)", f"{self.current_path['activation_energy']:.3f}"),
            ("反应能 (eV)", f"{self.current_path['reaction_energy']:.3f}"),
            ("最高能量 (eV)", f"{max(self.current_path['energies']):.3f}"),
            ("最低能量 (eV)", f"{min(self.current_path['energies']):.3f}")
        ]
        
        for row, (prop, value) in enumerate(results):
            self.results_table.setItem(row, 0, QTableWidgetItem(prop))
            self.results_table.setItem(row, 1, QTableWidgetItem(value))
        
        # 更新详细信息表格
        energies = self.current_path['energies']
        min_energy = min(energies)
        
        self.details_table.setRowCount(len(energies))
        
        for i, (coord, energy) in enumerate(zip(self.current_path['reaction_coordinate'], energies)):
            self.details_table.setItem(i, 0, QTableWidgetItem(str(i)))
            self.details_table.setItem(i, 1, QTableWidgetItem(f"{coord:.3f}"))
            self.details_table.setItem(i, 2, QTableWidgetItem(f"{energy:.3f}"))
            self.details_table.setItem(i, 3, QTableWidgetItem(f"{energy - min_energy:.3f}"))
    
    def update_plot(self):
        """更新图表"""
        if not self.current_path:
            return
        
        self.figure.clear()
        ax = self.figure.add_subplot(111)
        
        # 设置全局字体参数，适合论文展示
        plt.rcParams.update({
            'font.size': 14,           # 基础字体大小
            'axes.titlesize': 18,      # 标题字体大小
            'axes.labelsize': 16,      # 坐标轴标签字体大小
            'xtick.labelsize': 14,     # x轴刻度字体大小
            'ytick.labelsize': 14,     # y轴刻度字体大小
            'legend.fontsize': 14,     # 图例字体大小
            'figure.titlesize': 20,    # 图形标题字体大小
            'font.family': 'sans-serif',  # 使用无衬线字体（兼容中文）
            'font.sans-serif': ['Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans', 'Arial'],
            'axes.unicode_minus': False,  # 解决负号显示问题
        })
        
        plot_type = self.plot_type_combo.currentText()
        
        if plot_type == "能量曲线":
            # 绘制能量曲线
            coords = self.current_path['reaction_coordinate']
            energies = self.current_path['energies']
            min_energy = min(energies)
            relative_energies = [e - min_energy for e in energies]
            
            # 绘制数据点和连线，使用更粗的线条
            ax.plot(coords, relative_energies, 'o-', linewidth=3, markersize=8, 
                   color='#2E8B57', markerfacecolor='#228B22', markeredgecolor='white', 
                   markeredgewidth=1, label='反应路径')
            
            # 设置坐标轴标签
            ax.set_xlabel('反应坐标', fontsize=16, fontweight='bold')
            ax.set_ylabel('相对能量 (eV)', fontsize=16, fontweight='bold')
            
            # 设置标题
            ax.set_title('反应路径能量曲线', fontsize=18, fontweight='bold', pad=20)
            
            # 添加网格
            ax.grid(True, alpha=0.3, linestyle='--')
            
            # 标记过渡态
            max_idx = relative_energies.index(max(relative_energies))
            max_energy = max(relative_energies)
            ax.annotate('过渡态\n(TS)', 
                       xy=(coords[max_idx], relative_energies[max_idx]),
                       xytext=(coords[max_idx], relative_energies[max_idx] + max_energy * 0.15),
                       arrowprops=dict(arrowstyle='->', color='red', lw=2),
                       fontsize=14, ha='center', fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
            
            # 标记反应物和产物
            ax.annotate('反应物\n(R)', xy=(coords[0], relative_energies[0]),
                       xytext=(coords[0], relative_energies[0] - max_energy * 0.1),
                       ha='center', fontsize=12, fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.2', facecolor='lightblue', alpha=0.7))
            
            ax.annotate('产物\n(P)', xy=(coords[-1], relative_energies[-1]),
                       xytext=(coords[-1], relative_energies[-1] - max_energy * 0.1),
                       ha='center', fontsize=12, fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.2', facecolor='lightgreen', alpha=0.7))
        
        elif plot_type == "力分析":
            # 模拟力的分析图
            coords = self.current_path['reaction_coordinate']
            forces = np.gradient(self.current_path['energies'])  # 简单的力模拟
            
            ax.plot(coords, forces, 's-', color='#DC143C', linewidth=3, markersize=8,
                   markerfacecolor='#B22222', markeredgecolor='white', markeredgewidth=1)
            ax.set_xlabel('反应坐标', fontsize=16, fontweight='bold')
            ax.set_ylabel('力 (eV/Å)', fontsize=16, fontweight='bold')
            ax.set_title('反应路径力分析', fontsize=18, fontweight='bold', pad=20)
            ax.grid(True, alpha=0.3, linestyle='--')
            ax.axhline(y=0, color='black', linestyle='-', alpha=0.8, linewidth=2)
            
            # 添加力的正负区域填充
            ax.fill_between(coords, forces, 0, where=(np.array(forces) > 0), 
                           color='red', alpha=0.3, label='排斥力')
            ax.fill_between(coords, forces, 0, where=(np.array(forces) < 0), 
                           color='blue', alpha=0.3, label='吸引力')
            ax.legend()
        
        elif plot_type == "结构变化":
            # 模拟结构参数变化
            coords = self.current_path['reaction_coordinate']
            bond_length = 1.5 + 0.5 * np.sin(np.pi * np.array(coords))  # 模拟键长变化
            
            ax.plot(coords, bond_length, '^-', color='#228B22', linewidth=3, markersize=8,
                   markerfacecolor='#32CD32', markeredgecolor='white', markeredgewidth=1)
            ax.set_xlabel('反应坐标', fontsize=16, fontweight='bold')
            ax.set_ylabel('关键键长 (Å)', fontsize=16, fontweight='bold')
            ax.set_title('反应路径结构变化', fontsize=18, fontweight='bold', pad=20)
            ax.grid(True, alpha=0.3, linestyle='--')
            
            # 添加键长变化的关键点标注
            critical_points = [0, 0.5, 1.0]
            for cp in critical_points:
                idx = int(cp * (len(coords) - 1))
                if idx < len(coords):
                    bl = 1.5 + 0.5 * np.sin(np.pi * cp)
                    ax.plot(coords[idx], bl, 'ro', markersize=10, markeredgecolor='black')
        
        # 设置坐标轴刻度字体大小
        ax.tick_params(axis='both', which='major', labelsize=14, width=2, length=6)
        ax.tick_params(axis='both', which='minor', labelsize=12, width=1, length=4)
        
        # 设置坐标轴线宽
        for spine in ax.spines.values():
            spine.set_linewidth(2)
        
        # 优化布局
        self.figure.tight_layout(pad=2.0)
        self.canvas.draw()
    
    def save_plot(self):
        """保存图表 - 论文级别质量"""
        if not self.current_path:
            QMessageBox.warning(self, "无数据", "没有可保存的图表数据")
            return
        
        # 获取当前图表类型
        plot_type = self.plot_type_combo.currentText()
        default_filename = f"reaction_path_{plot_type}_{len(self.reaction_paths)}"
        
        filename, selected_filter = QFileDialog.getSaveFileName(
            self, "保存论文级别图表", default_filename, 
            "高分辨率PNG (*.png);;矢量PDF (*.pdf);;矢量SVG (*.svg);;EPS格式 (*.eps)"
        )
        
        if filename:
            try:
                # 设置论文级别的保存参数
                save_kwargs = {
                    'dpi': 300,                    # 高分辨率，适合打印
                    'bbox_inches': 'tight',        # 紧凑边界
                    'pad_inches': 0.2,            # 适当留白
                    'facecolor': 'white',         # 白色背景
                    'edgecolor': 'none',          # 无边框
                    'transparent': False,          # 不透明背景
                }
                
                # 根据文件类型调整参数
                if selected_filter.startswith("矢量PDF"):
                    save_kwargs['format'] = 'pdf'
                    save_kwargs['backend'] = 'pdf'
                elif selected_filter.startswith("矢量SVG"):
                    save_kwargs['format'] = 'svg'
                    save_kwargs['backend'] = 'svg'
                elif selected_filter.startswith("EPS格式"):
                    save_kwargs['format'] = 'eps'
                    save_kwargs['backend'] = 'ps'
                else:  # PNG格式
                    save_kwargs['format'] = 'png'
                    save_kwargs['dpi'] = 600  # PNG使用更高分辨率
                
                # 保存图表
                self.figure.savefig(filename, **save_kwargs)
                
                # 显示保存信息
                file_size = os.path.getsize(filename) / 1024  # KB
                QMessageBox.information(
                    self, "保存成功", 
                    f" 论文级别图表已保存！\n\n"
                    f" 文件: {os.path.basename(filename)}\n"
                    f" 分辨率: {save_kwargs['dpi']} DPI\n"
                    f" 大小: {file_size:.1f} KB\n"
                    f" 位置: {filename}\n\n"
                    f" 此图表已优化用于学术论文发表"
                )
                
            except Exception as e:
                QMessageBox.critical(self, "保存失败", f"保存图表时出错:\n{str(e)}")
                
    def export_all_plots(self):
        """导出所有类型的反应路径图表"""
        if not self.current_path:
            QMessageBox.warning(self, "无数据", "没有可导出的数据")
            return
        
        # 选择保存目录
        save_dir = QFileDialog.getExistingDirectory(
            self, "选择导出目录", "", 
            QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
        )
        
        if not save_dir:
            return
        
        try:
            # 保存当前选择的图表类型
            current_plot_type = self.plot_type_combo.currentText()
            
            # 导出所有类型的图表
            plot_types = ["能量曲线", "力分析", "结构变化"]
            exported_files = []
            
            for plot_type in plot_types:
                # 切换到对应的图表类型
                self.plot_type_combo.setCurrentText(plot_type)
                self.update_plot()
                
                # 构建文件名
                filename = os.path.join(save_dir, f"reaction_path_{plot_type}.png")
                
                # 保存图表
                self.figure.savefig(filename, 
                                  dpi=600, bbox_inches='tight', pad_inches=0.2,
                                  facecolor='white', edgecolor='none')
                exported_files.append(filename)
            
            # 恢复原来的图表类型
            self.plot_type_combo.setCurrentText(current_plot_type)
            self.update_plot()
            
            # 显示导出结果
            QMessageBox.information(
                self, "批量导出成功",
                f" 已成功导出 {len(exported_files)} 个图表文件！\n\n"
                f" 保存位置: {save_dir}\n"
                f" 包含文件:\n" + 
                "\n".join([f"• {os.path.basename(f)}" for f in exported_files]) +
                "\n\n 所有文件均为600 DPI高分辨率，适合论文发表"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "批量导出失败", f"导出过程中出错:\n{str(e)}")
    
    def update_structures(self, structures):
        """更新可用的结构列表"""
        # 清空现有选项
        self.start_structure_edit.clear()
        self.end_structure_edit.clear()
        
        # 添加新的结构选项
        self.start_structure_edit.addItem("请选择起始结构")
        self.end_structure_edit.addItem("请选择终止结构")
        
        if structures:
            print(f" 更新反应路径结构选择器: {len(structures)} 个数据集")
            
            for dataset_name, structure_list in structures.items():
                if isinstance(structure_list, list) and structure_list:
                    print(f"   {dataset_name}: {len(structure_list)} 个结构")
                    
                    for i, structure in enumerate(structure_list):
                        # 尝试获取结构名称，如果没有则使用默认名称
                        if isinstance(structure, dict) and 'name' in structure:
                            structure_name = f"{dataset_name} - {structure['name']}"
                        elif isinstance(structure, str):
                            structure_name = f"{dataset_name} - {structure}"
                        else:
                            structure_name = f"{dataset_name} - 结构 {i+1}"
                        
                        self.start_structure_edit.addItem(structure_name)
                        self.end_structure_edit.addItem(structure_name)
                elif isinstance(structure_list, dict):
                    # 如果结构是字典格式，提取结构名称
                    print(f"   {dataset_name}: 字典格式结构")
                    for key, value in structure_list.items():
                        structure_name = f"{dataset_name} - {key}"
                        self.start_structure_edit.addItem(structure_name)
                        self.end_structure_edit.addItem(structure_name)
                else:
                    print(f"   {dataset_name}: 无有效结构数据")
            
            print(f" 反应路径结构选择器已更新，共 {self.start_structure_edit.count()-1} 个可选结构")
        else:
            print(" 没有可用的结构数据") 