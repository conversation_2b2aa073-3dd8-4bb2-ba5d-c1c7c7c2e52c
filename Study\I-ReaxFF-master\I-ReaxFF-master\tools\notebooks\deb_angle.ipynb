{"cells": [{"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from ase.io.trajectory import TrajectoryWriter,Trajectory\n", "from irff.AtomDance import AtomDance\n", "from ase.calculators.singlepoint import SinglePointCalculator\n", "from ase.io import read,write\n", "from ase import units,Atoms\n", "from ase.visualize import view\n", "from ase.visualize.plot import plot_atoms\n", "from irff.irff_np import IRFF_NP\n", "from irff.plot.LearningResults import learning_result\n", "# from irff.tools import deb_energy\n", "import matplotlib.pyplot as plt\n", "from irff.plot.deb_bde import deb_energy,deb_bo,plot,deb_eover\n", "import numpy as np\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def get_theta(atoms,figsize=(8,6),show=False,print_=False):\n", "    ir = IRFF_NP(atoms=atoms,\n", "                 libfile='ffield.json',\n", "                 nn=True)\n", "    ir.calculate(atoms)\n", "     \n", "    for a,angle in enumerate(ir.angs):  \n", "        i,j,k = angle\n", "        print('{:3d} {:3d} {:3d} {:3d}  {:6.4f}  {:6.4f} Dpi: {:6.4f} SBO: {:6.4f} pbo: {:6.4f} SBO3: {:6.4f}'.format(a,\n", "                     i,j,k,\n", "                     ir.thet0[a],ir.theta[a],ir.sbo[a],ir.<PERSON><PERSON>[a],ir.pbo[a],\n", "                     ir.SBO3[a])) # self.thet0-self.theta"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["1.9024072777777778"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["3.14159*109.0/180.0"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  0   0   1   3  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", "  1   0   1  11  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", "  2   0   2   4  2.3644  2.0944 Dpi: 0.8604 SBO: 1.0216 pbo: 0.6937 SBO3: 1.0376\n", "  3   0  10   8  2.3644  2.0944 Dpi: 0.8604 SBO: 1.0216 pbo: 0.6937 SBO3: 1.0376\n", "  4   1   0   2  3.1416  2.0944 Dpi: 1.3595 SBO: 2.3423 pbo: 0.2763 SBO3: 2.0000\n", "  5   1   0  10  3.1416  2.0944 Dpi: 1.3595 SBO: 2.3423 pbo: 0.2763 SBO3: 2.0000\n", "  6   1   3   5  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", "  7   1   3  14  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", "  8   1  11   9  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", "  9   1  11  22  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 10   2   0  10  3.1416  2.0944 Dpi: 1.3595 SBO: 2.3423 pbo: 0.2763 SBO3: 2.0000\n", " 11   2   4   5  3.1416  2.0944 Dpi: 1.3595 SBO: 2.3423 pbo: 0.2763 SBO3: 2.0000\n", " 12   2   4   6  3.1416  2.0944 Dpi: 1.3595 SBO: 2.3423 pbo: 0.2763 SBO3: 2.0000\n", " 13   3   1  11  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 14   3   5   4  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 15   3   5   7  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 16   3  14  12  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 17   3  14  16  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 18   4   5   7  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 19   4   6   8  2.3644  2.0944 Dpi: 0.8604 SBO: 1.0216 pbo: 0.6937 SBO3: 1.0376\n", " 20   5   3  14  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 21   5   4   6  3.1416  2.0944 Dpi: 1.3595 SBO: 2.3423 pbo: 0.2763 SBO3: 2.0000\n", " 22   5   7   9  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 23   5   7  18  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 24   6   8   9  3.1416  2.0944 Dpi: 1.3595 SBO: 2.3423 pbo: 0.2763 SBO3: 2.0000\n", " 25   6   8  10  3.1416  2.0944 Dpi: 1.3595 SBO: 2.3423 pbo: 0.2763 SBO3: 2.0000\n", " 26   7   9   8  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 27   7   9  11  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 28   7  18  16  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 29   7  18  20  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 30   8   9  11  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 31   9   7  18  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 32   9   8  10  3.1416  2.0944 Dpi: 1.3595 SBO: 2.3423 pbo: 0.2763 SBO3: 2.0000\n", " 33   9  11  22  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 34  11  22  12  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 35  11  22  20  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 36  12  13  15  3.1416  2.0944 Dpi: 1.3595 SBO: 2.3423 pbo: 0.2763 SBO3: 2.0000\n", " 37  12  13  23  3.1416  2.0944 Dpi: 1.3595 SBO: 2.3423 pbo: 0.2763 SBO3: 2.0000\n", " 38  12  14  16  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 39  12  22  20  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 40  13  12  14  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 41  13  12  22  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 42  13  15  17  2.3644  2.0944 Dpi: 0.8604 SBO: 1.0216 pbo: 0.6937 SBO3: 1.0376\n", " 43  13  23  21  2.3644  2.0944 Dpi: 0.8604 SBO: 1.0216 pbo: 0.6937 SBO3: 1.0376\n", " 44  14  12  22  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 45  14  16  17  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 46  14  16  18  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 47  15  13  23  3.1416  2.0944 Dpi: 1.3595 SBO: 2.3423 pbo: 0.2763 SBO3: 2.0000\n", " 48  15  17  16  3.1416  2.0944 Dpi: 1.3595 SBO: 2.3423 pbo: 0.2763 SBO3: 2.0000\n", " 49  15  17  19  3.1416  2.0944 Dpi: 1.3595 SBO: 2.3423 pbo: 0.2763 SBO3: 2.0000\n", " 50  16  17  19  3.1416  2.0944 Dpi: 1.3595 SBO: 2.3423 pbo: 0.2763 SBO3: 2.0000\n", " 51  16  18  20  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 52  17  16  18  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 53  17  19  21  2.3644  2.0944 Dpi: 0.8604 SBO: 1.0216 pbo: 0.6937 SBO3: 1.0376\n", " 54  18  20  21  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 55  18  20  22  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n", " 56  19  21  20  3.1416  2.0944 Dpi: 1.3595 SBO: 2.3423 pbo: 0.2763 SBO3: 2.0000\n", " 57  19  21  23  3.1416  2.0944 Dpi: 1.3595 SBO: 2.3423 pbo: 0.2763 SBO3: 2.0000\n", " 58  20  21  23  3.1416  2.0944 Dpi: 1.3595 SBO: 2.3423 pbo: 0.2763 SBO3: 2.0000\n", " 59  21  20  22  3.1416  2.0944 Dpi: 1.4972 SBO: 2.4626 pbo: 0.0632 SBO3: 2.0000\n"]}], "source": ["atoms  = read('gpd.gen',index=-1)\n", "get_theta(atoms)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  0   0   1   3  3.1416  2.1733 Dpi: 1.4344 SBO: 2.4288 pbo: 0.1135 SBO3: 2.0000\n", "  1   0   1  11  3.1416  2.0307 Dpi: 1.4344 SBO: 2.4288 pbo: 0.1135 SBO3: 2.0000\n", "  2   0   2   4  2.2625  2.0409 Dpi: 0.7910 SBO: 0.9226 pbo: 0.7837 SBO3: 0.8681\n", "  3   0  10   8  2.4361  2.1768 Dpi: 0.9076 SBO: 1.0876 pbo: 0.6163 SBO3: 1.1487\n", "  4   1   0   2  3.1416  2.0836 Dpi: 1.3142 SBO: 2.2802 pbo: 0.2923 SBO3: 2.0000\n", "  5   1   0  10  3.1416  2.0607 Dpi: 1.3142 SBO: 2.2802 pbo: 0.2923 SBO3: 2.0000\n", "  6   1   3   5  3.1416  2.0579 Dpi: 1.4045 SBO: 2.3932 pbo: 0.1686 SBO3: 2.0000\n", "  7   1   3  14  3.1416  2.1477 Dpi: 1.4045 SBO: 2.3932 pbo: 0.1686 SBO3: 2.0000\n", "  8   1  11   9  3.1416  2.1957 Dpi: 1.4399 SBO: 2.4279 pbo: 0.0843 SBO3: 2.0000\n", "  9   1  11  22  3.1416  2.1121 Dpi: 1.4399 SBO: 2.4279 pbo: 0.0843 SBO3: 2.0000\n", " 10   2   0  10  3.1416  2.1109 Dpi: 1.3142 SBO: 2.2802 pbo: 0.2923 SBO3: 2.0000\n", " 11   2   4   5  3.0749  2.1184 Dpi: 1.1439 SBO: 1.7863 pbo: 0.5136 SBO3: 1.9334\n", " 12   2   4   6  3.0749  2.1005 Dpi: 1.1439 SBO: 1.7863 pbo: 0.5136 SBO3: 1.9334\n", " 13   3   1  11  3.1416  2.0737 Dpi: 1.4344 SBO: 2.4288 pbo: 0.1135 SBO3: 2.0000\n", " 14   3   5   4  3.1416  2.0716 Dpi: 1.3300 SBO: 2.3002 pbo: 0.2193 SBO3: 2.0000\n", " 15   3   5   7  3.1416  2.0911 Dpi: 1.3300 SBO: 2.3002 pbo: 0.2193 SBO3: 2.0000\n", " 16   3  14  12  3.1416  1.9509 Dpi: 1.3327 SBO: 2.3216 pbo: 0.1867 SBO3: 2.0000\n", " 17   3  14  16  3.1416  2.0771 Dpi: 1.3327 SBO: 2.3216 pbo: 0.1867 SBO3: 2.0000\n", " 18   4   5   7  3.1416  2.1152 Dpi: 1.3300 SBO: 2.3002 pbo: 0.2193 SBO3: 2.0000\n", " 19   4   6   8  2.4372  2.1782 Dpi: 0.8946 SBO: 1.0886 pbo: 0.5915 SBO3: 1.1503\n", " 20   5   3  14  3.1416  2.0762 Dpi: 1.4045 SBO: 2.3932 pbo: 0.1686 SBO3: 2.0000\n", " 21   5   4   6  3.0749  2.0103 Dpi: 1.1439 SBO: 1.7863 pbo: 0.5136 SBO3: 1.9334\n", " 22   5   7   9  3.1416  2.0598 Dpi: 1.4828 SBO: 2.4770 pbo: 0.1089 SBO3: 2.0000\n", " 23   5   7  18  3.1416  2.0757 Dpi: 1.4828 SBO: 2.4770 pbo: 0.1089 SBO3: 2.0000\n", " 24   6   8   9  3.1416  2.0436 Dpi: 1.4153 SBO: 2.4284 pbo: 0.1986 SBO3: 2.0000\n", " 25   6   8  10  3.1416  2.1628 Dpi: 1.4153 SBO: 2.4284 pbo: 0.1986 SBO3: 2.0000\n", " 26   7   9   8  3.1416  2.1465 Dpi: 1.4126 SBO: 2.4000 pbo: 0.0902 SBO3: 2.0000\n", " 27   7   9  11  3.1416  2.1229 Dpi: 1.4126 SBO: 2.4000 pbo: 0.0902 SBO3: 2.0000\n", " 28   7  18  16  3.1416  2.1662 Dpi: 1.4813 SBO: 2.4696 pbo: 0.0929 SBO3: 2.0000\n", " 29   7  18  20  3.1416  1.9962 Dpi: 1.4813 SBO: 2.4696 pbo: 0.0929 SBO3: 2.0000\n", " 30   8   9  11  3.1416  2.0137 Dpi: 1.4126 SBO: 2.4000 pbo: 0.0902 SBO3: 2.0000\n", " 31   9   7  18  3.1416  2.1439 Dpi: 1.4828 SBO: 2.4770 pbo: 0.1089 SBO3: 2.0000\n", " 32   9   8  10  3.1416  2.0762 Dpi: 1.4153 SBO: 2.4284 pbo: 0.1986 SBO3: 2.0000\n", " 33   9  11  22  3.1416  1.9728 Dpi: 1.4399 SBO: 2.4279 pbo: 0.0843 SBO3: 2.0000\n", " 34  11  22  12  3.1416  2.0386 Dpi: 1.4964 SBO: 2.5139 pbo: 0.1333 SBO3: 2.0000\n", " 35  11  22  20  3.1416  2.1926 Dpi: 1.4964 SBO: 2.5139 pbo: 0.1333 SBO3: 2.0000\n", " 36  12  13  15  1.9654  1.9857 Dpi: 0.4575 SBO: 0.4754 pbo: 0.9646 SBO3: 0.2710\n", " 37  12  13  23  1.9654  1.9099 Dpi: 0.4575 SBO: 0.4754 pbo: 0.9646 SBO3: 0.2710\n", " 38  12  14  16  3.1416  2.2499 Dpi: 1.3327 SBO: 2.3216 pbo: 0.1867 SBO3: 2.0000\n", " 39  12  22  20  3.1416  2.0504 Dpi: 1.4964 SBO: 2.5139 pbo: 0.1333 SBO3: 2.0000\n", " 40  13  12  14  3.0764  1.9385 Dpi: 1.1389 SBO: 1.7892 pbo: 0.4558 SBO3: 1.9350\n", " 41  13  12  22  3.0764  2.1290 Dpi: 1.1389 SBO: 1.7892 pbo: 0.4558 SBO3: 1.9350\n", " 42  13  15  17  2.0376  2.0470 Dpi: 0.5401 SBO: 0.6211 pbo: 0.8977 SBO3: 0.4334\n", " 43  13  23  21  1.9838  1.9141 Dpi: 0.4448 SBO: 0.5167 pbo: 0.9272 SBO3: 0.3137\n", " 44  14  12  22  3.0764  2.2150 Dpi: 1.1389 SBO: 1.7892 pbo: 0.4558 SBO3: 1.9350\n", " 45  14  16  17  3.1416  2.0814 Dpi: 1.4344 SBO: 2.4274 pbo: 0.1371 SBO3: 2.0000\n", " 46  14  16  18  3.1416  2.0427 Dpi: 1.4344 SBO: 2.4274 pbo: 0.1371 SBO3: 2.0000\n", " 47  15  13  23  1.9654  2.3802 Dpi: 0.4575 SBO: 0.4754 pbo: 0.9646 SBO3: 0.2710\n", " 48  15  17  16  3.1416  2.0737 Dpi: 1.2258 SBO: 2.0607 pbo: 0.3852 SBO3: 2.0000\n", " 49  15  17  19  3.1416  2.1537 Dpi: 1.2258 SBO: 2.0607 pbo: 0.3852 SBO3: 2.0000\n", " 50  16  17  19  3.1416  2.0033 Dpi: 1.2258 SBO: 2.0607 pbo: 0.3852 SBO3: 2.0000\n", " 51  16  18  20  3.1416  2.1208 Dpi: 1.4813 SBO: 2.4696 pbo: 0.0929 SBO3: 2.0000\n", " 52  17  16  18  3.1416  2.1548 Dpi: 1.4344 SBO: 2.4274 pbo: 0.1371 SBO3: 2.0000\n", " 53  17  19  21  2.3800  2.0759 Dpi: 0.8623 SBO: 1.0360 pbo: 0.6677 SBO3: 1.0623\n", " 54  18  20  21  3.1416  1.9420 Dpi: 1.3742 SBO: 2.3713 pbo: 0.1231 SBO3: 2.0000\n", " 55  18  20  22  3.1416  2.0902 Dpi: 1.3742 SBO: 2.3713 pbo: 0.1231 SBO3: 2.0000\n", " 56  19  21  20  3.1416  2.1121 Dpi: 1.2313 SBO: 2.1179 pbo: 0.3578 SBO3: 2.0000\n", " 57  19  21  23  3.1416  2.1530 Dpi: 1.2313 SBO: 2.1179 pbo: 0.3578 SBO3: 2.0000\n", " 58  20  21  23  3.1416  1.9796 Dpi: 1.2313 SBO: 2.1179 pbo: 0.3578 SBO3: 2.0000\n", " 59  21  20  22  3.1416  2.2451 Dpi: 1.3742 SBO: 2.3713 pbo: 0.1231 SBO3: 2.0000\n"]}], "source": ["atoms  = read('md.traj',index=-1)\n", "get_theta(atoms)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"scrolled": false}, "outputs": [{"ename": "NameError", "evalue": "name 'images' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-9-9c23de0f2b98>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0me\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mdeb_energy\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mimages\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mshow\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mNameError\u001b[0m: name 'images' is not defined"]}], "source": ["e = deb_energy(images,show=True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 2}