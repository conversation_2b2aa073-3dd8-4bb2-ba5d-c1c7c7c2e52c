"""
多目标/多保真度优化模块
实现面向多个目标函数和多种计算精度的力场参数优化
"""

import numpy as np
from collections import defaultdict
# 🔧 修复NumPy兼容性问题 - 临时禁用matplotlib
try:
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 优化器matplotlib导入失败: {e}")
    MATPLOTLIB_AVAILABLE = False
    # 创建占位符
    class plt:
        @staticmethod
        def figure(*args, **kwargs):
            pass
        @staticmethod
        def subplot(*args, **kwargs):
            return MockAxes()
        @staticmethod
        def show():
            pass
        @staticmethod
        def savefig(*args, **kwargs):
            pass

class MockAxes:
    def plot(self, *args, **kwargs):
        pass
    def scatter(self, *args, **kwargs):
        pass
    def set_xlabel(self, *args, **kwargs):
        pass
    def set_ylabel(self, *args, **kwargs):
        pass
    def set_title(self, *args, **kwargs):
        pass
    def legend(self, *args, **kwargs):
        pass
    def grid(self, *args, **kwargs):
        pass

class MultiObjectiveOptimizer:
    """多目标力场参数优化器"""
    
    def __init__(self, objectives, weights=None):
        """初始化多目标优化器
        
        Args:
            objectives (list): 目标函数列表，每个函数接受参数和训练数据，返回评分
            weights (list, optional): 目标函数权重，默认为均等权重
        """
        self.objectives = objectives
        
        # 如果没有提供权重，默认均等权重
        if weights is None:
            self.weights = [1.0 / len(objectives)] * len(objectives)
        else:
            # 确保权重总和为1.0
            total = sum(weights)
            self.weights = [w / total for w in weights]
        
        # 保存优化历史
        self.history = []
        self.pareto_front = []
    
    def compute_objectives(self, params, data):
        """计算所有目标函数值
        
        Args:
            params (dict): 力场参数
            data (dict): 训练数据
            
        Returns:
            list: 各目标函数值
        """
        scores = []
        for obj_func in self.objectives:
            scores.append(obj_func(params, data))
        
        return scores
    
    def compute_weighted_sum(self, objectives):
        """计算加权和
        
        Args:
            objectives (list): 目标函数值列表
            
        Returns:
            float: 加权和
        """
        return sum(o * w for o, w in zip(objectives, self.weights))
    
    def is_dominated(self, obj1, obj2):
        """判断obj1是否被obj2支配（假设最小化所有目标）
        
        Args:
            obj1 (list): 目标函数值列表1
            obj2 (list): 目标函数值列表2
            
        Returns:
            bool: 如果obj1被obj2支配，则返回True
        """
        # obj1被obj2支配，如果obj2在所有目标上都不比obj1差，且至少一个目标上更好
        not_worse = all(o2 <= o1 for o1, o2 in zip(obj1, obj2))
        better = any(o2 < o1 for o1, o2 in zip(obj1, obj2))
        
        return not_worse and better
    
    def compute_pareto_front(self, results):
        """计算帕累托前沿
        
        Args:
            results (list): 包含(params, objectives)元组的列表
            
        Returns:
            list: 帕累托前沿，包含非支配解的索引
        """
        pareto_front = []
        objectives_list = [r[1] for r in results]
        
        for i, obj_i in enumerate(objectives_list):
            dominated = False
            
            for j, obj_j in enumerate(objectives_list):
                if i != j and self.is_dominated(obj_i, obj_j):
                    dominated = True
                    break
            
            if not dominated:
                pareto_front.append(i)
        
        return pareto_front
    
    def optimize(self, initial_params, data, algorithm="NSGA-II", generations=50, population_size=50):
        """执行多目标优化
        
        Args:
            initial_params (dict): 初始参数
            data (dict): 训练数据
            algorithm (str): 优化算法，"NSGA-II"或"MOEA/D"
            generations (int): 迭代次数
            population_size (int): 种群大小
            
        Returns:
            list: 帕累托最优解集
        """
        # 根据算法选择调用相应的方法
        if algorithm == "NSGA-II":
            return self.optimize_nsga2(initial_params, data, generations, population_size)
        elif algorithm == "MOEA/D":
            return self.optimize_moead(initial_params, data, generations, population_size)
        else:
            raise ValueError(f"不支持的算法: {algorithm}")
    
    def optimize_nsga2(self, initial_params, data, generations, population_size):
        """使用NSGA-II算法进行多目标优化（模拟实现）
        
        在实际应用中，应使用专业库如pymoo或deap实现NSGA-II
        
        Args:
            initial_params (dict): 初始参数
            data (dict): 训练数据
            generations (int): 迭代次数
            population_size (int): 种群大小
            
        Returns:
            list: 帕累托最优解集
        """
        # 模拟NSGA-II优化结果
        print(f"执行NSGA-II多目标优化，代数={generations}，种群大小={population_size}")
        
        # 在实际应用中，这里应该有完整的NSGA-II算法实现
        # 但现在我们只是模拟结果
        
        results = []
        for i in range(10):  # 模拟10个最终解
            # 创建随机参数变化
            params = initial_params.copy()
            for key in params:
                params[key] *= (0.9 + 0.2 * np.random.random())  # 在0.9-1.1倍之间随机变化
            
            # 模拟目标函数值
            # 假设两个目标：能量RMSE和力RMSE
            obj1 = 10.0 * np.random.random()  # 能量RMSE (kcal/mol)
            obj2 = 0.5 * np.random.random()   # 力RMSE (kcal/mol/Å)
            
            results.append((params, [obj1, obj2]))
        
        # 计算帕累托前沿
        pareto_indices = self.compute_pareto_front(results)
        pareto_solutions = [results[i] for i in pareto_indices]
        
        # 保存到历史记录
        self.history = results
        self.pareto_front = pareto_solutions
        
        return pareto_solutions
    
    def optimize_moead(self, initial_params, data, generations, population_size):
        """使用MOEA/D算法进行多目标优化（多目标进化算法分解法）
        
        Args:
            initial_params (dict): 初始参数
            data (dict): 训练数据
            generations (int): 迭代次数
            population_size (int): 种群大小
            
        Returns:
            list: 帕累托最优解集
        """
        # 模拟MOEA/D优化结果
        print(f"执行MOEA/D多目标优化，代数={generations}，种群大小={population_size}")
        
        # 在实际应用中，这里应该有完整的MOEA/D算法实现
        # 但现在我们只是模拟结果
        
        results = []
        for i in range(10):  # 模拟10个最终解
            # 创建随机参数变化
            params = initial_params.copy()
            for key in params:
                params[key] *= (0.9 + 0.2 * np.random.random())
            
            # 模拟目标函数值
            obj1 = 10.0 * np.random.random()  # 能量RMSE
            obj2 = 0.5 * np.random.random()   # 力RMSE
            
            results.append((params, [obj1, obj2]))
        
        # 计算帕累托前沿
        pareto_indices = self.compute_pareto_front(results)
        pareto_solutions = [results[i] for i in pareto_indices]
        
        # 保存到历史记录
        self.history = results
        self.pareto_front = pareto_solutions
        
        return pareto_solutions
    
    def plot_pareto_front(self, filename=None):
        """绘制帕累托前沿
        
        Args:
            filename (str, optional): 保存图像的文件名，如果为None则显示而不保存
            
        Returns:
            matplotlib.figure.Figure: 图形对象
        """
        if not self.history:
            print("没有优化历史记录，无法绘制帕累托前沿")
            return None
        
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # 绘制所有解
        all_obj1 = [h[1][0] for h in self.history]
        all_obj2 = [h[1][1] for h in self.history]
        ax.scatter(all_obj1, all_obj2, c='blue', alpha=0.5, label='所有解')
        
        # 绘制帕累托前沿解
        pareto_obj1 = [p[1][0] for p in self.pareto_front]
        pareto_obj2 = [p[1][1] for p in self.pareto_front]
        ax.scatter(pareto_obj1, pareto_obj2, c='red', s=100, label='帕累托最优解')
        
        # 连接帕累托前沿点
        if len(pareto_obj1) > 1:
            # 按照obj1排序
            sorted_indices = np.argsort(pareto_obj1)
            sorted_obj1 = [pareto_obj1[i] for i in sorted_indices]
            sorted_obj2 = [pareto_obj2[i] for i in sorted_indices]
            ax.plot(sorted_obj1, sorted_obj2, 'r--')
        
        ax.set_xlabel('目标1: 能量RMSE (kcal/mol)')
        ax.set_ylabel('目标2: 力RMSE (kcal/mol/Å)')
        ax.set_title('多目标优化帕累托前沿')
        ax.legend()
        
        # 保存图像或显示
        if filename:
            plt.savefig(filename, dpi=300, bbox_inches='tight')
        
        return fig


class MultiFidelityOptimizer:
    """多保真度优化器"""
    
    def __init__(self, low_fidelity_func, high_fidelity_func, correlation_threshold=0.7):
        """初始化多保真度优化器
        
        Args:
            low_fidelity_func (function): 低保真度评估函数，计算成本低但精度也低
            high_fidelity_func (function): 高保真度评估函数，计算成本高但精度高
            correlation_threshold (float): 相关性阈值，决定何时切换到高保真度评估
        """
        self.low_fidelity_func = low_fidelity_func
        self.high_fidelity_func = high_fidelity_func
        self.correlation_threshold = correlation_threshold
        
        # 保存评估历史
        self.low_fidelity_history = []
        self.high_fidelity_history = []
        self.correlation_history = []
    
    def optimize(self, initial_params, data, budget=100, initial_high_fidelity=5):
        """执行多保真度优化
        
        Args:
            initial_params (dict): 初始参数
            data (dict): 训练数据
            budget (int): 总计算预算（高保真度评估的等效次数）
            initial_high_fidelity (int): 初始高保真度评估次数
            
        Returns:
            dict: 最优参数
        """
        # 假设低保真度评估成本是高保真度的1/10
        low_fidelity_cost = 0.1
        high_fidelity_cost = 1.0
        
        # 剩余预算
        remaining_budget = budget
        
        # 优化循环
        iteration = 0
        current_params = initial_params.copy()
        best_params = initial_params.copy()
        best_score = float('inf')
        
        # 初始高保真度评估
        print(f"执行初始高保真度评估 ({initial_high_fidelity}次)")
        for _ in range(initial_high_fidelity):
            # 随机扰动参数进行探索
            params = current_params.copy()
            for key in params:
                params[key] *= (0.95 + 0.1 * np.random.random())
                
            # 高保真度评估
            high_score = self.high_fidelity_func(params, data)
            self.high_fidelity_history.append((params, high_score))
            
            # 更新最优解
            if high_score < best_score:
                best_score = high_score
                best_params = params.copy()
            
            # 更新预算
            remaining_budget -= high_fidelity_cost
        
        # 主优化循环
        while remaining_budget > 0:
            iteration += 1
            print(f"迭代 {iteration}, 剩余预算: {remaining_budget:.2f}")
            
            # 确定本次迭代使用的评估函数
            use_high_fidelity = False
            
            # 如果有足够的历史数据，计算相关性
            if len(self.low_fidelity_history) > 5 and len(self.high_fidelity_history) > 5:
                correlation = self.compute_correlation()
                self.correlation_history.append(correlation)
                
                # 根据相关性决定是否使用高保真度评估
                if correlation < self.correlation_threshold:
                    use_high_fidelity = True
            
            # 如果预算不足以运行高保真度评估，强制使用低保真度
            if use_high_fidelity and remaining_budget < high_fidelity_cost:
                use_high_fidelity = False
            
            # 随机扰动参数进行探索
            params = current_params.copy()
            for key in params:
                params[key] *= (0.95 + 0.1 * np.random.random())
            
            # 根据决定使用不同保真度评估
            if use_high_fidelity:
                score = self.high_fidelity_func(params, data)
                self.high_fidelity_history.append((params, score))
                remaining_budget -= high_fidelity_cost
                
                # 更新最优解
                if score < best_score:
                    best_score = score
                    best_params = params.copy()
            else:
                score = self.low_fidelity_func(params, data)
                self.low_fidelity_history.append((params, score))
                remaining_budget -= low_fidelity_cost
                
                # 找到当前低保真度最优解
                if len(self.low_fidelity_history) % 10 == 0:
                    # 每10次低保真度评估，用高保真度验证当前最优解
                    best_low_params = min(self.low_fidelity_history[-10:], key=lambda x: x[1])[0]
                    high_score = self.high_fidelity_func(best_low_params, data)
                    self.high_fidelity_history.append((best_low_params, high_score))
                    remaining_budget -= high_fidelity_cost
                    
                    # 更新最优解
                    if high_score < best_score:
                        best_score = high_score
                        best_params = best_low_params.copy()
            
            # 更新当前参数
            current_params = params.copy()
        
        print(f"优化完成，最终得分: {best_score:.4f}")
        
        # 返回结果字典
        result = {
            'best_params': best_params,
            'best_loss': best_score,
            'low_fidelity_evaluations': len(self.low_fidelity_history),
            'high_fidelity_evaluations': len(self.high_fidelity_history),
            'total_iterations': iteration
        }
        
        return result
    
    def compute_correlation(self):
        """计算低保真度和高保真度评估之间的相关性
        
        Returns:
            float: 相关系数
        """
        # 提取最近的评估结果
        low_scores = [entry[1] for entry in self.low_fidelity_history[-10:]]
        high_scores = [entry[1] for entry in self.high_fidelity_history[-10:]]
        
        # 由于低保真度和高保真度评估可能是针对不同参数，这里模拟相关性
        # 在实际应用中，应该使用相同参数进行配对比较
        
        # 模拟相关系数
        correlation = 0.5 + 0.5 * np.random.random()
        
        return correlation
    
    def plot_history(self, filename=None):
        """绘制优化历史
        
        Args:
            filename (str, optional): 保存图像的文件名，如果为None则显示而不保存
            
        Returns:
            matplotlib.figure.Figure: 图形对象
        """
        if not self.low_fidelity_history and not self.high_fidelity_history:
            print("没有优化历史记录，无法绘制")
            return None
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 12))
        
        # 绘制评估得分历史
        low_indices = range(len(self.low_fidelity_history))
        low_scores = [entry[1] for entry in self.low_fidelity_history]
        ax1.scatter(low_indices, low_scores, c='blue', alpha=0.5, label='低保真度评估')
        
        high_indices = range(len(self.high_fidelity_history))
        high_scores = [entry[1] for entry in self.high_fidelity_history]
        ax1.scatter(high_indices, high_scores, c='red', s=100, label='高保真度评估')
        
        ax1.set_xlabel('评估次数')
        ax1.set_ylabel('评估得分')
        ax1.set_title('多保真度优化历史')
        ax1.legend()
        
        # 绘制相关性历史
        if self.correlation_history:
            correlation_indices = range(len(self.correlation_history))
            ax2.plot(correlation_indices, self.correlation_history, 'g-', label='保真度相关性')
            ax2.axhline(y=self.correlation_threshold, color='r', linestyle='--', label='相关性阈值')
            
            ax2.set_xlabel('迭代次数')
            ax2.set_ylabel('相关系数')
            ax2.set_title('保真度层级相关性')
            ax2.legend()
        
        plt.tight_layout()
        
        # 保存图像或显示
        if filename:
            plt.savefig(filename, dpi=300, bbox_inches='tight')
        
        return fig 