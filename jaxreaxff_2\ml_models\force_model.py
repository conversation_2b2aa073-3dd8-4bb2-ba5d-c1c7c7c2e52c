import jax
import jax.numpy as jnp
import flax.linen as nn
from typing import Dict, Any, Tuple, Optional
from .base_model import BaseModel

class ForceModel(BaseModel):
    """用于预测原子受力的模型"""
    
    def __init__(
        self,
        hidden_dim: int = 128,
        num_layers: int = 3,
        dropout_rate: float = 0.1,
        activation: str = 'relu'
    ):
        """
        初始化力场预测模型
        
        Args:
            hidden_dim: 隐藏层维度
            num_layers: 层数
            dropout_rate: Dropout比率
            activation: 激活函数
        """
        super().__init__()
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        self.dropout_rate = dropout_rate
        self.activation = activation
        
        # 定义网络层
        self.layers = []
        for i in range(num_layers):
            self.layers.append(nn.Dense(hidden_dim))
            self.layers.append(nn.Dropout(dropout_rate))
            if activation == 'relu':
                self.layers.append(nn.relu)
            elif activation == 'gelu':
                self.layers.append(nn.gelu)
            else:
                raise ValueError(f"Unsupported activation: {activation}")
        
        # 输出层 (预测3D力场)
        self.output_layer = nn.Dense(3)
        
        # 初始化参数
        self.params = None
    
    def initialize(self, rng: jax.random.PRNGKey, input_shape: Tuple[int, ...]) -> Dict[str, Any]:
        """
        初始化模型参数
        
        Args:
            rng: 随机数生成器
            input_shape: 输入张量的形状
            
        Returns:
            params: 模型参数字典
        """
        # 创建虚拟输入
        dummy_input = jnp.zeros(input_shape)
        
        # 初始化参数
        variables = self.init(rng, dummy_input, training=False)
        return variables['params']
    
    def __call__(self, inputs: jnp.ndarray, training: bool = False) -> jnp.ndarray:
        """
        模型前向传播
        
        Args:
            inputs: 输入数据 [batch_size, n_atoms, feature_dim]
            training: 是否处于训练模式
            
        Returns:
            forces: 预测的力场 [batch_size, n_atoms, 3]
        """
        batch_size, n_atoms, feature_dim = inputs.shape
        
        # 重塑输入以处理每个原子
        x = inputs.reshape(-1, feature_dim)
        
        # 通过所有层
        for layer in self.layers:
            if isinstance(layer, nn.Dropout):
                x = layer(x, deterministic=not training)
            else:
                x = layer(x)
        
        # 输出层
        forces = self.output_layer(x)
        
        # 重塑回原始形状
        forces = forces.reshape(batch_size, n_atoms, 3)
        
        return forces
    
    def get_metrics(self) -> Dict[str, float]:
        """
        获取模型指标
        
        Returns:
            metrics: 模型指标字典
        """
        return {
            'hidden_dim': self.hidden_dim,
            'num_layers': self.num_layers,
            'dropout_rate': self.dropout_rate,
            'activation': self.activation
        }
    
    def save(self, path: str):
        """
        保存模型参数
        
        Args:
            path: 保存路径
        """
        if self.params is None:
            raise ValueError("Model parameters not initialized")
        
        # 将参数转换为numpy数组
        params_np = jax.tree_map(lambda x: jnp.array(x), self.params)
        
        # 保存为numpy文件
        jnp.save(path, params_np)
    
    def load(self, path: str):
        """
        加载模型参数
        
        Args:
            path: 加载路径
        """
        # 加载numpy数组
        params_np = jnp.load(path)
        
        # 转换回JAX数组
        self.params = jax.tree_map(lambda x: jnp.array(x), params_np) 