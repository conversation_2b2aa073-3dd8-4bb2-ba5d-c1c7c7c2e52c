#!/usr/bin/env python3
"""
ReaxFF优化监控仪表板
轻量级监控工具，支持真实优化数据监控
"""

import time
import threading
from datetime import datetime
from flask import Flask, render_template_string, request, jsonify
import numpy as np

class SimpleMonitor:
    """简化监控器 - 支持真实数据和演示数据"""

    def __init__(self):
        self.is_running = False
        self.start_time = None
        self.current_iteration = 0
        self.current_loss = float('inf')
        self.best_loss = float('inf')
        self.loss_history = []
        self.dataset_info = {}
        self.selected_parameters = {}
        self.optimization_method = "Unknown"
        self.is_demo_mode = False
        
    def start_optimization(self, dataset_info=None, selected_params=None, method="Unknown", is_demo=False):
        """开始优化"""
        self.is_running = True
        self.start_time = datetime.now()
        self.current_iteration = 0
        self.loss_history = []
        self.dataset_info = dataset_info or {}
        self.selected_parameters = selected_params or {}
        self.optimization_method = method
        self.is_demo_mode = is_demo
        
    def stop_optimization(self):
        """停止优化"""
        self.is_running = False
        
    def update_progress(self, iteration, loss):
        """更新优化进度"""
        self.current_iteration = iteration
        self.current_loss = loss
        if loss < self.best_loss:
            self.best_loss = loss
        self.loss_history.append(loss)
        
    def get_runtime_str(self):
        """获取运行时间字符串"""
        if not self.start_time:
            return "00:00:00"
        runtime = datetime.now() - self.start_time
        hours, remainder = divmod(int(runtime.total_seconds()), 3600)
        minutes, seconds = divmod(remainder, 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        
    def get_current_data(self):
        """获取当前数据"""
        return {
            'iteration': self.current_iteration,
            'loss': round(self.current_loss, 6) if self.current_loss != float('inf') else 'N/A',
            'best_loss': round(self.best_loss, 6) if self.best_loss != float('inf') else 'N/A',
            'runtime': self.get_runtime_str(),
            'is_running': self.is_running,
            'loss_history': self.loss_history[-50:],  # 只保留最近50个点
            'dataset_info': self.dataset_info,
            'selected_parameters': len(self.selected_parameters),
            'optimization_method': self.optimization_method,
            'is_demo_mode': self.is_demo_mode
        }

# 创建Flask应用
app = Flask(__name__)
monitor = SimpleMonitor()

# HTML模板
DASHBOARD_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>ReaxFF优化监控仪表板</title>
    <meta charset="utf-8">
    <style>
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%); 
            color: white; 
            min-height: 100vh;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { color: #4CAF50; margin: 0; font-size: 2.5em; }
        .header p { color: #888; margin: 10px 0; }
        
        .metrics { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 20px; 
            margin: 30px 0; 
        }
        .metric-card { 
            background: linear-gradient(145deg, #2a2a2a, #3a3a3a); 
            padding: 25px; 
            border-radius: 15px; 
            text-align: center; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            border: 1px solid #444;
        }
        .metric-card h3 { margin: 0 0 15px 0; color: #888; font-size: 1.1em; }
        .metric-value { 
            font-size: 2.2em; 
            font-weight: bold; 
            margin: 10px 0;
        }
        .metric-value.iteration { color: #2196F3; }
        .metric-value.loss { color: #FF9800; }
        .metric-value.best { color: #4CAF50; }
        .metric-value.runtime { color: #9C27B0; }
        .metric-value.status { color: #F44336; }
        
        .chart-container { 
            background: linear-gradient(145deg, #2a2a2a, #3a3a3a); 
            padding: 25px; 
            border-radius: 15px; 
            margin: 20px 0;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            border: 1px solid #444;
        }
        .chart-container h3 { margin: 0 0 20px 0; color: #4CAF50; }
        
        .controls { 
            text-align: center; 
            margin: 30px 0; 
        }
        .btn { 
            background: linear-gradient(145deg, #4CAF50, #45a049); 
            color: white; 
            border: none; 
            padding: 12px 25px; 
            margin: 0 10px; 
            border-radius: 8px; 
            cursor: pointer; 
            font-size: 1em;
            transition: all 0.3s;
        }
        .btn:hover { 
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        .btn.demo { background: linear-gradient(145deg, #2196F3, #1976D2); }
        .btn.stop { background: linear-gradient(145deg, #F44336, #D32F2F); }
        
        .status-running { color: #4CAF50; }
        .status-stopped { color: #F44336; }
        
        .simple-chart {
            width: 100%;
            height: 200px;
            background: #1a1a1a;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }
        
        .update-info {
            text-align: center;
            color: #888;
            margin: 20px 0;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 ReaxFF优化监控仪表板</h1>
            <p>实时监控ReaxFF参数优化过程</p>
        </div>
        
        <div class="metrics">
            <div class="metric-card">
                <h3>📊 当前迭代</h3>
                <div class="metric-value iteration" id="iteration">0</div>
            </div>
            <div class="metric-card">
                <h3>📈 当前损失</h3>
                <div class="metric-value loss" id="loss">N/A</div>
            </div>
            <div class="metric-card">
                <h3>🎯 最佳损失</h3>
                <div class="metric-value best" id="best-loss">N/A</div>
            </div>
            <div class="metric-card">
                <h3>⏱️ 运行时间</h3>
                <div class="metric-value runtime" id="runtime">00:00:00</div>
            </div>
            <div class="metric-card">
                <h3>🔄 状态</h3>
                <div class="metric-value status" id="status">待机</div>
            </div>
            <div class="metric-card">
                <h3>📊 数据集</h3>
                <div class="metric-value" id="dataset-count" style="color: #2196F3;">0</div>
            </div>
            <div class="metric-card">
                <h3>🎯 参数数量</h3>
                <div class="metric-value" id="param-count" style="color: #9C27B0;">0</div>
            </div>
        </div>
        
        <div class="chart-container">
            <h3>📉 损失函数变化趋势</h3>
            <div class="simple-chart" id="loss-chart">
                <canvas id="chart-canvas" width="800" height="200"></canvas>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn demo" onclick="startDemo()">🎮 启动演示</button>
            <button class="btn stop" onclick="stopDemo()">⏹️ 停止演示</button>
            <button class="btn" onclick="refreshData()">🔄 刷新数据</button>
        </div>
        
        <div class="update-info">
            <p>⏰ 最后更新: <span id="last-update">-</span></p>
            <p>💡 页面每3秒自动刷新数据</p>
        </div>
    </div>

    <script>
        let updateInterval;
        
        function updateData() {
            fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('iteration').textContent = data.iteration;
                    document.getElementById('loss').textContent = data.loss;
                    document.getElementById('best-loss').textContent = data.best_loss;
                    document.getElementById('runtime').textContent = data.runtime;
                    document.getElementById('dataset-count').textContent = Object.keys(data.dataset_info || {}).length;
                    document.getElementById('param-count').textContent = data.selected_parameters || 0;
                    
                    const statusElement = document.getElementById('status');
                    if (data.is_running) {
                        const mode = data.is_demo_mode ? '演示模式' : '真实优化';
                        statusElement.textContent = `运行中 (${mode})`;
                        statusElement.className = 'metric-value status status-running';
                    } else {
                        statusElement.textContent = '已停止';
                        statusElement.className = 'metric-value status status-stopped';
                    }
                    
                    // 更新图表
                    updateChart(data.loss_history);
                    
                    // 更新时间
                    document.getElementById('last-update').textContent = new Date().toLocaleTimeString();
                })
                .catch(error => {
                    console.error('获取数据失败:', error);
                });
        }
        
        function updateChart(lossHistory) {
            const canvas = document.getElementById('chart-canvas');
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            if (lossHistory.length === 0) return;
            
            // 设置样式
            ctx.strokeStyle = '#4CAF50';
            ctx.lineWidth = 2;
            ctx.fillStyle = 'rgba(76, 175, 80, 0.1)';
            
            // 计算缩放
            const maxLoss = Math.max(...lossHistory);
            const minLoss = Math.min(...lossHistory);
            const range = maxLoss - minLoss || 1;
            
            // 绘制线条
            ctx.beginPath();
            lossHistory.forEach((loss, index) => {
                const x = (index / (lossHistory.length - 1)) * canvas.width;
                const y = canvas.height - ((loss - minLoss) / range) * canvas.height;
                
                if (index === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            });
            ctx.stroke();
            
            // 绘制填充区域
            ctx.lineTo(canvas.width, canvas.height);
            ctx.lineTo(0, canvas.height);
            ctx.closePath();
            ctx.fill();
        }
        
        function startDemo() {
            fetch('/api/start_demo')
                .then(response => response.json())
                .then(data => {
                    console.log('演示已启动:', data);
                    updateData();
                });
        }
        
        function stopDemo() {
            fetch('/api/control', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({action: 'stop'})
            })
            .then(response => response.json())
            .then(data => {
                console.log('演示已停止:', data);
                updateData();
            });
        }
        
        function refreshData() {
            updateData();
        }
        
        // 启动自动更新
        updateData();
        updateInterval = setInterval(updateData, 3000);
        
        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            if (updateInterval) {
                clearInterval(updateInterval);
            }
        });
    </script>
</body>
</html>
"""

@app.route('/')
def dashboard():
    """主仪表板页面"""
    return render_template_string(DASHBOARD_TEMPLATE)

@app.route('/api/data')
def get_data():
    """获取实时数据"""
    return jsonify(monitor.get_current_data())

@app.route('/api/control', methods=['POST'])
def control_optimization():
    """控制优化过程"""
    try:
        data = request.get_json()
        action = data.get('action')
        
        if action == 'stop':
            monitor.stop_optimization()
            return jsonify({'status': 'stopped'})
        else:
            return jsonify({'error': 'Invalid action'}), 400
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/start_demo')
def start_demo():
    """启动演示优化"""
    if not monitor.is_running:
        # 在后台线程中运行模拟
        demo_thread = threading.Thread(target=simulate_optimization)
        demo_thread.daemon = True
        demo_thread.start()
        return jsonify({'status': 'demo_started'})
    else:
        return jsonify({'status': 'already_running'})

# 简化的API，只保留核心功能

def simulate_optimization():
    """模拟优化过程（演示模式）"""
    # 启动演示模式
    demo_dataset = {"cobalt": "钴数据集（演示）"}
    demo_params = {"p_2_1_1": 2.15, "p_2_1_4": 2.15, "p_2_1_5": 2.15}
    monitor.start_optimization(demo_dataset, demo_params, "演示优化", is_demo=True)

    for i in range(200):
        if not monitor.is_running:
            break

        # 模拟损失函数下降
        noise = np.random.normal(0, 0.1)
        loss = 10.0 * np.exp(-i/50) + noise + 0.1

        monitor.update_progress(i, loss)
        time.sleep(1)  # 每秒更新一次

if __name__ == '__main__':
    print("🚀 启动简化版ReaxFF优化监控仪表板...")
    print("📊 访问地址: http://localhost:5000")
    print("🔧 演示模式: 点击页面上的'启动演示'按钮")
    print("=" * 50)
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n👋 监控仪表板已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
