"""
ReaxFFOpt 优化面板
管理优化算法设置和控制
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QComboBox, QSpinBox, QDoubleSpinBox,
                            QGroupBox, QFormLayout, QCheckBox, QTabWidget,
                            QRadioButton, QButtonGroup, QSlider, QProgressBar)
from PyQt5.QtCore import Qt, pyqtSignal, pyqtSlot


class OptimizationPanel(QWidget):
    """优化面板类，用于配置和控制优化过程"""
    
    # 定义信号
    optimization_started = pyqtSignal(dict)
    optimization_stopped = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 创建主布局
        main_layout = QVBoxLayout(self)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        
        # ===== 基本设置选项卡 =====
        basic_tab = QWidget()
        basic_layout = QVBoxLayout(basic_tab)
        
        # 优化方法设置
        method_group = QGroupBox("优化方法")
        method_layout = QFormLayout(method_group)
        
        self.method_combo = QComboBox()
        self.method_combo.addItems([
            "粒子群优化 (PSO)", 
            "差分进化 (DE)", 
            "遗传算法 (GA)", 
            "拟牛顿法 (BFGS)",
            "量子退火 (QA)",
            "多模态科学模型辅助优化"
        ])
        self.method_combo.currentIndexChanged.connect(self.update_method_options)
        method_layout.addRow("优化算法:", self.method_combo)
        
        # 添加通用优化参数
        self.pop_size_spin = QSpinBox()
        self.pop_size_spin.setRange(10, 1000)
        self.pop_size_spin.setValue(50)
        self.pop_size_spin.setSingleStep(10)
        method_layout.addRow("种群大小:", self.pop_size_spin)
        
        self.max_iter_spin = QSpinBox()
        self.max_iter_spin.setRange(10, 10000)
        self.max_iter_spin.setValue(200)
        self.max_iter_spin.setSingleStep(10)
        method_layout.addRow("最大迭代次数:", self.max_iter_spin)
        
        self.tolerance_spin = QDoubleSpinBox()
        self.tolerance_spin.setRange(1e-10, 1e-1)
        self.tolerance_spin.setValue(1e-5)
        self.tolerance_spin.setDecimals(10)
        self.tolerance_spin.setSingleStep(1e-5)
        method_layout.addRow("收敛容差:", self.tolerance_spin)
        
        basic_layout.addWidget(method_group)
        
        # 加速选项
        acceleration_group = QGroupBox("加速选项")
        acceleration_layout = QFormLayout(acceleration_group)
        
        self.threads_spin = QSpinBox()
        self.threads_spin.setRange(1, 32)
        self.threads_spin.setValue(4)
        acceleration_layout.addRow("并行线程数:", self.threads_spin)
        
        self.quantum_check = QCheckBox("启用量子加速")
        acceleration_layout.addRow("", self.quantum_check)
        
        self.ai_check = QCheckBox("启用多模态科学模型")
        acceleration_layout.addRow("", self.ai_check)
        
        basic_layout.addWidget(acceleration_group)
        
        # ===== 高级设置选项卡 =====
        advanced_tab = QWidget()
        advanced_layout = QVBoxLayout(advanced_tab)
        
        # 算法特定参数
        algorithm_group = QGroupBox("算法特定参数")
        algorithm_layout = QFormLayout(algorithm_group)
        
        # PSO参数
        self.pso_inertia_spin = QDoubleSpinBox()
        self.pso_inertia_spin.setRange(0.1, 1.0)
        self.pso_inertia_spin.setValue(0.7)
        self.pso_inertia_spin.setSingleStep(0.05)
        algorithm_layout.addRow("PSO: 惯性权重:", self.pso_inertia_spin)
        
        self.pso_cognitive_spin = QDoubleSpinBox()
        self.pso_cognitive_spin.setRange(0.1, 3.0)
        self.pso_cognitive_spin.setValue(1.5)
        self.pso_cognitive_spin.setSingleStep(0.1)
        algorithm_layout.addRow("PSO: 认知系数:", self.pso_cognitive_spin)
        
        self.pso_social_spin = QDoubleSpinBox()
        self.pso_social_spin.setRange(0.1, 3.0)
        self.pso_social_spin.setValue(1.5)
        self.pso_social_spin.setSingleStep(0.1)
        algorithm_layout.addRow("PSO: 社会系数:", self.pso_social_spin)
        
        # GA参数
        self.ga_crossover_spin = QDoubleSpinBox()
        self.ga_crossover_spin.setRange(0.1, 1.0)
        self.ga_crossover_spin.setValue(0.8)
        self.ga_crossover_spin.setSingleStep(0.05)
        algorithm_layout.addRow("GA: 交叉概率:", self.ga_crossover_spin)
        
        self.ga_mutation_spin = QDoubleSpinBox()
        self.ga_mutation_spin.setRange(0.001, 0.5)
        self.ga_mutation_spin.setValue(0.1)
        self.ga_mutation_spin.setSingleStep(0.01)
        algorithm_layout.addRow("GA: 变异概率:", self.ga_mutation_spin)
        
        # DE参数
        self.de_crossover_spin = QDoubleSpinBox()
        self.de_crossover_spin.setRange(0.1, 1.0)
        self.de_crossover_spin.setValue(0.7)
        self.de_crossover_spin.setSingleStep(0.05)
        algorithm_layout.addRow("DE: 交叉概率:", self.de_crossover_spin)
        
        self.de_f_spin = QDoubleSpinBox()
        self.de_f_spin.setRange(0.1, 2.0)
        self.de_f_spin.setValue(0.8)
        self.de_f_spin.setSingleStep(0.1)
        algorithm_layout.addRow("DE: 缩放因子F:", self.de_f_spin)
        
        advanced_layout.addWidget(algorithm_group)
        
        # 参数约束
        constraints_group = QGroupBox("参数约束")
        constraints_layout = QFormLayout(constraints_group)
        
        self.bounds_check = QCheckBox("使用严格参数边界")
        self.bounds_check.setChecked(True)
        constraints_layout.addRow("", self.bounds_check)
        
        self.penalty_combo = QComboBox()
        self.penalty_combo.addItems([
            "无惩罚", 
            "静态惩罚", 
            "自适应惩罚"
        ])
        constraints_layout.addRow("约束违反惩罚:", self.penalty_combo)
        
        advanced_layout.addWidget(constraints_group)
        
        # 添加选项卡
        tab_widget.addTab(basic_tab, "基本设置")
        tab_widget.addTab(advanced_tab, "高级设置")
        main_layout.addWidget(tab_widget)
        
        # 添加进度显示
        progress_group = QGroupBox("优化进度")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        progress_layout.addWidget(self.progress_bar)
        
        self.status_label = QLabel("就绪")
        progress_layout.addWidget(self.status_label)
        
        main_layout.addWidget(progress_group)
        
        # 添加控制按钮
        control_layout = QHBoxLayout()
        
        self.start_button = QPushButton("开始优化")
        self.start_button.setObjectName("startButton")
        self.start_button.clicked.connect(self.start_optimization)
        control_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("停止优化")
        self.stop_button.setObjectName("stopButton")
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_optimization)
        control_layout.addWidget(self.stop_button)
        
        main_layout.addLayout(control_layout)
        
        # 初始状态设置
        self.update_method_options(0)
    
    def update_method_options(self, index):
        """根据选择的优化方法更新可用选项"""
        is_population_based = index in [0, 1, 2]  # PSO, DE, GA
        is_gradient_based = index == 3  # BFGS
        is_quantum = index == 4  # QA
        is_ai = index == 5  # 多模态科学模型辅助优化
        
        # 更新基本选项
        self.pop_size_spin.setEnabled(is_population_based or is_quantum)
        
        # 更新高级选项可见性
        self.pso_inertia_spin.setEnabled(index == 0)
        self.pso_cognitive_spin.setEnabled(index == 0)
        self.pso_social_spin.setEnabled(index == 0)
        
        self.ga_crossover_spin.setEnabled(index == 2)
        self.ga_mutation_spin.setEnabled(index == 2)
        
        self.de_crossover_spin.setEnabled(index == 1)
        self.de_f_spin.setEnabled(index == 1)
        
        # 量子优化自动选中量子加速
        if is_quantum:
            self.quantum_check.setChecked(True)
        
        # AI辅助优化自动选中AI模型
        if is_ai:
            self.ai_check.setChecked(True)
    
    def start_optimization(self):
        """开始优化过程"""
        # 获取配置参数
        config = self.get_optimization_config()
        
        # 更新UI状态
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("优化中...")
        
        # 发出开始信号
        self.optimization_started.emit(config)
    
    def stop_optimization(self):
        """停止优化过程"""
        # 更新UI状态
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("已停止")
        
        # 发出停止信号
        self.optimization_stopped.emit()
    
    def get_optimization_config(self):
        """获取当前的优化配置"""
        method_index = self.method_combo.currentIndex()
        method_name = self.method_combo.currentText()
        
        config = {
            "method": method_name,
            "method_index": method_index,
            "max_iterations": self.max_iter_spin.value(),
            "tolerance": self.tolerance_spin.value(),
            "threads": self.threads_spin.value(),
            "use_quantum": self.quantum_check.isChecked(),
            "use_ai_model": self.ai_check.isChecked(),
            "strict_bounds": self.bounds_check.isChecked(),
            "penalty_method": self.penalty_combo.currentText()
        }
        
        # 添加特定算法参数
        if method_index == 0:  # PSO
            config.update({
                "population_size": self.pop_size_spin.value(),
                "inertia_weight": self.pso_inertia_spin.value(),
                "cognitive_coef": self.pso_cognitive_spin.value(),
                "social_coef": self.pso_social_spin.value()
            })
        elif method_index == 1:  # DE
            config.update({
                "population_size": self.pop_size_spin.value(),
                "crossover_prob": self.de_crossover_spin.value(),
                "scale_factor": self.de_f_spin.value()
            })
        elif method_index == 2:  # GA
            config.update({
                "population_size": self.pop_size_spin.value(),
                "crossover_prob": self.ga_crossover_spin.value(),
                "mutation_prob": self.ga_mutation_spin.value()
            })
        elif method_index == 4:  # QA
            config.update({
                "population_size": self.pop_size_spin.value()
            })
            
        return config
    
    @pyqtSlot(int)
    def update_progress(self, value):
        """更新进度条和状态"""
        self.progress_bar.setValue(value)
        self.status_label.setText(f"优化中... {value}%")
        
        if value >= 100:
            self.status_label.setText("优化完成")
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False) 