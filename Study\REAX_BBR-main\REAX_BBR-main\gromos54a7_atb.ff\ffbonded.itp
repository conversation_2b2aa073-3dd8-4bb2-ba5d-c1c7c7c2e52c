; Table 2.5.2.1
;       GROMOS bond-stretching parameters
;
;
;	Bond type code
;	Force constant
;	Ideal bond length
;	Examples of usage in terms of non-bonded atom types
;
;
;	ICB(H)[N]    CB[N] B0[N]
;
#define gb_1        0.1000  1.5700e+07
; H  -  OA      750     
;
#define gb_2        0.1000  1.8700e+07
; H  -  N (all) 895     
;
#define gb_3        0.1090  1.2300e+07
; HC  -  C      700     
;
#define gb_4         0.112  3.7000e+07
; C - O (CO in heme)  2220
;
#define gb_5        0.1230  1.6600e+07
; C  - O        1200    
;
#define gb_6        0.1250  1.3400e+07
; C  - OM       1000    
;
#define gb_7        0.1320  1.2000e+07
; CR1  -  NR (6-ring)   1000    
;
#define gb_8        0.1330  8.8700e+06
; H  -  S       750     
;
#define gb_9        0.1330  1.0600e+07
; C  -  NT, NL  900     
;
#define gb_10       0.1330  1.1800e+07
; C, CR1  -  N, NR, CR1, C (peptide, 5-ring)       1000    
;
#define gb_11       0.1340  1.0500e+07
; C  -  <PERSON>, NZ, NE       900     
;
#define gb_12       0.1340  1.1700e+07
; C  -  NR (no H) (6-ring)      1000    
;
#define gb_13       0.1360  1.0200e+07
; C  -  OA      900     
;
#define gb_14       0.1380  1.1000e+07
; C  -  NR (heme)       1000    
;
#define gb_15       0.1390  8.6600e+06
; CH2  -  C, CR1 (6-ring)       800     
;
#define gb_16       0.1390  1.0800e+07
; C, CR1  -  CH2, C, CR1 (6-ring)       1000    
;
#define gb_17       0.1400  8.5400e+06
; C, CR1, CH2  -  NR (6-ring)   800     
;
#define gb_18       0.1430  8.1800e+06
; CHn  -  OA    800     
;
#define gb_19       0.1430  9.2100e+06
; CHn  -  OM    900     
;
#define gb_20       0.1435  6.1000e+06
; CHn  -  OA (sugar)    600     
;
#define gb_21       0.1470  8.7100e+06
; CHn  -  N, NT, NL, NZ, NE     900     
;
#define gb_22       0.1480  5.7300e+06
; CHn  -  NR (5-ring)   600     
;
#define gb_23       0.1480  7.6400e+06
; CHn  -   NR (6-ring)  800     
;
#define gb_24       0.1480  8.6000e+06
; O, OM  -   P     900     
;
#define gb_25       0.1500  8.3700e+06
; O  -  S       900     
;
#define gb_26       0.1520  5.4300e+06
; CHn  -   CHn (sugar)  600     
;
#define gb_27       0.1530  7.1500e+06
; C, CHn  -   C, CHn    800     
;
#define gb_28       0.1610  4.8400e+06
; OA  -   P     600     
;
#define gb_29       0.1630  4.7200e+06
; OA  -   SI    600     
;
#define gb_30       0.1780  2.7200e+06
; FE  -  C (Heme)
;
#define gb_31       0.1780  5.9400e+06
; CH3  -   S    900     
;
#define gb_32       0.1830  5.6200e+06
; CH2  -   S    900     
;
#define gb_33       0.1870  3.5900e+06
; CH1  -   SI   600     
;
#define gb_34        0.198  0.6400e+06
; NR  -   FE    120    
;
#define gb_35        0.200  0.6280e+06
; NR (heme)  -  FE   120
;
#define gb_36       0.2040  5.0300e+06
; S  -   S      1000    
;
#define gb_37        0.221  0.5400e+06
; NR  -  FE     126
;
#define gb_38       0.1000  2.3200e+07
; HWat  -   OWat        1110    
;
#define gb_39       0.1100  1.2100e+07
; HChl  -   CChl        700     
;
#define gb_40       0.1758  8.1200e+06
; CChl  -   CLChl       1200    
;
#define gb_41       0.1530  8.0400e+06
; ODmso  -   SDmso      900     
;
#define gb_42     0.193799  4.9500e+06
; SDmso  -   CDmso      890     
;
#define gb_43       0.1760  8.1000e+06
; CCl4  -   CLCl4       1200    
;
#define gb_44       0.1265  1.3100e+07
; CUrea  -  OUrea       1000
;
#define gb_45        0.135  1.0300e+07
; CUrea  -  NUrea       900
;
#define gb_46     0.163299  8.7100e+06
; HWat  -   HWat        1110    
;
#define gb_47     0.233839  2.6800e+06
; HChl  -   CLChl        700    
;
#define gb_48     0.290283  2.9800e+06
; CLChl -   CLChl       1200    
;
#define gb_49     0.279388  2.3900e+06
; ODmso -   CDmso        890    
;
#define gb_50     0.291189  2.1900e+06
; CDmso -   CDmso        890    
;
#define gb_51       0.2077  3.9700e+06
; HMet  -   CMet         820    
;
#define gb_52     0.287407  3.0400e+06
; CLCl4 -   CLCl4       1200    
;
;---
;       Table 2.5.3.1.
;       GROMOS bond-angle bending parameters
; 
; 
; Bond-angle type code
; Force constant
; Ideal bond angle
; Example of usage in terms of non-bonded atom types
; 
; 
;  ICT(H)[N]  CT[N]  (T0[N])
;
#define ga_1         90.00      380.00
; NR(heme)  -  FE  -  C          90
;
#define ga_2         90.00      420.00
; NR(heme)  -  FE  -  NR(heme)  100     
;
#define ga_3         96.00      405.00
; H  -  S  -  CH2       95      
;
#define ga_4        100.00      475.00
; CH2  -  S  -  CH3     110     
;
#define ga_5        103.00      420.00
; OA  -  P  -  OA       95      
;
#define ga_6        104.00      490.00
; CH2  -  S  -  S       110     
;
#define ga_7        108.00      465.00
; NR, C, CR1(5-ring)    100     
;
#define ga_8        109.50      285.00
; CHn  - CHn - CHn, NR(6-ring) (sugar)  60      
;
#define ga_9        109.50      320.00
; CHn, OA  - CHn  - OA, NR(ring) (sugar)        68      
;
#define ga_10       109.50      380.00
; H -  NL, NT  -  H, CHn  - OA  - CHn(sugar)    80      
;
#define ga_11       109.50      425.00
; H  -  NL  -  C, CHn          H  -  NT  -  CHn 90      
;
#define ga_12       109.50      450.00
; X  -  OA, SI  -  X    95      
;
#define ga_13       109.50      520.00
; CHn,C  -  CHn  -  C, CHn, OA, OM, N, NE       110     
;
#define ga_14       109.60      450.00
; OM  -  P  -  OA       95      
;
#define ga_15       111.00      530.00
; CHn  -  CHn  -  C, CHn, OA, NR, NT, NL        110     
;
#define ga_16       113.00      545.00
; CHn  -  CH2  -  S     110     
;
#define ga_17       115.00       50.00
; NR(heme)  -  FE  - NR 10      
;
#define ga_18       115.00      460.00
; H  -  N  -  CHn       90      
;
#define ga_19       115.00      610.00
; CHn, C  -  C  -  OA, N, NT, NL        120     
;
#define ga_20       116.00      465.00
; H  -  NE  -  CH2      90      
;
#define ga_21       116.00      620.00
; CH2  -  N  -  CH1     120     
;
#define ga_22       117.00      635.00
; CH3 -  N  -  C, CHn  - C  - OM        120     
;
#define ga_23       120.00      390.00
; H  -  NT, NZ, NE  -  C        70      
;
#define ga_24       120.00      445.00
; H  -  NT, NZ  -  H    80      
;
#define ga_25       120.00      505.00
; H - N - CH3, H, HC - 6-ring, H - NT - CHn     90      
;
#define ga_26       120.00      530.00
; P, SI  -  OA  -  CHn, P       95      
;
#define ga_27       120.00      560.00
; N, C, CR1 (6-ring, no H)      100     
;
#define ga_28       120.00      670.00
; NZ  -  C  -  NZ, NE   120     
;
#define ga_29       120.00      780.00
; OM  - P  -  OM        140     
;
#define ga_30       121.00      685.00
; O  -  C  -  CHn, C          CH3  -  N  -  CHn 120     
;
#define ga_31       122.00      700.00
; CH1, CH2  -  N  -  C  120     
;
#define ga_32       123.00      415.00
; H  - N  - C   70      
;
#define ga_33       124.00      730.00
; O  - C  - OA, N, NT, NL   C - NE - CH2        120     
;
#define ga_34       125.00      375.00
; FE  - NR  - CR1 (5-ring)      60      
;
#define ga_35       125.00      750.00
; -     120     
;
#define ga_36       126.00      575.00
; H, HC  - 5-ring       90      
;
#define ga_37       126.00      640.00
; X(noH)  - 5-ring      100     
;
#define ga_38       126.00      770.00
; OM  - C  - OM 120     
;
#define ga_39       132.00      760.00
; 5, 6 ring connnection 100     
;
#define ga_40       155.00     2215.00
; SI  - OA  - SI        95      
;
#define ga_41       180.00    91350.00
; Fe  -  C  -  O (heme) 57
;
#define ga_42       109.50      434.00
; HWat  - OWat  - HWat  92      
;
#define ga_43       107.57      484.00
; HChl  - CChl  - CLChl 105     
;
#define ga_44       111.30      632.00
; CLChl  - CChl  - CLChl        131     
;
#define ga_45        97.40      469.00
; CDmso  - SDmso  - CDmso       110     
;
#define ga_46       106.75      503.00
; CDmso  - SDmso  -  ODmso      110     
;
#define ga_47       108.53      443.00
; HMet  - OMet  - CMet  95      
;
#define ga_48       109.50      618.00
; CLCl4  - CCl4  - CLCl4        131     
;
#define ga_49       107.60      507.00
; FTFE  -  CTFE  -  FTFE        100
;
#define ga_50       109.50      448.00
; HTFE  -  OTFE  -  CHTFE        85
;
#define ga_51        110.3      524.00
; OTFE  -  CHTFE  -  CTFE        97
;
#define ga_52        111.4      532.00
; CHTFE  -  CTFE  -  FTFE        95
;
#define ga_53        117.2      636.00
; NUrea  -  CUrea  -  NUrea     120
;
#define ga_54        121.4      690.00
; OUrea  -  CUrea  -  NUrea     120
;
;       Table 2.5.4.1
;       GROMOS improper (harmonic) dihedral angle parameters
; 
; 
; Improper dihedral-angle type code
; Force constant
; Ideal improper dihedral angle
; Example of usage
; 
; 
; ICQ(H)[N] CQ[N] (Q0[N])
;
#define gi_1           0.0   167.42309
; planar groups 40      
;
#define gi_2      35.26439   334.84617
; tetrahedral centres   80      
;
#define gi_3           0.0   669.69235
; heme iron     160     
;
#define gi_4         180.0   167.42309
; planar groups 40      
;
#define gi_5      -35.26439   334.84617
; tetrahedral centres   80      
;
;       Table ******* (Note: changes with respect to the 43A1 table)
;       GROMOS (trigonometric) dihedral torsional angle parameters
; 
; 
; Dihedral-angle type code
; Force constant
; Phase shift
; Multiplicity
; Example of usage in terms of non-bonded atom types
; 
; 
; ICP(H)[N]  CP[N] PD[N] NP[N]
; 
#define gd_1    180.000       2.67          1
; CHn-CHn-CHn-OA (sugar)  0.6
;
#define gd_2    180.000       3.41          1
; OA-CHn-OA-CHn,H (beta sugar) 0.8
;
#define gd_3    180.000       4.97          1
; OA-CHn-CHn-OA (sugar)	1.2
;
#define gd_4    180.000       5.86          1
; N-CHn-CHn-OA (lipid)	1.4
;
#define gd_5    180.000       9.35          1
; OA-CHn-CHn-OA (sugar)	2.2
;
#define gd_6    180.000       9.45          1
; OA-CHn-OA-CHn,H (alpha sugar)  2.3
;
#define gd_7      0.000       2.79          1
; P-O5*-C5*-C4* (dna)	0.7
;
#define gd_8      0.000       5.35          1
; O5*-C5*-C4*-O4* (dna)	1.3
;
#define gd_9    180.000       1.53          2
; C1-C2-CAB-CBB	(heme)	0.4
;
#define gd_10   180.000       5.86          2
; -C-C-	1.4
;
#define gd_11   180.000       7.11          2
; -C-OA,OE- (at ring)	1.7
;
#define gd_12   180.000       16.7          2
; -C-OA,OE- (carboxyl)	4.0
;
#define gd_13   180.000       24.0          2
; CHn-OE-C-CHn (ester lipid)	5.7
;
#define gd_14   180.000       33.5          2
; -C-N,NT,NE,NZ,NR-	8.0
;
#define gd_15   180.000       41.8          2
; -C-CR1- (6-ring)	10.0
;
#define gd_16     0.000        0.0          2
; -CH1(sugar)-NR(base)	0.0
;
#define gd_17     0.000      0.418          2
; O-CH1-CHn-no O	0.1
;
#define gd_18     0.000       2.09          2
; O-CH1-CHn-O	0.5
;
#define gd_19     0.000       3.14          2
; -OA-P-	0.75
;
#define gd_20     0.000       5.09          2
; O-P-O- (dna, lipids)	1.2
;
#define gd_21     0.000       16.7          2
; -S-S-	4.0
;
#define gd_22     0.000       1.05          3
; -OA-P-	0.25
;
#define gd_23     0.000       1.26          3
; -CHn-OA(no sugar)- 0.3
;
#define gd_24     0.000       1.30          3
; HTFE-OTFE-CHTFE-CTFE	0.3
;
#define gd_25     0.000       2.53          3
; O5*-C5*-C4*-O4* (dna)	0.6
;
#define gd_26     0.000       2.93          3
; -CH2-S-	0.7
;
#define gd_27     0.000       3.19          3
; O-P-O- (dna, lipids)	0.8
;
#define gd_28     0.000       3.65          3
; OA-CHn-OA-CHn,H (alpha sugar)	0.9
;
#define gd_29     0.000       3.77          3
; -C,CHn,SI-	0.9
;
#define gd_30     0.000       3.90          3
; CHn-CHn-OA-H (sugar)	0.9
;
#define gd_31     0.000       4.18          3
; HC-C-S-	1.0
;
#define gd_32     0.000       4.69          3
; AO-CHn-OA-CHn,H (beta sugar)
;
#define gd_33     0.000       5.44          3
; HC-C-C-	1.3
;
#define gd_34     0.000       5.92          3
; -CHn,SI-CHn-	1.4
;
#define gd_35     0.000       7.69          3
; OA-CHn-CHn-OA (sugar)	1.8
;
#define gd_36     0.000       8.62          3
; N-CHn-CHn-OA (lipid)	2.1
;
#define gd_37     0.000       9.50          3
; OA-CHn-CHn-OA (sugar)	2.3
;
#define gd_38     0.000        0.0          4
; -NR-FE-	0.0
;
#define gd_39   180.000        1.0          6
; -CHn-N,NE-	0.24
;
#define gd_40     0.000        1.0          6
; -CHn-C,NR(ring), CR1-	0.24
;
#define gd_41     0.000       3.77          6
; -CHn-NT-	0.9
;
#define gd_42    180.000        3.5         2
; Backbone dihedral angle -N-CA-C-N-    0.84
;
#define gd_43     0.000        2.8          3
; Backbone dihedral angle -C-N-CA-C-    0.67
;
#define gd_44   180.000        0.7          6
;  Backbone dihedral angle -C-N-CA-C-   0.17
;
#define gd_45     0.000        0.4          6
;  Backbone dihedral angle -N-CA-C-N-   0.096
;
; get the constraint distances for dummy atom constructions
 
#include "ff_dum.itp"
 
[ constrainttypes ]
; now the constraints for the rigid NH3 groups
 MNH3    C    2   DC_MNC1
 MNH3  CH1    2   DC_MNC2
 MNH3  CH2    2   DC_MNC2
 MNH3 MNH3    2   DC_MNMN
; and the angle-constraints for OH and SH groups in proteins:
  CH2    H    2   DC_CO
  CH1    H    2   DC_CO
    C    H    2   DC_CO
    P    H    2   DC_PO
                                                                             
; bond-, angle- and dihedraltypes for specbonds:
[ bondtypes ]
S      S       2    gb_36
NR     FE      2    gb_34

[ angletypes ]
CH1    CH2    S     2   ga_16
CH2    S      S     2   ga_6
CR1    NR    FE     2	ga_34
NR     FE    NR     2   ga_17

[ dihedraltypes ]
S      S      1   gd_21
NR     FE     1   gd_38
CH2    S      1   gd_26
