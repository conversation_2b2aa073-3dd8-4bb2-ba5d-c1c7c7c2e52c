Siesta Version  : v4.1-b4
Architecture    : GNU
Compiler version: GNU Fortran (Uos 8.3.0.3-3+rebuild) 8.3.0
Compiler flags  : mpif90 -O2 -fPIC -ftree-vectorize
PP flags        : -DMPI  -DFC_HAVE_ABORT -DGRID_DP -DPHI_GRID_SP -DSIESTA__DIAG_2STAGE
Libraries       : libsiestaLAPACK.a libsiestaLAPACK.a -lpthread -L/home/<USER>/siesta/mathlib/scaalapack -lscalapack -L/home/<USER>/siesta/mathlib/BLACS/LIB -lblacsF77init -lblacsCinit -lblacs  -L/home/<USER>/siesta/mathlib/lapack -lrefblas
PARALLEL version

* Running on 8 nodes in parallel
>> Start of run:  29-MAR-2022  11:25:09

                           ***********************       
                           *  WELCOME TO SIESTA  *       
                           ***********************       

reinit: Reading from standard input
reinit: Dumped input in INPUT_TMP.56321
************************** Dump of input data file ****************************
################## Species and atoms ##################
SystemName       siesta
SystemLabel      siesta
NumberOfSpecies   1
NumberOfAtoms     4
%block ChemicalSpeciesLabel
1 1 H
%endblock ChemicalSpeciesLabel
SolutionMethod   Diagon # ## OrderN or Diagon
MaxSCFIterations 500
PAO.BasisType    split
%block PAO.Basis
H     2
 n=1    0    2  S .7020340
   4.4302740  0.0
   1.000   1.000
 n=2    1    1
   4.7841521
   1.000
%endblock PAO.Basis
SpinPolarized    F
DM.MixingWeight      0.4
DM.NumberPulay       9
DM.Tolerance         1.d-4
################### FUNCTIONAL ###################
XC.functional    GGA    # Exchange-correlation functional type
XC.Authors       PBE    # Particular parametrization of xc func
MeshCutoff       200. Ry # Equivalent planewave cutoff for the grid
KgridCutoff      12.000000 Ang
WriteCoorInitial T
WriteCoorXmol    T
WriteMDhistory   T
WriteMullikenPop 1
WriteForces      T
###################  GEOMETRY  ###################
LatticeConstant  1.00 Ang
%block LatticeVectors
12.0 0.0 0.0
0.0 12.0 0.0
0.0 0.0 12.0
%endblock LatticeVectors
AtomicCoordinatesFormat Ang
%block AtomicCoordinatesAndAtomicSpecies
3.917083774935344 3.69373315051698 4.12065573346065 1
4.414896644052805 3.520936319033012 4.593231714151679 1
1.5272302974573169 4.389944011723035 2.446173387879595 1
1.0657249670679318 4.519232132337925 1.9308927458173581 1
%endblock AtomicCoordinatesAndAtomicSpecies
************************** End of input data file *****************************

reinit: -----------------------------------------------------------------------
reinit: System Name: siesta
reinit: -----------------------------------------------------------------------
reinit: System Label: siesta
reinit: -----------------------------------------------------------------------

initatom: Reading input for the pseudopotentials and atomic orbitals ----------
Species number:   1 Atomic number:    1 Label: H

Ground state valence configuration:   1s01
Reading pseudopotential information in formatted form from H.psf

Valence configuration for pseudopotential generation:
1s( 1.00) rc: 1.25
2p( 0.00) rc: 1.25
3d( 0.00) rc: 1.25
4f( 0.00) rc: 1.25
For H, standard SIESTA heuristics set lmxkb to 2
 (one more than the basis l, including polarization orbitals).
Use PS.lmax or PS.KBprojectors blocks to override.

<basis_specs>
===============================================================================
H                    Z=   1    Mass=  1.0100        Charge= 0.17977+309
Lmxo=1 Lmxkb= 2    BasisType=split      Semic=F
L=0  Nsemic=0  Cnfigmx=1
          n=1  nzeta=2  polorb=0
            splnorm:   0.70203    
               vcte:    0.0000    
               rinn:    0.0000    
               qcoe:    0.0000    
               qyuk:    0.0000    
               qwid:   0.10000E-01
                rcs:    4.4303      0.0000    
            lambdas:    1.0000      1.0000    
L=1  Nsemic=0  Cnfigmx=2
          n=1  nzeta=1  polorb=0
            splnorm:   0.15000    
               vcte:    0.0000    
               rinn:    0.0000    
               qcoe:    0.0000    
               qyuk:    0.0000    
               qwid:   0.10000E-01
                rcs:    4.7842    
            lambdas:    1.0000    
-------------------------------------------------------------------------------
L=0  Nkbl=1  erefs: 0.17977+309
L=1  Nkbl=1  erefs: 0.17977+309
L=2  Nkbl=1  erefs: 0.17977+309
===============================================================================
</basis_specs>

atom: Called for H                     (Z =   1)

read_vps: Pseudopotential generation method:
read_vps: ATM3.3    Troullier-Martins                       
Total valence charge:    1.00000

xc_check: Exchange-correlation functional:
xc_check: GGA Perdew, Burke & Ernzerhof 1996
V l=0 = -2*Zval/r beyond r=  1.2343
V l=1 = -2*Zval/r beyond r=  1.2189
V l=2 = -2*Zval/r beyond r=  1.2189
All V_l potentials equal beyond r=  1.2343
This should be close to max(r_c) in ps generation
All pots = -2*Zval/r beyond r=  1.2343

VLOCAL1: 99.0% of the norm of Vloc inside     28.493 Ry
VLOCAL1: 99.9% of the norm of Vloc inside     64.935 Ry
atom: Maximum radius for 4*pi*r*r*local-pseudopot. charge    1.45251
atom: Maximum radius for r*vlocal+2*Zval:    1.21892
GHOST: No ghost state for L =  0
GHOST: No ghost state for L =  1
GHOST: No ghost state for L =  2

KBgen: Kleinman-Bylander projectors: 
   l= 0   rc=  1.364359   el= -0.477200   Ekb= -2.021939   kbcos= -0.344793
   l= 1   rc=  1.434438   el=  0.001076   Ekb= -0.443447   kbcos= -0.022843
   l= 2   rc=  1.470814   el=  0.002010   Ekb= -0.140543   kbcos= -0.002863

KBgen: Total number of  Kleinman-Bylander projectors:    9
atom: -------------------------------------------------------------------------

atom: SANKEY-TYPE ORBITALS:
atom: Selected multiple-zeta basis: split     

SPLIT: Orbitals with angular momentum L= 0

SPLIT: Basis orbitals for state 1s

   izeta = 1
                 lambda =    1.000000
                     rc =    4.479210
                 energy =   -0.451136
                kinetic =    1.010721
    potential(screened) =   -1.461857
       potential(ionic) =   -1.992374

   izeta = 2
                 rmatch =    1.797005
              splitnorm =    0.702034
                 energy =    1.204812
                kinetic =    4.569535
    potential(screened) =   -3.364723
       potential(ionic) =   -3.917241

SPLIT: Orbitals with angular momentum L= 1

SPLIT: Basis orbitals for state 2p

   izeta = 1
                 lambda =    1.000000
                     rc =    4.828263
                 energy =    0.494150
                kinetic =    0.904558
    potential(screened) =   -0.410408
       potential(ionic) =   -0.853691
atom: Total number of Sankey-type orbitals:  5

atm_pop: Valence configuration (for local Pseudopot. screening):
 1s( 1.00)                                                            
 2p( 0.00)                                                            
Vna: chval, zval:    1.00000   1.00000

Vna:  Cut-off radius for the neutral-atom potential:   4.479210

atom: _________________________________________________________________________

prinput: Basis input ----------------------------------------------------------

PAO.BasisType split     

%block ChemicalSpeciesLabel
    1    1 H                       # Species index, atomic number, species label
%endblock ChemicalSpeciesLabel

%block PAO.Basis                 # Define Basis set
H                     2                    # Species label, number of l-shells
 n=1   0   2                         # n, l, Nzeta 
   4.479      1.797   
   1.000      1.000   
 n=2   1   1                         # n, l, Nzeta 
   4.828   
   1.000   
%endblock PAO.Basis

prinput: ----------------------------------------------------------------------

coor:   Atomic-coordinates input format  =     Cartesian coordinates
coor:                                          (in Angstroms)

siesta: Atomic coordinates (Bohr) and species
siesta:      7.40222   6.98015   7.78691  1        1
siesta:      8.34295   6.65361   8.67995  1        2
siesta:      2.88605   8.29580   4.62260  1        3
siesta:      2.01393   8.54011   3.64886  1        4

siesta: System type = molecule  

initatomlists: Number of atoms, orbitals, and projectors:      4    20    36

coxmol: Writing XMOL coordinates into file siesta.xyz                                                          

siesta: ******************** Simulation parameters ****************************
siesta:
siesta: The following are some of the parameters of the simulation.
siesta: A complete list of the parameters used, including default values,
siesta: can be found in file out.fdf
siesta:
redata: Spin configuration                          = none
redata: Number of spin components                   = 1
redata: Time-Reversal Symmetry                      = T
redata: Spin-spiral                                 = F
redata: Long output                                 =   F
redata: Number of Atomic Species                    =        1
redata: Charge density info will appear in .RHO file
redata: Write Mulliken Pop.                         = Atomic and Orbital charges
redata: Matel table size (NRTAB)                    =     1024
redata: Mesh Cutoff                                 =   200.0000 Ry
redata: Net charge of the system                    =     0.0000 |e|
redata: Min. number of SCF Iter                     =        0
redata: Max. number of SCF Iter                     =      500
redata: SCF convergence failure will abort job
redata: SCF mix quantity                            = Hamiltonian
redata: Mix DM or H after convergence               =   F
redata: Recompute H after scf cycle                 =   F
redata: Mix DM in first SCF step                    =   T
redata: Write Pulay info on disk                    =   F
redata: New DM Mixing Weight                        =     0.4000
redata: New DM Occupancy tolerance                  = 0.000000000001
redata: No kicks to SCF
redata: DM Mixing Weight for Kicks                  =     0.5000
redata: Require Harris convergence for SCF          =   F
redata: Harris energy tolerance for SCF             =     0.000100 eV
redata: Require DM convergence for SCF              =   T
redata: DM tolerance for SCF                        =     0.000100
redata: Require EDM convergence for SCF             =   F
redata: EDM tolerance for SCF                       =     0.001000 eV
redata: Require H convergence for SCF               =   T
redata: Hamiltonian tolerance for SCF               =     0.001000 eV
redata: Require (free) Energy convergence for SCF   =   F
redata: (free) Energy tolerance for SCF             =     0.000100 eV
redata: Using Saved Data (generic)                  =   F
redata: Use continuation files for DM               =   F
redata: Neglect nonoverlap interactions             =   F
redata: Method of Calculation                       = Diagonalization
redata: Electronic Temperature                      =   299.9869 K
redata: Fix the spin of the system                  =   F
redata: Dynamics option                             = Single-point calculation
mix.SCF: Pulay mixing                            = Pulay
mix.SCF:    Variant                              = stable
mix.SCF:    History steps                        = 9
mix.SCF:    Linear mixing weight                 =     0.400000
mix.SCF:    Mixing weight                        =     0.400000
mix.SCF:    SVD condition                        = 0.1000E-07
redata: ***********************************************************************

%block SCF.Mixers
  Pulay
%endblock SCF.Mixers

%block SCF.Mixer.Pulay
  # Mixing method
  method pulay
  variant stable

  # Mixing options
  weight 0.4000
  weight.linear 0.4000
  history 9
%endblock SCF.Mixer.Pulay

DM_history_depth set to one: no extrapolation allowed by default for geometry relaxation
Size of DM history Fstack: 1
Total number of electrons:     4.000000
Total ionic charge:     4.000000

* ProcessorY, Blocksize:    2   2


* Orbital distribution balance (max,min):     4     2

 Kpoints in:           18 . Kpoints trimmed:           14

siesta: k-grid: Number of k-points =    14
siesta: k-grid: Cutoff (effective) =    18.000 Ang
siesta: k-grid: Supercell and displacements
siesta: k-grid:    0   3   0      0.000
siesta: k-grid:    0   0   3      0.000
siesta: k-grid:    3   0   0      0.000

diag: Algorithm                                     = D&C
diag: Parallel over k                               =   F
diag: Use parallel 2D distribution                  =   T
diag: Parallel block-size                           = 2
diag: Parallel distribution                         =     2 x     4
diag: Used triangular part                          = Lower
diag: Absolute tolerance                            =  0.100E-15
diag: Orthogonalization factor                      =  0.100E-05
diag: Memory factor                                 =  1.0000


ts: **************************************************************
ts: Save H and S matrices                           =    F
ts: Save DM and EDM matrices                        =    F
ts: Fix Hartree potential                           =    F
ts: Only save the overlap matrix S                  =    F
ts: **************************************************************

************************ Begin: TS CHECKS AND WARNINGS ************************
************************ End: TS CHECKS AND WARNINGS **************************


                     ====================================
                        Single-point calculation
                     ====================================

outcell: Unit cell vectors (Ang):
       12.000000    0.000000    0.000000
        0.000000   12.000000    0.000000
        0.000000    0.000000   12.000000

outcell: Cell vector modules (Ang)   :   12.000000   12.000000   12.000000
outcell: Cell angles (23,13,12) (deg):     90.0000     90.0000     90.0000
outcell: Cell volume (Ang**3)        :   1728.0000
<dSpData1D:S at geom step 0
  <sparsity:sparsity for geom step 0
    nrows_g=20 nrows=4 sparsity=.1650 nnzs=66, refcount: 7>
  <dData1D:(new from dSpData1D) n=66, refcount: 1>
refcount: 1>
new_DM -- step:     1
Initializing Density Matrix...
DM filled with atomic data:
<dSpData2D:DM initialized from atoms
  <sparsity:sparsity for geom step 0
    nrows_g=20 nrows=4 sparsity=.1650 nnzs=66, refcount: 8>
  <dData2D:DM n=66 m=1, refcount: 1>
refcount: 1>
No. of atoms with KB's overlaping orbs in proc 0. Max # of overlaps:       4      14
New grid distribution:   1
           1       1:   54    1:   27    1:   14
           2       1:   54    1:   27   15:   28
           3       1:   54    1:   27   29:   41
           4       1:   54    1:   27   42:   54
           5       1:   54   28:   54    1:   14
           6       1:   54   28:   54   15:   28
           7       1:   54   28:   54   29:   41
           8       1:   54   28:   54   42:   54

InitMesh: MESH =   108 x   108 x   108 =     1259712
InitMesh: (bp) =    54 x    54 x    54 =      157464
InitMesh: Mesh cutoff (required, used) =   200.000   223.865 Ry
ExtMesh (bp) on 0 =   102 x    75 x    62 =      474300
New grid distribution:   2
           1      13:   54    1:   19    1:   16
           2       1:   11   20:   54    1:   16
           3      19:   54    1:   18   17:   54
           4       1:   17   19:   54   17:   54
           5      12:   54   20:   54    1:   16
           6       1:   18    1:   18   17:   54
           7      18:   54   19:   54   17:   54
           8       1:   12    1:   19    1:   16
New grid distribution:   3
           1      14:   54    1:   20    1:   16
           2       1:   11   21:   54    1:   16
           3       1:   17   18:   54   17:   54
           4      20:   54    1:   17   17:   54
           5      12:   54   21:   54    1:   16
           6       1:   19    1:   17   17:   54
           7      18:   54   18:   54   17:   54
           8       1:   13    1:   20    1:   16
Setting up quadratic distribution...
ExtMesh (bp) on 0 =    90 x    67 x    64 =      385920
PhiOnMesh: Number of (b)points on node 0 =                12768
PhiOnMesh: nlist on node 0 =                14382

stepf: Fermi-Dirac step function

siesta: Program's energy decomposition (eV):
siesta: Ebs     =       -41.232333
siesta: Eions   =        78.829286
siesta: Ena     =        21.981973
siesta: Ekin    =        64.839867
siesta: Enl     =       -23.991197
siesta: Eso     =         0.000000
siesta: Eldau   =         0.000000
siesta: DEna    =        -9.203001
siesta: DUscf   =         1.164971
siesta: DUext   =         0.000000
siesta: Enegf   =         0.000000
siesta: Exc     =       -38.434615
siesta: eta*DQ  =         0.000000
siesta: Emadel  =         0.000000
siesta: Emeta   =         0.000000
siesta: Emolmec =         0.000000
siesta: Ekinion =         0.000000
siesta: Eharris =       -72.199410
siesta: Etot    =       -62.471289
siesta: FreeEng =       -62.471289

        iscf     Eharris(eV)        E_KS(eV)     FreeEng(eV)     dDmax    Ef(eV) dHmax(eV)
   scf:    1      -72.199410      -62.471289      -62.471289  0.621030  0.025874  2.932680
timer: Routine,Calls,Time,% = IterSCF        1       0.667  59.87
   scf:    2      -62.577946      -62.525914      -62.525914  0.014430 -5.483050  1.550245
   scf:    3      -62.565414      -62.547326      -62.547326  0.016360 -4.502558  0.006886
   scf:    4      -62.547330      -62.547329      -62.547329  0.000425 -4.504306  0.003047
   scf:    5      -62.547329      -62.547329      -62.547329  0.000116 -4.502119  0.001208
   scf:    6      -62.547329      -62.547329      -62.547329  0.000016 -4.502642  0.000062

SCF Convergence by DM+H criterion
max |DM_out - DM_in|         :     0.0000162034
max |H_out - H_in|      (eV) :     0.0000622883
SCF cycle converged after 6 iterations

Using DM_out to compute the final energy and forces
No. of atoms with KB's overlaping orbs in proc 0. Max # of overlaps:       4      14

siesta: E_KS(eV) =              -62.5473

siesta: E_KS - E_eggbox =       -62.5473

siesta: Atomic forces (eV/Ang):
     1   -1.000403    0.347625   -0.950037
     2    0.994656   -0.344746    0.945338
     3    1.049697   -0.294551    1.171790
     4   -1.045738    0.292548   -1.167243
----------------------------------------
   Tot   -0.001787    0.000875   -0.000152
----------------------------------------
   Max    1.171790
   Res    0.872229    sqrt( Sum f_i^2 / 3N )
----------------------------------------
   Max    1.171790    constrained

Stress-tensor-Voigt (kbar):       -0.89       -0.09       -0.96        0.28        0.29       -0.93
(Free)E + p*V (eV/cell)      -61.8476
Target enthalpy (eV/cell)      -62.5473

mulliken: Atomic and Orbital Populations:

Species: H                   
Atom  Qatom  Qorb
               1s      1s      2py     2pz     2px     
   1  1.020   0.880   0.129   0.001   0.004   0.006
   2  0.980   0.840   0.135   0.000   0.002   0.002
   3  1.020   0.878   0.130   0.000   0.005   0.006
   4  0.980   0.839   0.137   0.000   0.002   0.002

mulliken: Qtot =        4.000

coxmol: Writing XMOL coordinates into file siesta.xyz                                                          

siesta: Program's energy decomposition (eV):
siesta: Ebs     =       -42.634362
siesta: Eions   =        78.829286
siesta: Ena     =        21.981973
siesta: Ekin    =        61.679049
siesta: Enl     =       -22.818666
siesta: Eso     =         0.000000
siesta: Eldau   =         0.000000
siesta: DEna    =        -7.819287
siesta: DUscf   =         0.858563
siesta: DUext   =         0.000000
siesta: Enegf   =         0.000000
siesta: Exc     =       -37.599674
siesta: eta*DQ  =         0.000000
siesta: Emadel  =         0.000000
siesta: Emeta   =         0.000000
siesta: Emolmec =         0.000000
siesta: Ekinion =         0.000000
siesta: Eharris =       -62.547329
siesta: Etot    =       -62.547329
siesta: FreeEng =       -62.547329

siesta: Final energy (eV):
siesta:  Band Struct. =     -42.634362
siesta:       Kinetic =      61.679049
siesta:       Hartree =      61.165577
siesta:       Eldau   =       0.000000
siesta:       Eso     =       0.000000
siesta:    Ext. field =       0.000000
siesta:       Enegf   =       0.000000
siesta:   Exch.-corr. =     -37.599674
siesta:  Ion-electron =    -178.384605
siesta:       Ion-ion =      30.592325
siesta:       Ekinion =       0.000000
siesta:         Total =     -62.547329
siesta:         Fermi =      -4.502642

siesta: Atomic forces (eV/Ang):
siesta:      1   -1.000403    0.347625   -0.950037
siesta:      2    0.994656   -0.344746    0.945338
siesta:      3    1.049697   -0.294551    1.171790
siesta:      4   -1.045738    0.292548   -1.167243
siesta: ----------------------------------------
siesta:    Tot   -0.001787    0.000875   -0.000152

siesta: Stress tensor (static) (eV/Ang**3):
siesta:    -0.000557    0.000175   -0.000577
siesta:     0.000175   -0.000056    0.000180
siesta:    -0.000577    0.000180   -0.000602

siesta: Cell volume =       1728.000000 Ang**3

siesta: Pressure (static):
siesta:                Solid            Molecule  Units
siesta:           0.00000441         -0.00000001  Ry/Bohr**3
siesta:           0.00040493         -0.00000061  eV/Ang**3
siesta:           0.64878175         -0.00098060  kBar
(Free)E+ p_basis*V_orbitals  =         -61.901964
(Free)Eharris+ p_basis*V_orbitals  =         -61.901964

siesta: Electric dipole (a.u.)  =    0.000110   -0.000214   -0.000346
siesta: Electric dipole (Debye) =    0.000279   -0.000545   -0.000880
>> End of run:  29-MAR-2022  11:25:13
Job completed
