[bumpversion]
current_version = 1.0.1.9
commit = True
tag = True

[bumpversion:file:setup.py]
search = version='{current_version}'
replace = version='{new_version}'

[bumpversion:file:parametrization_clean/__init__.py]
search = __version__ = '{current_version}'
replace = __version__ = '{new_version}'

[bdist_wheel]
universal = 1

[flake8]
ignore = D203, F401
exclude = .git, venv*, docs, test*
max-complexity = 10
max-line-length = 120

[aliases]
# Define setup.py command aliases here
test = pytest

[tool:pytest]
collect_ignore = ['setup.py']

