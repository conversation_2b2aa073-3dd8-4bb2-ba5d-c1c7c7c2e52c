#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多尺度图神经网络(MS-GNN)模型 - 同时捕捉局部和全局原子相互作用

该模型使用多尺度图神经网络架构，整合不同尺度的原子相互作用信息：
1. 局部尺度：捕捉化学键和局部电子结构
2. 中间尺度：捕捉分子片段和官能团
3. 全局尺度：捕捉长程相互作用和系统级特性
"""

import jax
import jax.numpy as jnp
import flax
import flax.linen as nn
from typing import Dict, Tuple, List, Callable, Optional, Any
import jraph

class MultiScaleMessagePassing(nn.Module):
    """多尺度消息传递层，在多个距离尺度上处理原子相互作用"""
    
    hidden_dim: int
    scales: List[float] = (2.0, 5.0, 10.0)  # 局部、中等和长程尺度(Å)
    activation: Callable = nn.swish
    use_attention: bool = True
    
    def setup(self):
        # 创建每个尺度的消息传递网络
        self.scale_networks = [
            nn.Sequential([
                nn.Dense(self.hidden_dim),
                self.activation,
                nn.Dense(self.hidden_dim)
            ])
            for _ in range(len(self.scales))
        ]
        
        # 注意力机制用于加权不同尺度的贡献
        if self.use_attention:
            self.attention_nets = [
                nn.Sequential([
                    nn.Dense(self.hidden_dim // 4),
                    self.activation,
                    nn.Dense(1)
                ])
                for _ in range(len(self.scales))
            ]
        
        # 最终聚合网络
        self.aggregate = nn.Sequential([
            nn.Dense(self.hidden_dim),
            self.activation,
            nn.Dense(self.hidden_dim)
        ])
    
    def create_scale_masks(self, distances, batch_size, n_nodes):
        """为每个尺度创建距离掩码"""
        masks = []
        
        # 为每个尺度创建掩码
        for i, max_dist in enumerate(self.scales):
            min_dist = 0.0 if i == 0 else self.scales[i-1]
            
            # 对应尺度的掩码
            scale_mask = (distances >= min_dist) & (distances < max_dist)
            
            # 移除自环(对角线)
            diag_mask = ~jnp.eye(n_nodes, dtype=bool)[None, :, :]
            scale_mask = scale_mask & diag_mask
            
            masks.append(scale_mask)
            
        return masks
    
    def __call__(self, node_features, positions, training=True):
        """
        多尺度消息传递操作
        
        Args:
            node_features: 节点特征 [batch_size, n_nodes, hidden_dim]
            positions: 原子坐标 [batch_size, n_nodes, 3]
            training: 是否在训练模式
            
        Returns:
            updated_features: 更新后的节点特征 [batch_size, n_nodes, hidden_dim]
        """
        batch_size, n_nodes = node_features.shape[0], node_features.shape[1]
        
        # 计算所有原子对的距离
        diff = positions[:, :, None, :] - positions[:, None, :, :]  # [batch, n_nodes, n_nodes, 3]
        distances = jnp.sqrt(jnp.sum(diff**2, axis=-1))  # [batch, n_nodes, n_nodes]
        
        # 创建每个尺度的掩码
        scale_masks = self.create_scale_masks(distances, batch_size, n_nodes)
        
        # 在每个尺度上执行消息传递
        scale_outputs = []
        for i, scale_mask in enumerate(scale_masks):
            # 复制节点特征以便于计算节点对表示
            senders = node_features[:, :, None, :].repeat(n_nodes, axis=2)  # [batch, n_nodes, n_nodes, hidden]
            receivers = node_features[:, None, :, :].repeat(n_nodes, axis=1)  # [batch, n_nodes, n_nodes, hidden]
            
            # 创建边特征(嵌入距离信息)
            distance_feats = self._rbf_expansion(distances, num_basis=16, max_distance=self.scales[-1])
            
            # 组合发送者、接收者和距离特征
            edge_feats = jnp.concatenate([
                senders, receivers, distance_feats
            ], axis=-1)
            
            # 应用尺度网络
            scale_output = self.scale_networks[i](edge_feats)
            
            # 应用尺度掩码
            masked_output = scale_output * scale_mask[:, :, :, None]
            
            # 聚合信息(对邻居求和)
            scale_aggregated = jnp.sum(masked_output, axis=2)  # [batch, n_nodes, hidden]
            scale_outputs.append(scale_aggregated)
        
        # 使用注意力机制聚合不同尺度的输出
        if self.use_attention:
            # 计算每个尺度的注意力分数
            attention_scores = []
            for i, scale_out in enumerate(scale_outputs):
                score = self.attention_nets[i](scale_out)  # [batch, n_nodes, 1]
                attention_scores.append(score)
                
            # Softmax归一化
            attention_scores = jnp.concatenate(attention_scores, axis=-1)  # [batch, n_nodes, n_scales]
            attention_weights = jax.nn.softmax(attention_scores, axis=-1)
            
            # 加权聚合
            weighted_outputs = []
            for i, scale_out in enumerate(scale_outputs):
                weighted = scale_out * attention_weights[:, :, i:i+1]
                weighted_outputs.append(weighted)
                
            combined = jnp.sum(jnp.stack(weighted_outputs), axis=0)
        else:
            # 简单连接所有尺度的输出
            combined = jnp.concatenate(scale_outputs, axis=-1)
            combined = nn.Dense(self.hidden_dim)(combined)
        
        # 最终处理
        output = self.aggregate(combined)
        
        # 残差连接
        return node_features + output
    
    def _rbf_expansion(self, distances, num_basis=16, max_distance=10.0):
        """将距离展开为径向基函数"""
        # 设置RBF中心
        centers = jnp.linspace(0, max_distance, num_basis)
        width = max_distance / num_basis
        
        # 计算RBF
        rbf = jnp.exp(-(distances[..., None] - centers)**2 / (2 * width**2))
        
        return rbf


class MultiScaleGNN(nn.Module):
    """多尺度图神经网络模型"""
    
    hidden_dim: int = 128
    num_layers: int = 3
    scales: List[float] = (2.0, 5.0, 10.0)
    output_dim: int = 1
    use_uncertainty: bool = True
    use_attention: bool = True
    
    def setup(self):
        # 原子特征编码器
        self.atom_encoder = nn.Sequential([
            nn.Dense(self.hidden_dim),
            nn.swish,
            nn.Dense(self.hidden_dim)
        ])
        
        # 多尺度消息传递层
        self.ms_layers = [
            MultiScaleMessagePassing(
                hidden_dim=self.hidden_dim,
                scales=self.scales,
                use_attention=self.use_attention
            )
            for _ in range(self.num_layers)
        ]
        
        # 输出预测层
        self.output_dim_with_uncertainty = self.output_dim * 2 if self.use_uncertainty else self.output_dim
        self.output_mlp = nn.Sequential([
            nn.Dense(self.hidden_dim),
            nn.swish,
            nn.Dense(self.hidden_dim // 2),
            nn.swish,
            nn.Dense(self.output_dim_with_uncertainty)
        ])
    
    def __call__(self, atom_features, positions, training=True):
        """
        模型前向传播
        
        Args:
            atom_features: 原子特征 [batch_size, n_atoms, feature_dim]
            positions: 原子坐标 [batch_size, n_atoms, 3]
            training: 是否在训练模式
            
        Returns:
            predictions: 能量预测，可能包含不确定性估计
            node_embeddings: 最终节点嵌入
        """
        # 编码原子特征
        x = self.atom_encoder(atom_features)
        
        # 应用多尺度层
        for ms_layer in self.ms_layers:
            x = ms_layer(x, positions, training=training)
        
        # 全局聚合
        # 使用平均池化和最大池化
        mean_pooled = jnp.mean(x, axis=1)  # [batch, hidden_dim]
        max_pooled = jnp.max(x, axis=1)    # [batch, hidden_dim]
        
        # 组合池化结果
        graph_repr = jnp.concatenate([mean_pooled, max_pooled], axis=-1)
        
        # 预测输出
        output = self.output_mlp(graph_repr)
        
        if self.use_uncertainty:
            # 将输出分割为预测值和不确定性
            predictions = output[:, :self.output_dim]
            uncertainty = jax.nn.softplus(output[:, self.output_dim:])  # 确保不确定性为正
            return (predictions, uncertainty), x
        else:
            return output, x


class HierarchicalPooling(nn.Module):
    """层次化池化层，用于创建多尺度表示"""
    
    hidden_dim: int
    num_clusters: int = 8
    
    def setup(self):
        # 聚类分配网络
        self.assignment_net = nn.Sequential([
            nn.Dense(self.hidden_dim),
            nn.swish,
            nn.Dense(self.num_clusters)
        ])
        
        # 聚类表示更新网络
        self.update_net = nn.Sequential([
            nn.Dense(self.hidden_dim),
            nn.swish,
            nn.Dense(self.hidden_dim)
        ])
    
    def __call__(self, node_features, adjacency=None):
        """
        层次化池化操作
        
        Args:
            node_features: 节点特征 [batch_size, n_nodes, hidden_dim]
            adjacency: 邻接矩阵 [batch_size, n_nodes, n_nodes] (可选)
            
        Returns:
            pooled_features: 池化后的特征 [batch_size, num_clusters, hidden_dim]
            cluster_adjacency: 聚类间邻接矩阵 [batch_size, num_clusters, num_clusters]
            assignment: 节点到聚类的分配矩阵 [batch_size, n_nodes, num_clusters]
        """
        batch_size, n_nodes = node_features.shape[0], node_features.shape[1]
        
        # 计算节点到聚类的分配
        assignment_logits = self.assignment_net(node_features)
        assignment = jax.nn.softmax(assignment_logits, axis=-1)
        
        # 聚合节点特征到聚类
        pooled_features = jnp.matmul(
            assignment.transpose(0, 2, 1),  # [batch, num_clusters, n_nodes]
            node_features                   # [batch, n_nodes, hidden_dim]
        )  # [batch, num_clusters, hidden_dim]
        
        # 更新聚类表示
        pooled_features = self.update_net(pooled_features)
        
        # 如果提供了邻接矩阵，计算聚类间的邻接矩阵
        if adjacency is not None:
            # S^T A S，其中S是分配矩阵
            cluster_adjacency = jnp.matmul(
                jnp.matmul(
                    assignment.transpose(0, 2, 1),  # [batch, num_clusters, n_nodes]
                    adjacency                      # [batch, n_nodes, n_nodes]
                ),
                assignment                         # [batch, n_nodes, num_clusters]
            )  # [batch, num_clusters, num_clusters]
            
            return pooled_features, cluster_adjacency, assignment
        
        return pooled_features, None, assignment


class MultiScaleHierarchicalGNN(nn.Module):
    """结合多尺度消息传递和层次化池化的GNN模型"""
    
    hidden_dim: int = 128
    num_ms_layers: int = 2  # 每个尺度的MS-GNN层数
    num_hierarchies: int = 2  # 层次级别数
    clusters_per_hierarchy: List[int] = (16, 4)  # 每个层次的聚类数
    scales: List[float] = (2.0, 5.0, 10.0)
    output_dim: int = 1
    use_uncertainty: bool = True
    
    def setup(self):
        # 原子特征编码器
        self.atom_encoder = nn.Sequential([
            nn.Dense(self.hidden_dim),
            nn.swish,
            nn.Dense(self.hidden_dim)
        ])
        
        # 为每个层次创建多尺度层
        self.hierarchy_ms_layers = []
        for h in range(self.num_hierarchies + 1):  # +1是为了原始尺度
            h_layers = [
                MultiScaleMessagePassing(
                    hidden_dim=self.hidden_dim,
                    scales=self.scales
                )
                for _ in range(self.num_ms_layers)
            ]
            self.hierarchy_ms_layers.append(h_layers)
        
        # 层次池化层
        if self.num_hierarchies > 0:
            self.pooling_layers = [
                HierarchicalPooling(
                    hidden_dim=self.hidden_dim,
                    num_clusters=self.clusters_per_hierarchy[h]
                )
                for h in range(self.num_hierarchies)
            ]
        
        # 跨层次注意力整合
        self.cross_attention = nn.MultiHeadDotProductAttention(
            num_heads=4,
            qkv_features=self.hidden_dim
        )
        
        # 输出预测层
        out_dim = self.output_dim * 2 if self.use_uncertainty else self.output_dim
        self.output_mlp = nn.Sequential([
            nn.Dense(self.hidden_dim),
            nn.swish,
            nn.Dense(self.hidden_dim // 2),
            nn.swish,
            nn.Dense(out_dim)
        ])
    
    def create_adjacency(self, positions, threshold=10.0):
        """根据原子位置创建邻接矩阵"""
        diff = positions[:, :, None, :] - positions[:, None, :, :]  # [batch, n, n, 3]
        dist = jnp.sqrt(jnp.sum(diff**2, axis=-1))  # [batch, n, n]
        
        # 基于距离阈值创建邻接矩阵
        adjacency = (dist < threshold).astype(jnp.float32)
        
        # 移除自环
        diag_mask = 1.0 - jnp.eye(positions.shape[1])[None, :, :]
        adjacency = adjacency * diag_mask
        
        return adjacency
    
    def __call__(self, atom_features, positions, training=True):
        """
        模型前向传播
        
        Args:
            atom_features: 原子特征 [batch_size, n_atoms, feature_dim]
            positions: 原子坐标 [batch_size, n_atoms, 3]
            training: 是否在训练模式
            
        Returns:
            predictions: 能量预测，可能包含不确定性估计
            hierarchical_embeddings: 每个层次的嵌入
        """
        batch_size, n_atoms = atom_features.shape[0], atom_features.shape[1]
        
        # 创建邻接矩阵
        adjacency = self.create_adjacency(positions)
        
        # 编码原子特征
        x = self.atom_encoder(atom_features)
        
        # 在原始尺度上应用多尺度层
        for ms_layer in self.hierarchy_ms_layers[0]:
            x = ms_layer(x, positions, training=training)
        
        # 存储所有层次的表示
        hierarchy_features = [x]
        hierarchy_positions = [positions]
        hierarchy_adjacencies = [adjacency]
        
        # 应用层次池化和多尺度层
        current_x = x
        current_pos = positions
        current_adj = adjacency
        
        for h in range(self.num_hierarchies):
            # 层次池化
            pooled_x, pooled_adj, assignment = self.pooling_layers[h](current_x, current_adj)
            
            # 计算池化后的虚拟原子位置(加权平均)
            pooled_pos = jnp.matmul(
                assignment.transpose(0, 2, 1),  # [batch, num_clusters, n_nodes]
                current_pos                    # [batch, n_nodes, 3]
            )  # [batch, num_clusters, 3]
            
            # 在当前层次上应用多尺度层
            current_x = pooled_x
            current_pos = pooled_pos
            current_adj = pooled_adj
            
            for ms_layer in self.hierarchy_ms_layers[h+1]:
                current_x = ms_layer(current_x, current_pos, training=training)
            
            # 存储当前层次的表示
            hierarchy_features.append(current_x)
            hierarchy_positions.append(current_pos)
            hierarchy_adjacencies.append(current_adj)
        
        # 跨层次融合(使用注意力)
        # 将所有层次的表示聚合到全局
        global_features = []
        for h_feat in hierarchy_features:
            # 全局平均池化
            global_feat = jnp.mean(h_feat, axis=1)
            global_features.append(global_feat)
        
        # 连接所有层次的全局表示
        multi_scale_repr = jnp.concatenate(global_features, axis=-1)
        
        # 最终MLP预测
        output = self.output_mlp(multi_scale_repr)
        
        if self.use_uncertainty:
            # 分割为预测值和不确定性估计
            predictions = output[:, :self.output_dim]
            uncertainty = jax.nn.softplus(output[:, self.output_dim:])
            return (predictions, uncertainty), hierarchy_features
        else:
            return output, hierarchy_features 