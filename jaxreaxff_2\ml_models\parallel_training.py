import jax
import jax.numpy as jnp
import numpy as np
import optax
import time
from typing import Dict, List, Tuple, Callable, Any, Optional, Union
from functools import partial
from .base_model import BaseModel

class ParallelTrainer:
    """
    并行训练器，利用JAX的并行计算能力加速训练
    """
    
    def __init__(
        self,
        model: BaseModel,
        optimizer: optax.GradientTransformation,
        loss_fn: Callable,
        num_devices: Optional[int] = None,
        batch_size_per_device: int = 32,
        precision: str = 'float32',  # 'float32', 'float16', 'bfloat16'
        gradient_accumulation_steps: int = 1,
        max_grad_norm: Optional[float] = 1.0,
        sync_interval: int = 1
    ):
        """
        初始化并行训练器
        
        Args:
            model: 要训练的模型
            optimizer: 优化器
            loss_fn: 损失函数
            num_devices: 使用的设备数量 (None表示使用所有可用设备)
            batch_size_per_device: 每个设备的批量大小
            precision: 计算精度
            gradient_accumulation_steps: 梯度累积步数
            max_grad_norm: 梯度裁剪范数上限
            sync_interval: 设备同步间隔
        """
        self.model = model
        self.optimizer = optimizer
        self.loss_fn = loss_fn
        self.batch_size_per_device = batch_size_per_device
        self.precision = precision
        self.gradient_accumulation_steps = gradient_accumulation_steps
        self.max_grad_norm = max_grad_norm
        self.sync_interval = sync_interval
        
        # 检测可用设备
        self.devices = jax.devices()
        if num_devices is not None:
            self.devices = self.devices[:num_devices]
        self.num_devices = len(self.devices)
        
        # 检查是否有足够的设备
        if self.num_devices == 0:
            raise ValueError("No JAX devices available")
        elif self.num_devices == 1:
            print("Only one device available, will use single-device training")
        else:
            print(f"Using {self.num_devices} devices for parallel training")
            
        # 设置计算精度
        if precision == 'float16':
            self.dtype = jnp.float16
        elif precision == 'bfloat16':
            self.dtype = jnp.bfloat16
        else:
            self.dtype = jnp.float32
            
        # 初始化训练状态
        self.step = 0
        self.best_loss = float('inf')
        self.opt_state = None
        self.replicated_state = None
        self.train_metrics = {
            'train_loss': [],
            'val_loss': [],
            'learning_rate': []
        }
    
    def _create_train_state(self, rng: jax.random.PRNGKey, dummy_input: jnp.ndarray) -> Dict:
        """
        创建训练状态
        
        Args:
            rng: 随机数生成器
            dummy_input: 虚拟输入数据
            
        Returns:
            state: 训练状态字典
        """
        # 初始化模型参数
        params = self.model.initialize(rng, dummy_input.shape)
        
        # 初始化优化器状态
        opt_state = self.optimizer.init(params)
        
        return {
            'params': params,
            'opt_state': opt_state,
            'step': 0
        }
    
    def _shard_batch(
        self, 
        batch: Tuple[jnp.ndarray, jnp.ndarray]
    ) -> Tuple[jnp.ndarray, jnp.ndarray]:
        """
        将批次数据分片到多个设备
        
        Args:
            batch: 包含输入和标签的元组
            
        Returns:
            sharded_batch: 分片后的批次数据
        """
        # 分离输入和标签
        inputs, labels = batch
        
        # 计算每个设备的数据量
        batch_size = inputs.shape[0]
        per_device_batch_size = batch_size // self.num_devices
        
        # 重塑数据以适应设备数量
        def reshape_for_devices(x):
            return x.reshape((self.num_devices, per_device_batch_size) + x.shape[1:])
            
        # 分片输入和标签
        sharded_inputs = reshape_for_devices(inputs)
        sharded_labels = reshape_for_devices(labels)
        
        return sharded_inputs, sharded_labels
    
    def _train_step_single(
        self, 
        state: Dict, 
        batch: Tuple[jnp.ndarray, jnp.ndarray],
        training: bool = True
    ) -> Tuple[Dict, Dict]:
        """
        单设备训练步骤
        
        Args:
            state: 训练状态字典
            batch: 批次数据
            training: 是否处于训练模式
            
        Returns:
            new_state: 更新后的状态
            metrics: 训练指标
        """
        # 定义损失函数
        def loss_fn(params):
            inputs, labels = batch
            # 前向传播
            pred = self.model(inputs, training=training)
            # 计算损失
            loss = self.loss_fn(pred, labels)
            return loss, pred
            
        # 计算梯度
        grad_fn = jax.value_and_grad(loss_fn, has_aux=True)
        (loss, pred), grads = grad_fn(state['params'])
        
        # 梯度裁剪
        if self.max_grad_norm is not None:
            grads = jax.tree_map(
                lambda g: jnp.clip(g, -self.max_grad_norm, self.max_grad_norm),
                grads
            )
            
        # 更新参数
        updates, new_opt_state = self.optimizer.update(
            grads, state['opt_state'], state['params']
        )
        new_params = optax.apply_updates(state['params'], updates)
        
        # 更新训练状态
        new_state = {
            'params': new_params,
            'opt_state': new_opt_state,
            'step': state['step'] + 1
        }
        
        # 计算指标
        metrics = {
            'loss': loss,
            'params_norm': optax.global_norm(new_params),
            'grads_norm': optax.global_norm(grads)
        }
        
        return new_state, metrics
    
    @partial(jax.pmap, axis_name='devices')
    def _train_step(
        self, 
        state: Dict, 
        batch: Tuple[jnp.ndarray, jnp.ndarray]
    ) -> Tuple[Dict, Dict]:
        """
        并行训练步骤
        
        Args:
            state: 训练状态字典
            batch: 分片的批次数据
            
        Returns:
            new_state: 更新后的状态
            metrics: 训练指标
        """
        # 定义损失函数
        def loss_fn(params):
            inputs, labels = batch
            # 前向传播
            pred = self.model(inputs, training=True)
            # 计算损失
            loss = self.loss_fn(pred, labels)
            return loss, pred
            
        # 计算梯度
        grad_fn = jax.value_and_grad(loss_fn, has_aux=True)
        (loss, pred), grads = grad_fn(state['params'])
        
        # 梯度裁剪
        if self.max_grad_norm is not None:
            grads = jax.tree_map(
                lambda g: jnp.clip(g, -self.max_grad_norm, self.max_grad_norm),
                grads
            )
            
        # 多设备同步梯度
        grads = jax.lax.pmean(grads, axis_name='devices')
        
        # 更新参数
        updates, new_opt_state = self.optimizer.update(
            grads, state['opt_state'], state['params']
        )
        new_params = optax.apply_updates(state['params'], updates)
        
        # 更新训练状态
        new_state = {
            'params': new_params,
            'opt_state': new_opt_state,
            'step': state['step'] + 1
        }
        
        # 计算指标
        metrics = {
            'loss': loss,
            'params_norm': optax.global_norm(new_params),
            'grads_norm': optax.global_norm(grads)
        }
        
        return new_state, metrics
    
    @partial(jax.pmap, axis_name='devices')
    def _eval_step(
        self, 
        params: Dict, 
        batch: Tuple[jnp.ndarray, jnp.ndarray]
    ) -> Dict:
        """
        并行评估步骤
        
        Args:
            params: 模型参数
            batch: 分片的批次数据
            
        Returns:
            metrics: 评估指标
        """
        inputs, labels = batch
        # 前向传播
        pred = self.model(inputs, training=False)
        # 计算损失
        loss = self.loss_fn(pred, labels)
        
        # 计算指标
        metrics = {
            'loss': loss
        }
        
        # 多设备同步指标
        metrics = jax.lax.pmean(metrics, axis_name='devices')
        
        return metrics
    
    def _eval_step_single(
        self, 
        params: Dict, 
        batch: Tuple[jnp.ndarray, jnp.ndarray]
    ) -> Dict:
        """
        单设备评估步骤
        
        Args:
            params: 模型参数
            batch: 批次数据
            
        Returns:
            metrics: 评估指标
        """
        inputs, labels = batch
        # 前向传播
        pred = self.model(inputs, training=False)
        # 计算损失
        loss = self.loss_fn(pred, labels)
        
        # 计算指标
        metrics = {
            'loss': loss
        }
        
        return metrics
    
    def train_epoch(
        self, 
        train_data: Tuple[jnp.ndarray, jnp.ndarray],
        val_data: Optional[Tuple[jnp.ndarray, jnp.ndarray]] = None,
        batch_size: Optional[int] = None,
        rng: Optional[jax.random.PRNGKey] = None
    ) -> Dict:
        """
        训练一个epoch
        
        Args:
            train_data: 训练数据和标签的元组
            val_data: 验证数据和标签的元组
            batch_size: 批量大小 (每个设备的批量大小的倍数)
            rng: 随机数生成器
            
        Returns:
            metrics: 训练和验证指标
        """
        train_inputs, train_labels = train_data
        
        if batch_size is None:
            batch_size = self.batch_size_per_device * self.num_devices
            
        # 确保批量大小是设备数量的倍数
        if batch_size % self.num_devices != 0 and self.num_devices > 1:
            batch_size = (batch_size // self.num_devices) * self.num_devices
            print(f"Adjusting batch size to {batch_size} to be divisible by {self.num_devices} devices")
            
        # 计算每个设备的批量大小
        per_device_batch_size = batch_size // max(1, self.num_devices)
        
        # 生成随机数生成器
        if rng is None:
            rng = jax.random.PRNGKey(int(time.time() * 1000) % (2**32))
            
        # 创建初始训练状态 (如果尚未初始化)
        if self.replicated_state is None:
            dummy_input = train_inputs[:per_device_batch_size]
            state = self._create_train_state(rng, dummy_input)
            
            if self.num_devices > 1:
                # 复制状态到所有设备
                self.replicated_state = jax.device_put_replicated(
                    state, self.devices
                )
            else:
                self.replicated_state = state
        
        # 准备数据的批次迭代器
        steps_per_epoch = len(train_inputs) // batch_size
        indices = jax.random.permutation(rng, len(train_inputs))
        
        # 训练循环
        epoch_loss = 0.0
        for step in range(steps_per_epoch):
            # 获取批次索引
            batch_indices = indices[step * batch_size:(step + 1) * batch_size]
            
            # 准备批次数据
            batch_inputs = train_inputs[batch_indices]
            batch_labels = train_labels[batch_indices]
            
            # 转换为设备所需的精度
            batch_inputs = batch_inputs.astype(self.dtype)
            
            # 执行训练步骤
            if self.num_devices > 1:
                # 分片批次数据到多个设备
                sharded_inputs, sharded_labels = self._shard_batch((batch_inputs, batch_labels))
                
                # 执行并行训练步骤
                self.replicated_state, metrics = self._train_step(
                    self.replicated_state, 
                    (sharded_inputs, sharded_labels)
                )
                
                # 获取主设备上的损失
                step_loss = jnp.mean(metrics['loss'])
            else:
                # 执行单设备训练步骤
                self.replicated_state, metrics = self._train_step_single(
                    self.replicated_state,
                    (batch_inputs, batch_labels)
                )
                
                step_loss = metrics['loss']
            
            # 累积损失
            epoch_loss += float(step_loss)
            
            # 更新全局步骤
            self.step += 1
            
        # 计算平均损失
        epoch_loss /= steps_per_epoch
        
        # 记录训练指标
        self.train_metrics['train_loss'].append(float(epoch_loss))
        
        # 如果提供了验证数据，则进行验证
        val_loss = None
        if val_data is not None:
            val_loss = self.evaluate(val_data, batch_size)
            self.train_metrics['val_loss'].append(float(val_loss))
            
            # 更新最佳损失
            if val_loss < self.best_loss:
                self.best_loss = val_loss
                
        # 返回epoch指标
        return {
            'train_loss': float(epoch_loss),
            'val_loss': float(val_loss) if val_loss is not None else None,
            'step': self.step
        }
    
    def evaluate(
        self, 
        eval_data: Tuple[jnp.ndarray, jnp.ndarray],
        batch_size: Optional[int] = None
    ) -> float:
        """
        评估模型
        
        Args:
            eval_data: 评估数据和标签的元组
            batch_size: 批量大小
            
        Returns:
            eval_loss: 评估损失
        """
        eval_inputs, eval_labels = eval_data
        
        if batch_size is None:
            batch_size = self.batch_size_per_device * max(1, self.num_devices)
            
        # 确保批量大小是设备数量的倍数
        if batch_size % self.num_devices != 0 and self.num_devices > 1:
            batch_size = (batch_size // self.num_devices) * self.num_devices
            
        # 获取当前模型参数
        if self.num_devices > 1:
            params = jax.tree_map(lambda x: x[0], self.replicated_state['params'])
            # 复制参数到所有设备
            replicated_params = jax.device_put_replicated(params, self.devices)
        else:
            params = self.replicated_state['params']
            replicated_params = params
        
        # 准备数据的批次迭代器
        steps_per_eval = len(eval_inputs) // batch_size
        
        # 评估循环
        eval_loss = 0.0
        for step in range(steps_per_eval):
            # 准备批次数据
            batch_inputs = eval_inputs[step * batch_size:(step + 1) * batch_size]
            batch_labels = eval_labels[step * batch_size:(step + 1) * batch_size]
            
            # 转换为设备所需的精度
            batch_inputs = batch_inputs.astype(self.dtype)
            
            # 执行评估步骤
            if self.num_devices > 1:
                # 分片批次数据到多个设备
                sharded_inputs, sharded_labels = self._shard_batch((batch_inputs, batch_labels))
                
                # 执行并行评估步骤
                metrics = self._eval_step(replicated_params, (sharded_inputs, sharded_labels))
                
                # 获取主设备上的损失
                step_loss = jnp.mean(metrics['loss'])
            else:
                # 执行单设备评估步骤
                metrics = self._eval_step_single(replicated_params, (batch_inputs, batch_labels))
                step_loss = metrics['loss']
            
            # 累积损失
            eval_loss += float(step_loss)
            
        # 计算平均损失
        if steps_per_eval > 0:
            eval_loss /= steps_per_eval
        
        return float(eval_loss)
    
    def train(
        self, 
        train_data: Tuple[jnp.ndarray, jnp.ndarray],
        val_data: Optional[Tuple[jnp.ndarray, jnp.ndarray]] = None,
        num_epochs: int = 10,
        batch_size: Optional[int] = None,
        rng: Optional[jax.random.PRNGKey] = None,
        early_stopping_patience: Optional[int] = None,
        save_best: bool = True,
        save_path: Optional[str] = None,
        callback: Optional[Callable] = None
    ) -> Dict:
        """
        训练模型
        
        Args:
            train_data: 训练数据和标签的元组
            val_data: 验证数据和标签的元组
            num_epochs: 训练轮数
            batch_size: 批量大小
            rng: 随机数生成器
            early_stopping_patience: 早停耐心值
            save_best: 是否保存最佳模型
            save_path: 模型保存路径
            callback: 每个epoch结束时调用的回调函数
            
        Returns:
            metrics: 训练指标
        """
        if rng is None:
            rng = jax.random.PRNGKey(int(time.time() * 1000) % (2**32))
            
        # 记录训练开始时间
        start_time = time.time()
        
        # 初始化早停计数器
        if early_stopping_patience is not None:
            patience_counter = 0
            best_val_loss = float('inf')
            
        # 训练循环
        for epoch in range(num_epochs):
            # 为当前epoch生成随机数种子
            epoch_rng = jax.random.fold_in(rng, epoch)
            
            # 训练一个epoch
            metrics = self.train_epoch(train_data, val_data, batch_size, epoch_rng)
            
            # 打印训练进度
            epoch_time = time.time() - start_time
            print(f"Epoch {epoch+1}/{num_epochs}, "
                  f"Train Loss: {metrics['train_loss']:.6f}, "
                  f"Val Loss: {metrics['val_loss']:.6f if metrics['val_loss'] is not None else 'N/A'}, "
                  f"Time: {epoch_time:.2f}s")
                  
            # 调用回调函数
            if callback is not None:
                callback(epoch, metrics)
                
            # 早停检查
            if early_stopping_patience is not None and val_data is not None:
                val_loss = metrics['val_loss']
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                    
                    # 保存最佳模型
                    if save_best and save_path is not None:
                        self.save_model(save_path)
                else:
                    patience_counter += 1
                    if patience_counter >= early_stopping_patience:
                        print(f"Early stopping at epoch {epoch+1}")
                        break
        
        # 计算总训练时间
        total_time = time.time() - start_time
        print(f"Training completed in {total_time:.2f}s")
        
        # 返回训练指标
        return self.train_metrics
    
    def save_model(self, path: str):
        """
        保存模型参数
        
        Args:
            path: 保存路径
        """
        # 获取参数
        if self.num_devices > 1:
            # 获取主设备上的参数
            params = jax.tree_map(lambda x: x[0], self.replicated_state['params'])
        else:
            params = self.replicated_state['params']
        
        # 保存参数
        self.model.params = params
        self.model.save(path)
    
    def load_model(self, path: str):
        """
        加载模型参数
        
        Args:
            path: 加载路径
        """
        # 加载参数
        self.model.load(path)
        params = self.model.params
        
        # 复制参数到所有设备
        if self.replicated_state is not None:
            if self.num_devices > 1:
                self.replicated_state = jax.device_put_replicated(
                    {
                        'params': params,
                        'opt_state': self.replicated_state['opt_state'],
                        'step': self.replicated_state['step']
                    },
                    self.devices
                )
            else:
                self.replicated_state['params'] = params
    
    def get_model_parameters(self) -> Dict:
        """
        获取模型参数
        
        Returns:
            params: 模型参数
        """
        if self.replicated_state is not None:
            if self.num_devices > 1:
                return jax.tree_map(lambda x: x[0], self.replicated_state['params'])
            else:
                return self.replicated_state['params']
        else:
            return self.model.params
    
    def get_batch_size(self) -> int:
        """
        获取总批量大小
        
        Returns:
            batch_size: 总批量大小
        """
        return self.batch_size_per_device * max(1, self.num_devices) 