"""
ReaxFFOpt 数据处理模块
负责导入和解析各种类型的数据集文件
"""

import os
import numpy as np
import glob
from PyQt5.QtCore import QThread, pyqtSignal
import logging
import time

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatasetWorker(QThread):
    """数据集处理工作线程类"""
    progress = pyqtSignal(int)
    finished = pyqtSignal(dict)

    def __init__(self, folder_path, selected_datasets=None, clear_data=False):
        super().__init__()
        self.folder_path = folder_path
        self.selected_datasets = selected_datasets
        self.clear_data = clear_data
        self.handler = DatasetHandler()

    def run(self):
        """处理数据集文件夹"""
        logger.info(f"开始处理数据集文件夹: {self.folder_path}")

        try:
            # 如果没有指定数据集，则处理所有数据集
            if not self.selected_datasets:
                result = self.handler.process_folder(self.folder_path, self.progress.emit, clear_data=self.clear_data)
            else:
                # 只处理选中的数据集
                total_progress = len(self.selected_datasets)
                processed = 0
                for i, dataset in enumerate(self.selected_datasets):
                    dataset_path = os.path.join(self.folder_path, dataset)
                    if os.path.exists(dataset_path) and os.path.isdir(dataset_path):
                        # 处理单个数据集，使用传入的clear_data参数
                        # 只有第一个数据集时使用clear_data参数，后续数据集都累积添加
                        should_clear = self.clear_data and i == 0
                        self.handler.process_folder(dataset_path, None, clear_data=should_clear)

                        # 更新进度
                        processed += 1
                        progress = int((processed / total_progress) * 100)
                        self.progress.emit(progress)

                # 收集处理结果 - 计算总的结构数量
                total_structures = sum(len(structures) for structures in self.handler.structures.values())
                total_energies = sum(len(energies) for energies in self.handler.energies.values())
                total_forces = sum(len(forces) for forces in self.handler.forces.values())

                result = {
                    "structures": total_structures,
                    "energies": total_energies,
                    "forces": total_forces,
                    "training_sets": len(self.handler.training_sets),
                    "parameters": len(self.handler.parameters)
                }

            logger.info(f"数据集处理完成: {result}")
            self.finished.emit(result)

        except Exception as e:
            logger.error(f"处理数据集时发生错误: {str(e)}", exc_info=True)
            self.finished.emit({})  # 发送空结果表示处理失败

class DatasetHandler:
    """数据集处理类"""

    def __init__(self):
        # 存储不同类型的数据
        self.structures = {}  # 分子结构
        self.energies = {}    # 能量数据
        self.forces = {}      # 力数据
        self.training_sets = {}  # 训练集定义
        self.parameters = {}  # 力场参数
        self.calculation_results = {}  # 计算结果存储
        self.dataset_paths = {}  # 数据集路径信息 {dataset_name: full_path}

    def clear_data(self):
        """清除所有数据"""
        self.structures.clear()
        self.energies.clear()
        self.forces.clear()
        self.training_sets.clear()
        self.parameters.clear()
        self.calculation_results.clear()
        
        # 清除新增的数据类型
        if hasattr(self, 'control_files'):
            self.control_files.clear()
        if hasattr(self, 'json_files'):
            self.json_files.clear()
        if hasattr(self, 'csv_files'):
            self.csv_files.clear()
        if hasattr(self, 'yaml_files'):
            self.yaml_files.clear()
        if hasattr(self, 'data_files'):
            self.data_files.clear()
        if hasattr(self, 'dataset_paths'):
            self.dataset_paths.clear()
            
        logger.info("已清除所有数据")

    def process_folder(self, folder_path, progress_callback=None, clear_data=False):
        """处理整个数据集文件夹 - 修复：默认不清除数据，支持累积导入"""
        logger.info(f"开始处理数据集文件夹: {folder_path}, clear_data={clear_data}")

        # 只在明确要求时清除之前的数据
        if clear_data:
            self.clear_data()
            logger.info("已清除之前的数据")
        else:
            logger.info("保留之前的数据，将累积导入新数据")

        # 获取所有文件和子目录
        all_files = []
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                all_files.append(os.path.join(root, file))

        total_files = len(all_files)
        processed_files = 0

        # 首先处理参数文件并建立路径映射
        for file_path in all_files:
            if os.path.basename(file_path).lower() == 'params':
                try:
                    params = self._parse_params(file_path)
                    if params:
                        # 使用相对路径创建数据集名称以保留层次结构
                        dataset_name, dataset_path = self._create_dataset_name_with_path(file_path, folder_path)
                        self.parameters[dataset_name] = params
                        self.dataset_paths[dataset_name] = dataset_path
                        logger.info(f"成功加载参数文件: {file_path}")
                        logger.info(f"数据集名称: {dataset_name}, 路径: {dataset_path}")
                except Exception as e:
                    logger.error(f"处理参数文件失败: {file_path}, 错误: {str(e)}")

                processed_files += 1
                if progress_callback:
                    progress = int((processed_files / total_files) * 100)
                    progress_callback(progress)

        # 处理所有类型的文件
        for file_path in all_files:
            if processed_files in [i for i in range(0, total_files) if i % max(1, total_files // 100) == 0]:
                if progress_callback:
                    progress = int((processed_files / total_files) * 100)
                    progress_callback(progress)
            
            file_type = self._identify_file_type(file_path)
            # 使用相对路径创建数据集名称以保留层次结构
            dataset_name, dataset_path = self._create_dataset_name_with_path(file_path, folder_path)
            
            # 记录数据集路径
            if dataset_name not in self.dataset_paths:
                self.dataset_paths[dataset_name] = dataset_path
            
            try:
                if file_type == 'geo':
                    structures = self._parse_geo(file_path)
                    if structures:
                        self.structures[dataset_name] = structures
                        logger.info(f"成功加载geo文件: {file_path}")
                        
                elif file_type == 'xyz':
                    structures = self._parse_xyz(file_path)
                    if structures:
                        if dataset_name not in self.structures:
                            self.structures[dataset_name] = []
                        self.structures[dataset_name].extend(structures)
                        logger.info(f"成功加载XYZ文件: {file_path}")
                        
                elif file_type == 'pdb':
                    structures = self._parse_pdb(file_path)
                    if structures:
                        if dataset_name not in self.structures:
                            self.structures[dataset_name] = []
                        self.structures[dataset_name].extend(structures)
                        logger.info(f"成功加载PDB文件: {file_path}")
                        
                elif file_type == 'trainset':
                    training_set = self._parse_trainset(file_path)
                    if training_set:
                        self.training_sets[dataset_name] = training_set
                        logger.info(f"成功加载训练集文件: {file_path}")
                        
                elif file_type == 'control':
                    control_data = self._parse_control(file_path)
                    if control_data:
                        if 'control_files' not in self.__dict__:
                            self.control_files = {}
                        self.control_files[dataset_name] = control_data
                        logger.info(f"成功加载控制文件: {file_path}")
                        
                elif file_type == 'json':
                    json_data = self._parse_json(file_path)
                    if json_data:
                        if 'json_files' not in self.__dict__:
                            self.json_files = {}
                        self.json_files[dataset_name] = json_data
                        logger.info(f"成功加载JSON文件: {file_path}")
                        
                elif file_type == 'csv':
                    csv_data = self._parse_csv(file_path)
                    if csv_data:
                        if 'csv_files' not in self.__dict__:
                            self.csv_files = {}
                        self.csv_files[dataset_name] = csv_data
                        logger.info(f"成功加载CSV文件: {file_path}")
                        
                elif file_type == 'yaml':
                    yaml_data = self._parse_yaml(file_path)
                    if yaml_data:
                        if 'yaml_files' not in self.__dict__:
                            self.yaml_files = {}
                        self.yaml_files[dataset_name] = yaml_data
                        logger.info(f"成功加载YAML文件: {file_path}")
                        
                elif file_type == 'data':
                    data_content = self._parse_data_file(file_path)
                    if data_content:
                        if 'data_files' not in self.__dict__:
                            self.data_files = {}
                        self.data_files[dataset_name] = data_content
                        logger.info(f"成功加载数据文件: {file_path}")
                        
            except Exception as e:
                logger.error(f"处理文件失败: {file_path}, 错误: {str(e)}")
            
            processed_files += 1

        # 收集处理结果 - 计算总的结构数量
        total_structures = sum(len(structures) for structures in self.structures.values())
        total_energies = sum(len(energies) for energies in self.energies.values())
        total_forces = sum(len(forces) for forces in self.forces.values())

        result = {
            "structures": total_structures,
            "energies": total_energies,
            "forces": total_forces,
            "training_sets": len(self.training_sets),
            "parameters": len(self.parameters)
        }

        logger.info(f"数据集处理完成，结果: {result}")
        return result

    def _create_dataset_name_with_path(self, file_path, base_folder):
        """根据文件路径创建数据集名称，保留层次结构
        
        Args:
            file_path (str): 文件的完整路径
            base_folder (str): 基础文件夹路径
            
        Returns:
            tuple: (dataset_name, dataset_path)
        """
        try:
            # 获取文件所在的目录
            file_dir = os.path.dirname(file_path)
            
            # 计算相对于基础文件夹的相对路径
            rel_path = os.path.relpath(file_dir, base_folder)
            
            # 标准化路径分隔符为 '/'
            rel_path = rel_path.replace('\\', '/')
            
            # 如果是当前目录，使用文件夹名
            if rel_path == '.' or rel_path == '':
                dataset_name = os.path.basename(base_folder)
            else:
                # 使用相对路径作为数据集名称，这样可以保留层次结构
                dataset_name = rel_path
                
                # 特殊处理：如果路径包含多层，创建层次结构名称
                if '/' in dataset_name:
                    parts = dataset_name.split('/')
                    if len(parts) >= 2:
                        # 例如：disulfide/valSet -> valSet (作为 disulfide 的子集)
                        parent_name = parts[0]
                        child_name = parts[1]
                        dataset_name = f"{parent_name}/{child_name}"
            
            return dataset_name, file_dir
            
        except Exception as e:
            logger.error(f"创建数据集名称失败: {e}")
            # 回退到原来的逻辑
            return os.path.basename(os.path.dirname(file_path)), os.path.dirname(file_path)

    def _identify_file_type(self, file_path):
        """识别文件类型 - 扩展版本支持更多格式"""
        filename = os.path.basename(file_path).lower()
        file_ext = os.path.splitext(filename)[1].lower()

        # 基于文件名识别
        if filename == "geo":
            return "geo"
        elif filename == "trainset.in":
            return "trainset"
        elif filename.startswith("ffield"):
            return "ffield"
        elif filename == "params":
            return "params"
        elif filename == "control":
            return "control"
        elif filename == "iopt":
            return "iopt"
        
        # 基于扩展名识别
        elif file_ext == ".geo":
            return "geo"
        elif file_ext == ".xyz":
            return "xyz"
        elif file_ext == ".pdb":
            return "pdb"
        elif file_ext == ".cif":
            return "cif"
        elif file_ext == ".mol":
            return "mol"
        elif file_ext == ".sdf":
            return "sdf"
        elif file_ext == ".lmp" or file_ext == ".data":
            return "lammps"
        elif file_ext == ".bgf":
            return "bgf"
        elif file_ext == ".car":
            return "car"
        elif file_ext == ".msi":
            return "msi"
        elif file_ext == ".json":
            return "json"
        elif file_ext == ".yaml" or file_ext == ".yml":
            return "yaml"
        elif file_ext == ".toml":
            return "toml"
        elif file_ext == ".xml":
            return "xml"
        elif file_ext == ".csv":
            return "csv"
        elif file_ext == ".dat" or file_ext == ".txt":
            return "data"
        elif file_ext == ".out" or file_ext == ".log":
            return "output"

        # 如果文件名无法识别，尝试通过内容识别
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                first_line = f.readline().strip()
                if "BIOGRF" in first_line or "XTLGRF" in first_line:
                    return "geo"
                elif "ATOMS" in first_line:
                    return "trainset"
                elif "Reactive MD-force field" in first_line:
                    return "ffield"
                elif first_line.isdigit():  # 可能是xyz格式
                    return "xyz"
                elif "data_" in first_line and "CIF" in first_line:
                    return "cif"
        except:
            pass

        return "unknown"

    def _parse_geo(self, file_path):
        """解析ReaxFF几何文件(geo) - 完全重写版本"""
        structures = []
        current_structure = None

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            logger.info(f"开始解析geo文件: {file_path}, 总行数: {len(lines)}")

            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line:
                    continue

                # 处理XTLGRF和BIOGRF格式的开始标记
                if line.startswith("XTLGRF") or line.startswith("BIOGRF"):
                    # 保存前一个结构
                    if current_structure and len(current_structure['atoms']) > 0:
                        current_structure['coordinates'] = np.array(current_structure['coordinates'])
                        current_structure['atom_types'] = np.array(current_structure['atom_types'])
                        structures.append(current_structure)
                        logger.debug(f"保存结构: {current_structure['description']}, 原子数: {len(current_structure['atoms'])}")

                    # 开始新结构
                    current_structure = {
                        'description': '',
                        'atoms': [],
                        'cell': None,
                        'coordinates': [],
                        'atom_types': [],
                        'energy': None,
                        'connections': []
                    }
                    continue

                elif line.startswith("DESCRP"):
                    if current_structure is not None:
                        current_structure['description'] = line[7:].strip()
                    continue
                elif line.startswith("CRYSTX"):
                    # 解析晶胞参数
                    if current_structure is not None:
                        parts = line.split()
                        if len(parts) >= 7:
                            try:
                                current_structure['cell'] = {
                                    'a': float(parts[1]),
                                    'b': float(parts[2]),
                                    'c': float(parts[3]),
                                    'alpha': float(parts[4]),
                                    'beta': float(parts[5]),
                                    'gamma': float(parts[6])
                                }
                            except ValueError:
                                pass
                    continue

                elif line.startswith("HETATM") or line.startswith("ATOM"):
                    # 解析原子信息 - 使用ReaxFF geo格式
                    if current_structure is None:
                        continue

                    try:
                        # ReaxFF geo格式: HETATM     1 Co                  6.28853   6.78413   7.50395    Co  0 0  0.00000
                        # 使用空格分割解析，这样更可靠
                        parts = line.split()
                        if len(parts) >= 6:
                            atom_id = int(parts[1])
                            atom_symbol = parts[2]
                            x = float(parts[3])
                            y = float(parts[4])
                            z = float(parts[5])
                        else:
                            logger.warning(f"原子行格式不正确，字段数不足: {line}")
                            continue

                        atom = {
                            'id': atom_id,
                            'symbol': atom_symbol,
                            'x': x,
                            'y': y,
                            'z': z,
                            'charge': 0.0
                        }

                        current_structure['atoms'].append(atom)
                        current_structure['coordinates'].append([x, y, z])
                        current_structure['atom_types'].append(
                            self._convert_symbol_to_type(atom_symbol)
                        )

                        logger.debug(f"解析原子: {atom_symbol} at ({x:.3f}, {y:.3f}, {z:.3f})")

                    except (ValueError, IndexError) as e:
                        logger.warning(f"解析原子行失败: {line}, 错误: {str(e)}")
                        continue

                elif line.startswith("ENERGY"):
                    # 解析能量信息
                    if current_structure is not None:
                        parts = line.split()
                        if len(parts) >= 2:
                            try:
                                energy = float(parts[1])
                                current_structure['energy'] = energy
                                logger.debug(f"找到能量: {energy}")
                            except:
                                pass

                elif line.startswith("END"):
                    # 结构结束
                    if current_structure and len(current_structure['atoms']) > 0:
                        # 将坐标和原子类型转换为numpy数组
                        current_structure['coordinates'] = np.array(current_structure['coordinates'])
                        current_structure['atom_types'] = np.array(current_structure['atom_types'])
                        structures.append(current_structure)
                        current_structure = None

            # 添加最后一个结构（如果文件没有以END结尾）
            if current_structure and len(current_structure['atoms']) > 0:
                # 将坐标和原子类型转换为numpy数组
                current_structure['coordinates'] = np.array(current_structure['coordinates'])
                current_structure['atom_types'] = np.array(current_structure['atom_types'])
                structures.append(current_structure)

            logger.info(f"成功解析geo文件 {file_path}: 找到 {len(structures)} 个结构")

        except Exception as e:
            logger.error(f"解析geo文件失败: {str(e)}", exc_info=True)
            return []

        return structures

    def _parse_xyz(self, file_path):
        """解析XYZ格式文件"""
        structures = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            i = 0
            while i < len(lines):
                if lines[i].strip().isdigit():
                    num_atoms = int(lines[i].strip())
                    if i + 1 < len(lines):
                        description = lines[i + 1].strip()
                    else:
                        description = f"Structure from {os.path.basename(file_path)}"
                    
                    structure = {
                        'description': description,
                        'atoms': [],
                        'coordinates': [],
                        'atom_types': [],
                        'energy': None
                    }
                    
                    # 读取原子数据
                    for j in range(2, min(2 + num_atoms, len(lines) - i)):
                        parts = lines[i + j].split()
                        if len(parts) >= 4:
                            symbol = parts[0]
                            x, y, z = float(parts[1]), float(parts[2]), float(parts[3])
                            
                            atom = {
                                'id': j - 1,
                                'symbol': symbol,
                                'x': x, 'y': y, 'z': z,
                                'charge': 0.0
                            }
                            
                            structure['atoms'].append(atom)
                            structure['coordinates'].append([x, y, z])
                            structure['atom_types'].append(self._convert_symbol_to_type(symbol))
                    
                    if structure['atoms']:
                        structure['coordinates'] = np.array(structure['coordinates'])
                        structure['atom_types'] = np.array(structure['atom_types'])
                        structures.append(structure)
                    
                    i += 2 + num_atoms
                else:
                    i += 1
                    
            logger.info(f"成功解析XYZ文件 {file_path}: 找到 {len(structures)} 个结构")
            
        except Exception as e:
            logger.error(f"解析XYZ文件失败: {str(e)}")
            return []
        
        return structures

    def _parse_pdb(self, file_path):
        """解析PDB格式文件"""
        structures = []
        current_structure = {
            'description': f"Structure from {os.path.basename(file_path)}",
            'atoms': [],
            'coordinates': [],
            'atom_types': [],
            'energy': None
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.startswith('ATOM') or line.startswith('HETATM'):
                        # PDB格式固定位置解析
                        atom_id = int(line[6:11].strip())
                        atom_name = line[12:16].strip()
                        x = float(line[30:38].strip())
                        y = float(line[38:46].strip())
                        z = float(line[46:54].strip())
                        
                        # 提取元素符号
                        if len(line) > 76:
                            symbol = line[76:78].strip()
                        else:
                            symbol = atom_name[0]
                        
                        atom = {
                            'id': atom_id,
                            'symbol': symbol,
                            'x': x, 'y': y, 'z': z,
                            'charge': 0.0
                        }
                        
                        current_structure['atoms'].append(atom)
                        current_structure['coordinates'].append([x, y, z])
                        current_structure['atom_types'].append(self._convert_symbol_to_type(symbol))
                        
                    elif line.startswith('END'):
                        if current_structure['atoms']:
                            current_structure['coordinates'] = np.array(current_structure['coordinates'])
                            current_structure['atom_types'] = np.array(current_structure['atom_types'])
                            structures.append(current_structure)
                        break
            
            # 如果没有END标记，添加当前结构
            if current_structure['atoms'] and not structures:
                current_structure['coordinates'] = np.array(current_structure['coordinates'])
                current_structure['atom_types'] = np.array(current_structure['atom_types'])
                structures.append(current_structure)
                
            logger.info(f"成功解析PDB文件 {file_path}: 找到 {len(structures)} 个结构")
            
        except Exception as e:
            logger.error(f"解析PDB文件失败: {str(e)}")
            return []
        
        return structures

    def _parse_json(self, file_path):
        """解析JSON格式文件"""
        try:
            import json
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logger.info(f"成功解析JSON文件 {file_path}")
            return data
            
        except Exception as e:
            logger.error(f"解析JSON文件失败: {str(e)}")
            return {}

    def _parse_csv(self, file_path):
        """解析CSV格式文件"""
        try:
            import pandas as pd
            data = pd.read_csv(file_path)
            
            logger.info(f"成功解析CSV文件 {file_path}: {data.shape[0]} 行, {data.shape[1]} 列")
            return data.to_dict('records')
            
        except Exception as e:
            logger.error(f"解析CSV文件失败: {str(e)}")
            return []

    def _parse_yaml(self, file_path):
        """解析YAML格式文件"""
        try:
            import yaml
            with open(file_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            
            logger.info(f"成功解析YAML文件 {file_path}")
            return data
            
        except ImportError:
            logger.warning("PyYAML未安装，无法解析YAML文件")
            return {}
        except Exception as e:
            logger.error(f"解析YAML文件失败: {str(e)}")
            return {}

    def _parse_data_file(self, file_path):
        """解析通用数据文件(.dat, .txt)"""
        data = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    
                    # 尝试解析数值数据
                    parts = line.split()
                    try:
                        numbers = [float(p) for p in parts]
                        data.append(numbers)
                    except ValueError:
                        # 如果不是数值，保存为字符串
                        data.append(parts)
            
            logger.info(f"成功解析数据文件 {file_path}: {len(data)} 行数据")
            
        except Exception as e:
            logger.error(f"解析数据文件失败: {str(e)}")
            return []
        
        return data

    def _parse_control(self, file_path):
        """解析ReaxFF控制文件"""
        control_params = {}
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('!') or line.startswith('#'):
                        continue
                    
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        
                        # 尝试转换数值
                        try:
                            if '.' in value:
                                control_params[key] = float(value)
                            else:
                                control_params[key] = int(value)
                        except ValueError:
                            control_params[key] = value
            
            logger.info(f"成功解析控制文件 {file_path}: {len(control_params)} 个参数")
            
        except Exception as e:
            logger.error(f"解析控制文件失败: {str(e)}")
            return {}
        
        return control_params

    def _convert_symbol_to_type(self, symbol):
        """将原子符号转换为数值类型

        Args:
            symbol (str): 原子符号

        Returns:
            int: 原子类型的数值表示
        """
        # 定义原子符号到数值的映射
        symbol_to_type = {
            'H': 1, 'C': 6, 'N': 7, 'O': 8, 'F': 9,
            'Si': 14, 'P': 15, 'S': 16, 'Cl': 17,
            'Co': 27, 'Ni': 28, 'Cu': 29, 'Zn': 30
        }

        # 移除下标并获取基本符号
        base_symbol = ''.join(c for c in symbol if not c.isdigit())

        # 返回映射的数值，如果没有找到则返回0
        return symbol_to_type.get(base_symbol, 0)

    def _parse_trainset(self, file_path):
        """解析训练集文件(trainset.in)"""
        training_data = {
            'structures': [],
            'energies': [],
            'forces': []
        }

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            current_section = None
            current_data = {}

            for line in lines:
                line = line.strip()
                if not line or line.startswith("#"):
                    continue

                if line.startswith("ENERGY"):
                    if current_data:
                        training_data['energies'].append(current_data)
                    current_section = "energy"
                    current_data = {'type': 'energy'}
                elif line.startswith("FORCE"):
                    if current_data:
                        training_data['forces'].append(current_data)
                    current_section = "force"
                    current_data = {'type': 'force'}
                elif line.startswith("ENDENER") or line.startswith("ENDFOR"):
                    if current_data:
                        if current_section == "energy":
                            training_data['energies'].append(current_data)
                        elif current_section == "force":
                            training_data['forces'].append(current_data)
                    current_section = None
                    current_data = {}
                elif current_section:
                    # 解析数据行
                    parts = line.split()
                    if len(parts) >= 2:
                        try:
                            value = float(parts[0])
                            weight = float(parts[1])
                            current_data['value'] = value
                            current_data['weight'] = weight
                        except:
                            pass

        except Exception as e:
            print(f"解析trainset文件失败: {e}")
            return {'structures': [], 'energies': [], 'forces': []}

        return training_data

    def _parse_params(self, file_path):
        """解析参数文件 - 支持多种格式"""
        params = {}
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            param_count = 0
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line or line.startswith("#"):
                    continue

                # 移除注释部分
                if '!' in line:
                    line = line.split('!')[0].strip()

                parts = line.split()
                if len(parts) < 3:
                    continue

                try:
                    # 检测格式类型
                    if len(parts) >= 4 and not parts[0].isdigit():
                        # 格式1: param_name current_value min_value max_value
                        param_name = parts[0]
                        current_value = float(parts[1])
                        min_value = float(parts[2])
                        max_value = float(parts[3])
                    elif len(parts) >= 6 and all(p.replace('.', '').replace('-', '').isdigit() or p.replace('.', '').replace('-', '').replace('e', '').replace('E', '').replace('+', '').isdigit() for p in parts[:6]):
                        # 格式2: ReaxFF标准格式 section index1 index2 sensitivity low_value high_value
                        section = int(parts[0])
                        index1 = int(parts[1])
                        index2 = int(parts[2])
                        sensitivity = float(parts[3])
                        low_value = float(parts[4])
                        high_value = float(parts[5])

                        # 创建参数名
                        param_name = f"p_{section}_{index1}_{index2}"
                        current_value = (low_value + high_value) / 2  # 使用中值作为当前值
                        min_value = low_value
                        max_value = high_value
                    else:
                        continue

                    # 确保min <= max
                    if min_value > max_value:
                        min_value, max_value = max_value, min_value

                    params[param_name] = {
                        'value': current_value,
                        'min': min_value,
                        'max': max_value
                    }
                    param_count += 1

                except (ValueError, IndexError) as e:
                    logger.debug(f"跳过第{line_num}行: {line} (错误: {e})")
                    continue

            logger.info(f"成功解析参数文件 {file_path}: 找到 {len(params)} 个参数")

        except Exception as e:
            logger.error(f"解析参数文件失败: {str(e)}", exc_info=True)
            return {}

        return params

    def get_structure(self, name):
        """获取指定名称的结构"""
        return self.structures.get(name)

    def get_training_set(self, name):
        """获取指定名称的训练集"""
        return self.training_sets.get(name)

    def get_all_structures(self):
        """获取所有结构"""
        return self.structures

    def get_all_training_sets(self):
        """获取所有训练集"""
        return self.training_sets

    def get_all_parameters(self):
        """获取所有参数"""
        return self.parameters

    def get_control_files(self):
        """获取所有控制文件数据"""
        return getattr(self, 'control_files', {})
    
    def get_json_files(self):
        """获取所有JSON文件数据"""
        return getattr(self, 'json_files', {})
    
    def get_csv_files(self):
        """获取所有CSV文件数据"""
        return getattr(self, 'csv_files', {})
    
    def get_yaml_files(self):
        """获取所有YAML文件数据"""
        return getattr(self, 'yaml_files', {})
    
    def get_data_files(self):
        """获取所有数据文件内容"""
        return getattr(self, 'data_files', {})
    
    def get_file_by_type(self, file_type):
        """根据文件类型获取数据
        
        Args:
            file_type (str): 文件类型 ('control', 'json', 'csv', 'yaml', 'data')
            
        Returns:
            dict: 对应类型的文件数据
        """
        type_mapping = {
            'control': self.get_control_files(),
            'json': self.get_json_files(),
            'csv': self.get_csv_files(),
            'yaml': self.get_yaml_files(),
            'data': self.get_data_files(),
            'structures': self.get_all_structures(),
            'training_sets': self.get_all_training_sets(),
            'parameters': self.get_all_parameters()
        }
        return type_mapping.get(file_type, {})
    
    def get_supported_formats(self):
        """获取支持的文件格式列表"""
        return [
            'geo', 'xyz', 'pdb', 'cif', 'mol', 'sdf',
            'lammps', 'bgf', 'car', 'msi',
            'json', 'yaml', 'yml', 'toml', 'xml',
            'csv', 'dat', 'txt', 'out', 'log',
            'trainset', 'params', 'control', 'iopt'
        ]
    
    def export_data_summary(self):
        """导出数据摘要
        
        Returns:
            dict: 包含所有数据类型统计信息的字典
        """
        summary = {
            'structures': {
                'datasets': len(self.structures),
                'total_structures': sum(len(structures) for structures in self.structures.values())
            },
            'training_sets': len(self.training_sets),
            'parameters': len(self.parameters),
            'control_files': len(self.get_control_files()),
            'json_files': len(self.get_json_files()),
            'csv_files': len(self.get_csv_files()),
            'yaml_files': len(self.get_yaml_files()),
            'data_files': len(self.get_data_files())
        }
        return summary

    def calculate_energy(self, structure_name):
        """计算指定结构的能量

        Args:
            structure_name (str): 结构名称

        Returns:
            float: 计算得到的能量值
        """
        structure = self.get_structure(structure_name)
        if not structure:
            logger.error(f"未找到结构: {structure_name}")
            return None

        try:
            # 这里添加实际的能量计算逻辑
            # 示例: 使用简单的力场计算
            energy = self._calculate_structure_energy(structure)

            # 存储计算结果
            if structure_name not in self.calculation_results:
                self.calculation_results[structure_name] = {}
            self.calculation_results[structure_name]['energy'] = energy

            return energy

        except Exception as e:
            logger.error(f"计算能量失败: {str(e)}")
            return None

    def calculate_forces(self, structure_name):
        """计算指定结构的原子力

        Args:
            structure_name (str): 结构名称

        Returns:
            numpy.ndarray: 原子力数组
        """
        structure = self.get_structure(structure_name)
        if not structure:
            logger.error(f"未找到结构: {structure_name}")
            return None

        try:
            # 这里添加实际的力计算逻辑
            forces = self._calculate_structure_forces(structure)

            # 存储计算结果
            if structure_name not in self.calculation_results:
                self.calculation_results[structure_name] = {}
            self.calculation_results[structure_name]['forces'] = forces

            return forces

        except Exception as e:
            logger.error(f"计算原子力失败: {str(e)}")
            return None

    def save_ffield(self, file_path, structure_name=None):
        """保存力场文件

        Args:
            file_path (str): 保存路径
            structure_name (str, optional): 指定结构名称，如果不指定则保存所有结构的力场
        """
        try:
            # 收集要保存的力场数据
            ffield_data = {}
            if structure_name:
                if structure_name in self.calculation_results:
                    ffield_data[structure_name] = self.calculation_results[structure_name]
                else:
                    logger.error(f"未找到结构的计算结果: {structure_name}")
                    return False
            else:
                ffield_data = self.calculation_results

            # 保存力场文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write("# Generated ReaxFF force field file\n")
                f.write("# Date: " + time.strftime("%Y-%m-%d %H:%M:%S") + "\n\n")

                for struct_name, results in ffield_data.items():
                    f.write(f"# Structure: {struct_name}\n")
                    if 'energy' in results:
                        f.write(f"Energy: {results['energy']:.6f}\n")
                    if 'forces' in results:
                        f.write("Forces:\n")
                        for force in results['forces']:
                            f.write(f"{force[0]:.6f} {force[1]:.6f} {force[2]:.6f}\n")
                    f.write("\n")

            logger.info(f"成功保存力场文件: {file_path}")
            return True

        except Exception as e:
            logger.error(f"保存力场文件失败: {str(e)}")
            return False

    def _calculate_structure_energy(self, structure):
        """计算结构能量的内部方法"""
        # 这里实现具体的能量计算逻辑
        # 示例: 使用简单的距离求和
        energy = 0.0
        coords = structure['coordinates']
        for i in range(len(coords)):
            for j in range(i + 1, len(coords)):
                dist = np.linalg.norm(coords[i] - coords[j])
                energy += 1.0 / (dist + 1e-6)  # 简单的库仑势能
        return energy

    def _calculate_structure_forces(self, structure):
        """计算结构原子力的内部方法"""
        # 这里实现具体的力计算逻辑
        # 示例: 使用简单的距离导数
        coords = structure['coordinates']
        forces = np.zeros_like(coords)
        for i in range(len(coords)):
            for j in range(len(coords)):
                if i != j:
                    diff = coords[i] - coords[j]
                    dist = np.linalg.norm(diff)
                    force = -diff / (dist + 1e-6)**3  # 简单的库仑力
                    forces[i] += force
        return forces
