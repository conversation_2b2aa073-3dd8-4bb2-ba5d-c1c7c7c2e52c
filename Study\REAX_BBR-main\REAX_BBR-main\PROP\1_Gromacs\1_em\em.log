                      :-) GROMACS - gmx mdrun, 2022.2 (-:

Copyright 1991-2022 The GROMACS Authors.
GROMACS is free software; you can redistribute it and/or modify it
under the terms of the GNU Lesser General Public License
as published by the Free Software Foundation; either version 2.1
of the License, or (at your option) any later version.

                         Current GROMACS contributors:
       <PERSON>           Vytas Gapsys       
       Gaurav Garg           <PERSON>           M. Eric <PERSON>gang              <PERSON>rz              <PERSON>el    
     Alessandra Villa      Sebastian Wingbermuehle        Artem Z<PERSON>urov       

                         Previous GROMACS contributors:
        <PERSON><PERSON> Apol             Rossen Apostolov           <PERSON>       
  Herman J.C. Berendsen          Par Bjelkmar           <PERSON>lav <PERSON>dert van Buuren          Carlo Camilloni           Rudi <PERSON>n      
      Anton Fe<PERSON>tra           Gerrit <PERSON>ro<PERSON>hof            Bert de Groot       
      Anca Hamuraru           <PERSON>         Aleksei Iupinov      
   <PERSON> Lemkul           Viveca <PERSON>           Vedran Miletic      
      Teemu Mu<PERSON>              Sand<PERSON> Pronk            Alexey Shvetsov      
      Alfons Sijbers            Peter Tieleman             Jon Vincent        
     Teemu Virolainen         Christian Wennberg           Maarten Wolf       

                  Coordinated by the GROMACS project leaders:
                    Paul Bauer, Berk Hess, and Erik Lindahl

GROMACS:      gmx mdrun, version 2022.2
Executable:   /home/<USER>/Programs/gromacs/bin/gmx_mpi
Data prefix:  /home/<USER>/Programs/gromacs
Working dir:  /home/<USER>/Desktop/PROP/1_Gromacs/1_em
Process ID:   10353
Command line:
  gmx_mpi mdrun -v -s em.tpr -deffnm em -ntomp 4 -pin on -pinoffset 1 -pinstride 2

GROMACS version:    2022.2
Precision:          mixed
Memory model:       64 bit
MPI library:        MPI
OpenMP support:     enabled (GMX_OPENMP_MAX_THREADS = 128)
GPU support:        CUDA
SIMD instructions:  AVX2_256
CPU FFT library:    fftw-3.3.8-sse2-avx
GPU FFT library:    cuFFT
RDTSCP usage:       enabled
TNG support:        enabled
Hwloc support:      disabled
Tracing support:    disabled
C compiler:         /usr/bin/cc GNU 11.2.0
C compiler flags:   -mavx2 -mfma -Wno-missing-field-initializers -fexcess-precision=fast -funroll-all-loops -O3 -DNDEBUG
C++ compiler:       /usr/bin/c++ GNU 11.2.0
C++ compiler flags: -mavx2 -mfma -Wno-missing-field-initializers -fexcess-precision=fast -funroll-all-loops -fopenmp -O3 -DNDEBUG
CUDA compiler:      /usr/local/cuda/bin/nvcc nvcc: NVIDIA (R) Cuda compiler driver;Copyright (c) 2005-2022 NVIDIA Corporation;Built on Wed_Jun__8_16:49:14_PDT_2022;Cuda compilation tools, release 11.7, V11.7.99;Build cuda_11.7.r11.7/compiler.31442593_0
CUDA compiler flags:-std=c++17;-gencode;arch=compute_35,code=sm_35;-gencode;arch=compute_37,code=sm_37;-gencode;arch=compute_50,code=sm_50;-gencode;arch=compute_52,code=sm_52;-gencode;arch=compute_60,code=sm_60;-gencode;arch=compute_61,code=sm_61;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-Wno-deprecated-gpu-targets;-gencode;arch=compute_53,code=sm_53;-gencode;arch=compute_80,code=sm_80;-use_fast_math;-D_FORCE_INLINES;-mavx2 -mfma -Wno-missing-field-initializers -fexcess-precision=fast -funroll-all-loops -fopenmp -O3 -DNDEBUG
CUDA driver:        11.70
CUDA runtime:       N/A


Running on 1 node with total 16 cores, 24 processing units (GPU detection failed)
Hardware detected on host jw (the node of MPI rank 0):
  CPU info:
    Vendor: Intel
    Brand:  12th Gen Intel(R) Core(TM) i9-12900KF
    Family: 6   Model: 151   Stepping: 2
    Features: aes apic avx avx2 clfsh cmov cx8 cx16 f16c fma htt intel lahf mmx msr nonstop_tsc pcid pclmuldq pdcm pdpe1gb popcnt pse rdrnd rdtscp sha sse2 sse3 sse4.1 sse4.2 ssse3 tdt x2apic
  Hardware topology: Basic
    Packages, cores, and logical processors:
    [indices refer to OS logical processors]
      Package  0: [   0   1] [   2   3] [   4   5] [   6   7] [   8   9] [  10  11] [  12  13] [  14  15] [  16] [  17] [  18] [  19] [  20] [  21] [  22] [  23]
    CPU limit set by OS: -1   Recommended max number of threads: 24


++++ PLEASE READ AND CITE THE FOLLOWING REFERENCE ++++
M. J. Abraham, T. Murtola, R. Schulz, S. Páll, J. C. Smith, B. Hess, E.
Lindahl
GROMACS: High performance molecular simulations through multi-level
parallelism from laptops to supercomputers
SoftwareX 1 (2015) pp. 19-25
-------- -------- --- Thank You --- -------- --------


++++ PLEASE READ AND CITE THE FOLLOWING REFERENCE ++++
S. Páll, M. J. Abraham, C. Kutzner, B. Hess, E. Lindahl
Tackling Exascale Software Challenges in Molecular Dynamics Simulations with
GROMACS
In S. Markidis & E. Laure (Eds.), Solving Software Challenges for Exascale 8759 (2015) pp. 3-27
-------- -------- --- Thank You --- -------- --------


++++ PLEASE READ AND CITE THE FOLLOWING REFERENCE ++++
S. Pronk, S. Páll, R. Schulz, P. Larsson, P. Bjelkmar, R. Apostolov, M. R.
Shirts, J. C. Smith, P. M. Kasson, D. van der Spoel, B. Hess, and E. Lindahl
GROMACS 4.5: a high-throughput and highly parallel open source molecular
simulation toolkit
Bioinformatics 29 (2013) pp. 845-54
-------- -------- --- Thank You --- -------- --------


++++ PLEASE READ AND CITE THE FOLLOWING REFERENCE ++++
B. Hess and C. Kutzner and D. van der Spoel and E. Lindahl
GROMACS 4: Algorithms for highly efficient, load-balanced, and scalable
molecular simulation
J. Chem. Theory Comput. 4 (2008) pp. 435-447
-------- -------- --- Thank You --- -------- --------


++++ PLEASE READ AND CITE THE FOLLOWING REFERENCE ++++
D. van der Spoel, E. Lindahl, B. Hess, G. Groenhof, A. E. Mark and H. J. C.
Berendsen
GROMACS: Fast, Flexible and Free
J. Comp. Chem. 26 (2005) pp. 1701-1719
-------- -------- --- Thank You --- -------- --------


++++ PLEASE READ AND CITE THE FOLLOWING REFERENCE ++++
E. Lindahl and B. Hess and D. van der Spoel
GROMACS 3.0: A package for molecular simulation and trajectory analysis
J. Mol. Mod. 7 (2001) pp. 306-317
-------- -------- --- Thank You --- -------- --------


++++ PLEASE READ AND CITE THE FOLLOWING REFERENCE ++++
H. J. C. Berendsen, D. van der Spoel and R. van Drunen
GROMACS: A message-passing parallel molecular dynamics implementation
Comp. Phys. Comm. 91 (1995) pp. 43-56
-------- -------- --- Thank You --- -------- --------


++++ PLEASE CITE THE DOI FOR THIS VERSION OF GROMACS ++++
https://doi.org/10.5281/zenodo.6637571
-------- -------- --- Thank You --- -------- --------

Input Parameters:
   integrator                     = steep
   tinit                          = 0
   dt                             = 0.001
   nsteps                         = 50000
   init-step                      = 0
   simulation-part                = 1
   mts                            = false
   comm-mode                      = Linear
   nstcomm                        = 100
   bd-fric                        = 0
   ld-seed                        = -328740944
   emtol                          = 1000
   emstep                         = 0.01
   niter                          = 20
   fcstep                         = 0
   nstcgsteep                     = 1000
   nbfgscorr                      = 10
   rtpi                           = 0.05
   nstxout                        = 0
   nstvout                        = 0
   nstfout                        = 0
   nstlog                         = 1000
   nstcalcenergy                  = 100
   nstenergy                      = 1000
   nstxout-compressed             = 0
   compressed-x-precision         = 1000
   cutoff-scheme                  = Verlet
   nstlist                        = 1
   pbc                            = xyz
   periodic-molecules             = false
   verlet-buffer-tolerance        = 0.005
   rlist                          = 0.9
   coulombtype                    = PME
   coulomb-modifier               = Potential-shift
   rcoulomb-switch                = 0
   rcoulomb                       = 0.9
   epsilon-r                      = 1
   epsilon-rf                     = inf
   vdw-type                       = Cut-off
   vdw-modifier                   = Potential-shift
   rvdw-switch                    = 0
   rvdw                           = 0.9
   DispCorr                       = No
   table-extension                = 1
   fourierspacing                 = 0.12
   fourier-nx                     = 20
   fourier-ny                     = 20
   fourier-nz                     = 20
   pme-order                      = 4
   ewald-rtol                     = 1e-05
   ewald-rtol-lj                  = 0.001
   lj-pme-comb-rule               = Geometric
   ewald-geometry                 = 3d
   epsilon-surface                = 0
   tcoupl                         = No
   nsttcouple                     = -1
   nh-chain-length                = 0
   print-nose-hoover-chain-variables = false
   pcoupl                         = No
   pcoupltype                     = Isotropic
   nstpcouple                     = -1
   tau-p                          = 1
   compressibility (3x3):
      compressibility[    0]={ 0.00000e+00,  0.00000e+00,  0.00000e+00}
      compressibility[    1]={ 0.00000e+00,  0.00000e+00,  0.00000e+00}
      compressibility[    2]={ 0.00000e+00,  0.00000e+00,  0.00000e+00}
   ref-p (3x3):
      ref-p[    0]={ 0.00000e+00,  0.00000e+00,  0.00000e+00}
      ref-p[    1]={ 0.00000e+00,  0.00000e+00,  0.00000e+00}
      ref-p[    2]={ 0.00000e+00,  0.00000e+00,  0.00000e+00}
   refcoord-scaling               = No
   posres-com (3):
      posres-com[0]= 0.00000e+00
      posres-com[1]= 0.00000e+00
      posres-com[2]= 0.00000e+00
   posres-comB (3):
      posres-comB[0]= 0.00000e+00
      posres-comB[1]= 0.00000e+00
      posres-comB[2]= 0.00000e+00
   QMMM                           = false
qm-opts:
   ngQM                           = 0
   constraint-algorithm           = Lincs
   continuation                   = false
   Shake-SOR                      = false
   shake-tol                      = 0.0001
   lincs-order                    = 4
   lincs-iter                     = 1
   lincs-warnangle                = 30
   nwall                          = 0
   wall-type                      = 9-3
   wall-r-linpot                  = -1
   wall-atomtype[0]               = -1
   wall-atomtype[1]               = -1
   wall-density[0]                = 0
   wall-density[1]                = 0
   wall-ewald-zfac                = 3
   pull                           = false
   awh                            = false
   rotation                       = false
   interactiveMD                  = false
   disre                          = No
   disre-weighting                = Conservative
   disre-mixed                    = false
   dr-fc                          = 1000
   dr-tau                         = 0
   nstdisreout                    = 100
   orire-fc                       = 0
   orire-tau                      = 0
   nstorireout                    = 100
   free-energy                    = no
   cos-acceleration               = 0
   deform (3x3):
      deform[    0]={ 0.00000e+00,  0.00000e+00,  0.00000e+00}
      deform[    1]={ 0.00000e+00,  0.00000e+00,  0.00000e+00}
      deform[    2]={ 0.00000e+00,  0.00000e+00,  0.00000e+00}
   simulated-tempering            = false
   swapcoords                     = no
   userint1                       = 0
   userint2                       = 0
   userint3                       = 0
   userint4                       = 0
   userreal1                      = 0
   userreal2                      = 0
   userreal3                      = 0
   userreal4                      = 0
   applied-forces:
     electric-field:
       x:
         E0                       = 0
         omega                    = 0
         t0                       = 0
         sigma                    = 0
       y:
         E0                       = 0
         omega                    = 0
         t0                       = 0
         sigma                    = 0
       z:
         E0                       = 0
         omega                    = 0
         t0                       = 0
         sigma                    = 0
     density-guided-simulation:
       active                     = false
       group                      = protein
       similarity-measure         = inner-product
       atom-spreading-weight      = unity
       force-constant             = 1e+09
       gaussian-transform-spreading-width = 0.2
       gaussian-transform-spreading-range-in-multiples-of-width = 4
       reference-density-filename = reference.mrc
       nst                        = 1
       normalize-densities        = true
       adaptive-force-scaling     = false
       adaptive-force-scaling-time-constant = 4
       shift-vector               = 
       transformation-matrix      = 
     qmmm-cp2k:
       active                     = false
       qmgroup                    = System
       qmmethod                   = PBE
       qmfilenames                = 
       qmcharge                   = 0
       qmmultiplicity             = 1
grpopts:
   nrdf:          90
   ref-t:           0
   tau-t:           0
annealing:          No
annealing-npoints:           0
   acc:	           0           0           0
   nfreeze:           N           N           N
   energygrp-flags[  0]: 0

When checking whether update groups are usable:
  No constraints or virtual sites are in use, so it is best not to use update groups

Initializing Domain Decomposition on 1 ranks
NOTE: disabling dynamic load balancing as it is only supported with dynamics, not with integrator 'steep'.
Dynamic load balancing: off
Minimum cell size due to atom displacement: 0.000 nm
Initial maximum distances in bonded interactions:
    two-body bonded interactions: 0.377 nm, LJ-14, atoms 3 12
  multi-body bonded interactions: 0.377 nm, Proper Dih., atoms 3 12
Minimum cell size due to bonded interactions: 0.414 nm
Using 0 separate PME ranks because: there are too few total ranks for efficient splitting
Optimizing the DD grid for 1 cells with a minimum initial size of 0.414 nm
The maximum allowed number of cells is: X 4 Y 4 Z 4
Domain decomposition grid 1 x 1 x 1, separate PME ranks 0
PME domain decomposition: 1 x 1 x 1

The initial number of communication pulses is:
The initial domain decomposition cell size is:

The maximum allowed distance for atoms involved in interactions is:
                 non-bonded interactions           0.900 nm
            two-body bonded interactions  (-rdd)   0.900 nm
          multi-body bonded interactions  (-rdd)   0.900 nm

Using 1 MPI process
Using 4 OpenMP threads 

Applying core pinning offset 1
Pinning threads with a user-specified logical cpu stride of 2
System total charge: -0.000
Will do PME sum in reciprocal space for electrostatic interactions.

++++ PLEASE READ AND CITE THE FOLLOWING REFERENCE ++++
U. Essmann, L. Perera, M. L. Berkowitz, T. Darden, H. Lee and L. G. Pedersen 
A smooth particle mesh Ewald method
J. Chem. Phys. 103 (1995) pp. 8577-8592
-------- -------- --- Thank You --- -------- --------

Using a Gaussian width (1/beta) of 0.288146 nm for Ewald
Potential shift: LJ r^-12: -3.541e+00 r^-6: -1.882e+00, Ewald -1.111e-05
Initialized non-bonded Coulomb Ewald tables, spacing: 8.85e-04 size: 1018

Generated table with 950 data points for 1-4 COUL.
Tabscale = 500 points/nm
Generated table with 950 data points for 1-4 LJ6.
Tabscale = 500 points/nm
Generated table with 950 data points for 1-4 LJ12.
Tabscale = 500 points/nm


Using SIMD 4x8 nonbonded short-range kernels

Using a 4x8 pair-list setup:
  updated every 1 steps, buffer 0.000 nm, rlist 0.900 nm

Using Geometric Lennard-Jones combination rule
Removing pbc first time

Linking all bonded interactions to atoms


Note that activating steepest-descent energy minimization via the integrator .mdp option and the command gmx mdrun may be available in a different form in a future version of GROMACS, e.g. gmx minimize and an .mdp option.
Initiating Steepest Descents

Atom distribution over 1 domains: av 31 stddev 0 min 31 max 31
Started Steepest Descents on rank 0 Mon Sep 25 14:07:06 2023


Steepest Descents:
   Tolerance (Fmax)   =  1.00000e+03
   Number of steps    =        50000
           Step           Time
              0        0.00000

   Energies (kJ/mol)
        G96Bond       G96Angle    Proper Dih.          LJ-14     Coulomb-14
    7.16549e-01    2.19306e+00    6.95564e-02   -1.75525e+00   -1.27804e+02
        LJ (SR)   Coulomb (SR)   Coul. recip.      Potential Pressure (bar)
    3.87114e+02    9.60472e+01    9.88348e+00    3.66465e+02    3.48642e+03

           Step           Time
              1        1.00000

   Energies (kJ/mol)
        G96Bond       G96Angle    Proper Dih.          LJ-14     Coulomb-14
    8.07976e+00    3.22059e+00    1.27489e-01   -1.95048e+00   -1.27756e+02
        LJ (SR)   Coulomb (SR)   Coul. recip.      Potential Pressure (bar)
    1.40134e+02    9.71740e+01    8.58566e+00    1.27616e+02    1.67025e+03

           Step           Time
              2        2.00000

   Energies (kJ/mol)
        G96Bond       G96Angle    Proper Dih.          LJ-14     Coulomb-14
    1.17684e+01    1.30010e+01    2.76705e-01   -2.11767e+00   -1.27568e+02
        LJ (SR)   Coulomb (SR)   Coul. recip.      Potential Pressure (bar)
    5.21131e+01    9.77931e+01    7.65296e+00    5.29192e+01    8.61377e+02

           Step           Time
              3        3.00000

           Step           Time
              4        4.00000

   Energies (kJ/mol)
        G96Bond       G96Angle    Proper Dih.          LJ-14     Coulomb-14
    1.08752e+01    7.19352e+00    3.04595e-01   -2.01605e+00   -1.27567e+02
        LJ (SR)   Coulomb (SR)   Coul. recip.      Potential Pressure (bar)
    3.72488e+01    9.68445e+01    8.48023e+00    3.13642e+01    9.23884e+02

           Step           Time
              5        5.00000

           Step           Time
              6        6.00000

   Energies (kJ/mol)
        G96Bond       G96Angle    Proper Dih.          LJ-14     Coulomb-14
    5.81350e+00    5.80445e+00    3.50307e-01   -2.01765e+00   -1.27660e+02
        LJ (SR)   Coulomb (SR)   Coul. recip.      Potential Pressure (bar)
    3.26836e+01    9.69043e+01    8.43172e+00    2.03100e+01    3.41152e+02

           Step           Time
              7        7.00000

           Step           Time
              8        8.00000

   Energies (kJ/mol)
        G96Bond       G96Angle    Proper Dih.          LJ-14     Coulomb-14
    2.57709e+00    5.37467e+00    3.60135e-01   -2.01597e+00   -1.27604e+02
        LJ (SR)   Coulomb (SR)   Coul. recip.      Potential Pressure (bar)
    2.92238e+01    9.67749e+01    8.50950e+00    1.32001e+01    6.50355e+02

           Step           Time
              9        9.00000

   Energies (kJ/mol)
        G96Bond       G96Angle    Proper Dih.          LJ-14     Coulomb-14
    3.06711e+00    4.58498e+00    3.77217e-01   -2.01899e+00   -1.27535e+02
        LJ (SR)   Coulomb (SR)   Coul. recip.      Potential Pressure (bar)
    2.52961e+01    9.66094e+01    8.60210e+00    8.98309e+00    2.85808e+02

           Step           Time
             10       10.00000

   Energies (kJ/mol)
        G96Bond       G96Angle    Proper Dih.          LJ-14     Coulomb-14
    8.07125e+00    3.93016e+00    3.91047e-01   -2.02268e+00   -1.27478e+02
        LJ (SR)   Coulomb (SR)   Coul. recip.      Potential Pressure (bar)
    2.09110e+01    9.64762e+01    8.67645e+00    8.95518e+00    7.02499e+02

           Step           Time
             11       11.00000

   Energies (kJ/mol)
        G96Bond       G96Angle    Proper Dih.          LJ-14     Coulomb-14
    8.12243e+00    3.52999e+00    3.99329e-01   -2.02938e+00   -1.27429e+02
        LJ (SR)   Coulomb (SR)   Coul. recip.      Potential Pressure (bar)
    1.94404e+01    9.63861e+01    8.72119e+00    7.14064e+00    3.38062e+01

           Step           Time
             12       12.00000

           Step           Time
             13       13.00000

   Energies (kJ/mol)
        G96Bond       G96Angle    Proper Dih.          LJ-14     Coulomb-14
    9.18962e-01    3.33715e+00    4.03201e-01   -2.03188e+00   -1.27412e+02
        LJ (SR)   Coulomb (SR)   Coul. recip.      Potential Pressure (bar)
    1.77778e+01    9.63471e+01    8.74245e+00   -1.91699e+00    4.34553e+02

           Step           Time
             14       14.00000

   Energies (kJ/mol)
        G96Bond       G96Angle    Proper Dih.          LJ-14     Coulomb-14
    3.39981e+00    2.76355e+00    4.15575e-01   -2.04411e+00   -1.27343e+02
        LJ (SR)   Coulomb (SR)   Coul. recip.      Potential Pressure (bar)
    1.46046e+01    9.62164e+01    8.80852e+00   -3.17844e+00    8.44223e+01

           Step           Time
             15       15.00000

           Step           Time
             16       16.00000

   Energies (kJ/mol)
        G96Bond       G96Angle    Proper Dih.          LJ-14     Coulomb-14
    5.94703e-01    2.63582e+00    4.18188e-01   -2.04753e+00   -1.27329e+02
        LJ (SR)   Coulomb (SR)   Coul. recip.      Potential Pressure (bar)
    1.34383e+01    9.61896e+01    8.82240e+00   -7.27763e+00    3.57444e+02


Steepest Descents converged to Fmax < 1000 in 17 steps
Potential Energy  = -7.2776260e+00
Maximum force     =  8.8790015e+02 on atom 19
Norm of force     =  2.3219901e+02

    D O M A I N   D E C O M P O S I T I O N   S T A T I S T I C S

 av. #atoms communicated per step for force:  2 x 0.0

Finished mdrun on rank 0 Mon Sep 25 14:07:06 2023

