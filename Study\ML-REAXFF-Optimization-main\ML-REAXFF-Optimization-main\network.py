from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Dense

def model_v_1(input_dim,output_dim):

    model = Sequential()
    model.add(<PERSON><PERSON>(300, input_dim=input_dim, activation='relu'))
    model.add(<PERSON><PERSON>(300, activation='relu'))
    model.add(<PERSON><PERSON>(300, activation='relu'))
    model.add(Den<PERSON>(output_dim, activation='linear'))

    return model


def model_v_2(input_dim,output_dim):
    model = Sequential()
    model.add(Den<PERSON>(500, input_dim=input_dim, activation='relu'))
    model.add(<PERSON><PERSON>(500, activation='relu'))
    model.add(<PERSON><PERSON>(500, activation='relu'))
    model.add(<PERSON><PERSON>(500, activation='relu'))
    model.add(Dense(output_dim, activation='linear'))

    return model


def model_v_3(input_dim,output_dim):
    model = Sequential()
    model.add(Dense(300, input_dim=input_dim, activation='relu'))
    model.add(<PERSON><PERSON>(300, activation='relu'))
    model.add(<PERSON><PERSON>(300, activation='relu'))
    model.add(Dense(300, activation='relu'))
    model.add(Den<PERSON>(300, activation='relu'))
    model.add(<PERSON><PERSON>(output_dim, activation='linear'))

    return model

def model_v_4(input_dim,output_dim):
    model = Sequential()
    model.add(Dense(500, input_dim=input_dim, activation='relu'))
    model.add(Dense(500, activation='relu'))
    model.add(Dense(500, activation='relu'))
    model.add(Dense(500, activation='relu'))
    model.add(Dense(500, activation='relu'))
    model.add(Dense(500, activation='relu'))
    model.add(Dense(500, activation='relu'))
    model.add(Dense(output_dim, activation='linear'))

def model_v_5(input_dim, output_dim):
    model = Sequential()
    model.add(Dense(500, input_dim=input_dim, activation='relu'))
    model.add(Dense(500, activation='relu'))
    model.add(Dense(1000, activation='relu'))
    model.add(Dense(1000, activation='relu'))
    model.add(Dense(1000, activation='relu'))
    model.add(Dense(1000, activation='relu'))
    model.add(Dense(1000, activation='relu'))
    model.add(Dense(1000, activation='relu'))
    model.add(Dense(1000, activation='relu'))
    model.add(Dense(1000, activation='relu'))
    model.add(Dense(1000, activation='relu'))
    model.add(Dense(1000, activation='relu'))
    model.add(Dense(500, activation='relu'))
    model.add(Dense(500, activation='relu'))
    model.add(Dense(output_dim, activation='linear'))

    return model