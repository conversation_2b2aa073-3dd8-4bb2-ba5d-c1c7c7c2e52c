Siesta Version  : v4.1-b4
Architecture    : GNU
Compiler version: GNU Fortran (Uos 8.3.0.3-3+rebuild) 8.3.0
Compiler flags  : mpif90 -O2 -fPIC -ftree-vectorize
PP flags        : -DMPI  -DFC_HAVE_ABORT -DGRID_DP -DPHI_GRID_SP -DSIESTA__DIAG_2STAGE
Libraries       : libsiestaLAPACK.a libsiestaLAPACK.a -lpthread -L/home/<USER>/siesta/mathlib/scaalapack -lscalapack -L/home/<USER>/siesta/mathlib/BLACS/LIB -lblacsF77init -lblacsCinit -lblacs  -L/home/<USER>/siesta/mathlib/lapack -lrefblas
PARALLEL version

* Running on 8 nodes in parallel
>> Start of run:  27-MAR-2022   7:23:08

                           ***********************       
                           *  WELCOME TO SIESTA  *       
                           ***********************       

reinit: Reading from standard input
reinit: Dumped input in INPUT_TMP.35700
************************** Dump of input data file ****************************
################## Species and atoms ##################
SystemName       siesta
SystemLabel      siesta
NumberOfSpecies   1
NumberOfAtoms     4
%block ChemicalSpeciesLabel
1 1 H
%endblock ChemicalSpeciesLabel
SolutionMethod   Diagon # ## OrderN or Diagon
MaxSCFIterations 500
PAO.BasisType    split
%block PAO.Basis
H     2
 n=1    0    2  S .7020340
   4.4302740  0.0
   1.000   1.000
 n=2    1    1
   4.7841521
   1.000
%endblock PAO.Basis
SpinPolarized    F
DM.MixingWeight      0.4
DM.NumberPulay       9
DM.Tolerance         1.d-4
################### FUNCTIONAL ###################
XC.functional    GGA    # Exchange-correlation functional type
XC.Authors       PBE    # Particular parametrization of xc func
MeshCutoff       200. Ry # Equivalent planewave cutoff for the grid
KgridCutoff      12.000000 Ang
WriteCoorInitial T
WriteCoorXmol    T
WriteMDhistory   T
WriteMullikenPop 1
WriteForces      T
###################  GEOMETRY  ###################
LatticeConstant  1.00 Ang
%block LatticeVectors
12.0 0.0 0.0
0.0 12.0 0.0
0.0 0.0 12.0
%endblock LatticeVectors
AtomicCoordinatesFormat Ang
%block AtomicCoordinatesAndAtomicSpecies
3.917083774935344 3.69373315051698 4.12065573346065 1
4.414896644052805 3.520936319033012 4.593231714151679 1
2.3238480438104663 4.15787368491792 3.004334288949236 1
1.8623427134210813 4.2871618055328105 2.4890536468869993 1
%endblock AtomicCoordinatesAndAtomicSpecies
************************** End of input data file *****************************

reinit: -----------------------------------------------------------------------
reinit: System Name: siesta
reinit: -----------------------------------------------------------------------
reinit: System Label: siesta
reinit: -----------------------------------------------------------------------

initatom: Reading input for the pseudopotentials and atomic orbitals ----------
Species number:   1 Atomic number:    1 Label: H

Ground state valence configuration:   1s01
Reading pseudopotential information in formatted form from H.psf

Valence configuration for pseudopotential generation:
1s( 1.00) rc: 1.25
2p( 0.00) rc: 1.25
3d( 0.00) rc: 1.25
4f( 0.00) rc: 1.25
For H, standard SIESTA heuristics set lmxkb to 2
 (one more than the basis l, including polarization orbitals).
Use PS.lmax or PS.KBprojectors blocks to override.

<basis_specs>
===============================================================================
H                    Z=   1    Mass=  1.0100        Charge= 0.17977+309
Lmxo=1 Lmxkb= 2    BasisType=split      Semic=F
L=0  Nsemic=0  Cnfigmx=1
          n=1  nzeta=2  polorb=0
            splnorm:   0.70203    
               vcte:    0.0000    
               rinn:    0.0000    
               qcoe:    0.0000    
               qyuk:    0.0000    
               qwid:   0.10000E-01
                rcs:    4.4303      0.0000    
            lambdas:    1.0000      1.0000    
L=1  Nsemic=0  Cnfigmx=2
          n=1  nzeta=1  polorb=0
            splnorm:   0.15000    
               vcte:    0.0000    
               rinn:    0.0000    
               qcoe:    0.0000    
               qyuk:    0.0000    
               qwid:   0.10000E-01
                rcs:    4.7842    
            lambdas:    1.0000    
-------------------------------------------------------------------------------
L=0  Nkbl=1  erefs: 0.17977+309
L=1  Nkbl=1  erefs: 0.17977+309
L=2  Nkbl=1  erefs: 0.17977+309
===============================================================================
</basis_specs>

atom: Called for H                     (Z =   1)

read_vps: Pseudopotential generation method:
read_vps: ATM3.3    Troullier-Martins                       
Total valence charge:    1.00000

xc_check: Exchange-correlation functional:
xc_check: GGA Perdew, Burke & Ernzerhof 1996
V l=0 = -2*Zval/r beyond r=  1.2343
V l=1 = -2*Zval/r beyond r=  1.2189
V l=2 = -2*Zval/r beyond r=  1.2189
All V_l potentials equal beyond r=  1.2343
This should be close to max(r_c) in ps generation
All pots = -2*Zval/r beyond r=  1.2343

VLOCAL1: 99.0% of the norm of Vloc inside     28.493 Ry
VLOCAL1: 99.9% of the norm of Vloc inside     64.935 Ry
atom: Maximum radius for 4*pi*r*r*local-pseudopot. charge    1.45251
atom: Maximum radius for r*vlocal+2*Zval:    1.21892
GHOST: No ghost state for L =  0
GHOST: No ghost state for L =  1
GHOST: No ghost state for L =  2

KBgen: Kleinman-Bylander projectors: 
   l= 0   rc=  1.364359   el= -0.477200   Ekb= -2.021939   kbcos= -0.344793
   l= 1   rc=  1.434438   el=  0.001076   Ekb= -0.443447   kbcos= -0.022843
   l= 2   rc=  1.470814   el=  0.002010   Ekb= -0.140543   kbcos= -0.002863

KBgen: Total number of  Kleinman-Bylander projectors:    9
atom: -------------------------------------------------------------------------

atom: SANKEY-TYPE ORBITALS:
atom: Selected multiple-zeta basis: split     

SPLIT: Orbitals with angular momentum L= 0

SPLIT: Basis orbitals for state 1s

   izeta = 1
                 lambda =    1.000000
                     rc =    4.479210
                 energy =   -0.451136
                kinetic =    1.010721
    potential(screened) =   -1.461857
       potential(ionic) =   -1.992374

   izeta = 2
                 rmatch =    1.797005
              splitnorm =    0.702034
                 energy =    1.204812
                kinetic =    4.569535
    potential(screened) =   -3.364723
       potential(ionic) =   -3.917241

SPLIT: Orbitals with angular momentum L= 1

SPLIT: Basis orbitals for state 2p

   izeta = 1
                 lambda =    1.000000
                     rc =    4.828263
                 energy =    0.494150
                kinetic =    0.904558
    potential(screened) =   -0.410408
       potential(ionic) =   -0.853691
atom: Total number of Sankey-type orbitals:  5

atm_pop: Valence configuration (for local Pseudopot. screening):
 1s( 1.00)                                                            
 2p( 0.00)                                                            
Vna: chval, zval:    1.00000   1.00000

Vna:  Cut-off radius for the neutral-atom potential:   4.479210

atom: _________________________________________________________________________

prinput: Basis input ----------------------------------------------------------

PAO.BasisType split     

%block ChemicalSpeciesLabel
    1    1 H                       # Species index, atomic number, species label
%endblock ChemicalSpeciesLabel

%block PAO.Basis                 # Define Basis set
H                     2                    # Species label, number of l-shells
 n=1   0   2                         # n, l, Nzeta 
   4.479      1.797   
   1.000      1.000   
 n=2   1   1                         # n, l, Nzeta 
   4.828   
   1.000   
%endblock PAO.Basis

prinput: ----------------------------------------------------------------------

coor:   Atomic-coordinates input format  =     Cartesian coordinates
coor:                                          (in Angstroms)

siesta: Atomic coordinates (Bohr) and species
siesta:      7.40222   6.98015   7.78691  1        1
siesta:      8.34295   6.65361   8.67995  1        2
siesta:      4.39144   7.85725   5.67737  1        3
siesta:      3.51932   8.10156   4.70363  1        4

siesta: System type = molecule  

initatomlists: Number of atoms, orbitals, and projectors:      4    20    36

coxmol: Writing XMOL coordinates into file siesta.xyz                                                          

siesta: ******************** Simulation parameters ****************************
siesta:
siesta: The following are some of the parameters of the simulation.
siesta: A complete list of the parameters used, including default values,
siesta: can be found in file out.fdf
siesta:
redata: Spin configuration                          = none
redata: Number of spin components                   = 1
redata: Time-Reversal Symmetry                      = T
redata: Spin-spiral                                 = F
redata: Long output                                 =   F
redata: Number of Atomic Species                    =        1
redata: Charge density info will appear in .RHO file
redata: Write Mulliken Pop.                         = Atomic and Orbital charges
redata: Matel table size (NRTAB)                    =     1024
redata: Mesh Cutoff                                 =   200.0000 Ry
redata: Net charge of the system                    =     0.0000 |e|
redata: Min. number of SCF Iter                     =        0
redata: Max. number of SCF Iter                     =      500
redata: SCF convergence failure will abort job
redata: SCF mix quantity                            = Hamiltonian
redata: Mix DM or H after convergence               =   F
redata: Recompute H after scf cycle                 =   F
redata: Mix DM in first SCF step                    =   T
redata: Write Pulay info on disk                    =   F
redata: New DM Mixing Weight                        =     0.4000
redata: New DM Occupancy tolerance                  = 0.000000000001
redata: No kicks to SCF
redata: DM Mixing Weight for Kicks                  =     0.5000
redata: Require Harris convergence for SCF          =   F
redata: Harris energy tolerance for SCF             =     0.000100 eV
redata: Require DM convergence for SCF              =   T
redata: DM tolerance for SCF                        =     0.000100
redata: Require EDM convergence for SCF             =   F
redata: EDM tolerance for SCF                       =     0.001000 eV
redata: Require H convergence for SCF               =   T
redata: Hamiltonian tolerance for SCF               =     0.001000 eV
redata: Require (free) Energy convergence for SCF   =   F
redata: (free) Energy tolerance for SCF             =     0.000100 eV
redata: Using Saved Data (generic)                  =   F
redata: Use continuation files for DM               =   F
redata: Neglect nonoverlap interactions             =   F
redata: Method of Calculation                       = Diagonalization
redata: Electronic Temperature                      =   299.9869 K
redata: Fix the spin of the system                  =   F
redata: Dynamics option                             = Single-point calculation
mix.SCF: Pulay mixing                            = Pulay
mix.SCF:    Variant                              = stable
mix.SCF:    History steps                        = 9
mix.SCF:    Linear mixing weight                 =     0.400000
mix.SCF:    Mixing weight                        =     0.400000
mix.SCF:    SVD condition                        = 0.1000E-07
redata: ***********************************************************************

%block SCF.Mixers
  Pulay
%endblock SCF.Mixers

%block SCF.Mixer.Pulay
  # Mixing method
  method pulay
  variant stable

  # Mixing options
  weight 0.4000
  weight.linear 0.4000
  history 9
%endblock SCF.Mixer.Pulay

DM_history_depth set to one: no extrapolation allowed by default for geometry relaxation
Size of DM history Fstack: 1
Total number of electrons:     4.000000
Total ionic charge:     4.000000

* ProcessorY, Blocksize:    2   2


* Orbital distribution balance (max,min):     4     2

 Kpoints in:           18 . Kpoints trimmed:           14

siesta: k-grid: Number of k-points =    14
siesta: k-grid: Cutoff (effective) =    18.000 Ang
siesta: k-grid: Supercell and displacements
siesta: k-grid:    0   3   0      0.000
siesta: k-grid:    0   0   3      0.000
siesta: k-grid:    3   0   0      0.000

diag: Algorithm                                     = D&C
diag: Parallel over k                               =   F
diag: Use parallel 2D distribution                  =   T
diag: Parallel block-size                           = 2
diag: Parallel distribution                         =     2 x     4
diag: Used triangular part                          = Lower
diag: Absolute tolerance                            =  0.100E-15
diag: Orthogonalization factor                      =  0.100E-05
diag: Memory factor                                 =  1.0000


ts: **************************************************************
ts: Save H and S matrices                           =    F
ts: Save DM and EDM matrices                        =    F
ts: Fix Hartree potential                           =    F
ts: Only save the overlap matrix S                  =    F
ts: **************************************************************

************************ Begin: TS CHECKS AND WARNINGS ************************
************************ End: TS CHECKS AND WARNINGS **************************


                     ====================================
                        Single-point calculation
                     ====================================

outcell: Unit cell vectors (Ang):
       12.000000    0.000000    0.000000
        0.000000   12.000000    0.000000
        0.000000    0.000000   12.000000

outcell: Cell vector modules (Ang)   :   12.000000   12.000000   12.000000
outcell: Cell angles (23,13,12) (deg):     90.0000     90.0000     90.0000
outcell: Cell volume (Ang**3)        :   1728.0000
<dSpData1D:S at geom step 0
  <sparsity:sparsity for geom step 0
    nrows_g=20 nrows=4 sparsity=.1900 nnzs=76, refcount: 7>
  <dData1D:(new from dSpData1D) n=76, refcount: 1>
refcount: 1>
new_DM -- step:     1
Initializing Density Matrix...
DM filled with atomic data:
<dSpData2D:DM initialized from atoms
  <sparsity:sparsity for geom step 0
    nrows_g=20 nrows=4 sparsity=.1900 nnzs=76, refcount: 8>
  <dData2D:DM n=76 m=1, refcount: 1>
refcount: 1>
No. of atoms with KB's overlaping orbs in proc 0. Max # of overlaps:       4      18
New grid distribution:   1
           1       1:   54    1:   27    1:   14
           2       1:   54    1:   27   15:   28
           3       1:   54    1:   27   29:   41
           4       1:   54    1:   27   42:   54
           5       1:   54   28:   54    1:   14
           6       1:   54   28:   54   15:   28
           7       1:   54   28:   54   29:   41
           8       1:   54   28:   54   42:   54

InitMesh: MESH =   108 x   108 x   108 =     1259712
InitMesh: (bp) =    54 x    54 x    54 =      157464
InitMesh: Mesh cutoff (required, used) =   200.000   223.865 Ry
ExtMesh (bp) on 0 =   102 x    75 x    62 =      474300
New grid distribution:   2
           1      14:   54   19:   54    1:   17
           2      15:   54    1:   18    1:   17
           3      18:   54    1:   18   18:   54
           4       1:   14    1:   18    1:   17
           5       1:   17    1:   18   18:   54
           6       1:   13   19:   54    1:   17
           7      17:   54   19:   54   18:   54
           8       1:   16   19:   54   18:   54
New grid distribution:   3
           1      12:   54   20:   54    1:   16
           2      13:   54    1:   19    1:   16
           3      20:   54    1:   17   17:   54
           4       1:   12    1:   19    1:   16
           5       1:   19    1:   17   17:   54
           6       1:   11   20:   54    1:   16
           7      18:   54   18:   54   17:   54
           8       1:   17   18:   54   17:   54
Setting up quadratic distribution...
ExtMesh (bp) on 0 =    89 x    84 x    65 =      485940
PhiOnMesh: Number of (b)points on node 0 =                25092
PhiOnMesh: nlist on node 0 =                13865

stepf: Fermi-Dirac step function

siesta: Program's energy decomposition (eV):
siesta: Ebs     =       -41.344089
siesta: Eions   =        78.829286
siesta: Ena     =        21.934577
siesta: Ekin    =        65.011916
siesta: Enl     =       -24.007863
siesta: Eso     =         0.000000
siesta: Eldau   =         0.000000
siesta: DEna    =        -9.212546
siesta: DUscf   =         1.169604
siesta: DUext   =         0.000000
siesta: Enegf   =         0.000000
siesta: Exc     =       -38.538101
siesta: eta*DQ  =         0.000000
siesta: Emadel  =         0.000000
siesta: Emeta   =         0.000000
siesta: Emolmec =         0.000000
siesta: Ekinion =         0.000000
siesta: Eharris =       -72.028072
siesta: Etot    =       -62.471699
siesta: FreeEng =       -62.471699

        iscf     Eharris(eV)        E_KS(eV)     FreeEng(eV)     dDmax    Ef(eV) dHmax(eV)
   scf:    1      -72.028072      -62.471699      -62.471699  0.584135 -6.328621  2.928727
timer: Routine,Calls,Time,% = IterSCF        1       0.755  67.66
   scf:    2      -62.575516      -62.524852      -62.524852  0.014790 -5.468836  1.549314
   scf:    3      -62.563472      -62.545755      -62.545755  0.017379 -4.509696  0.004095
   scf:    4      -62.545757      -62.545756      -62.545756  0.000352 -4.512636  0.005380
   scf:    5      -62.545756      -62.545756      -62.545756  0.000074 -4.510415  0.000889

SCF Convergence by DM+H criterion
max |DM_out - DM_in|         :     0.0000735961
max |H_out - H_in|      (eV) :     0.0008885057
SCF cycle converged after 5 iterations

Using DM_out to compute the final energy and forces
No. of atoms with KB's overlaping orbs in proc 0. Max # of overlaps:       4      18

siesta: E_KS(eV) =              -62.5458

siesta: E_KS - E_eggbox =       -62.5458

siesta: Atomic forces (eV/Ang):
     1   -0.930711    0.325945   -0.897579
     2    0.985784   -0.340876    0.936535
     3    0.981034   -0.274409    1.116130
     4   -1.036276    0.290673   -1.154473
----------------------------------------
   Tot   -0.000169    0.001333    0.000614
----------------------------------------
   Max    1.154473
   Res    0.842507    sqrt( Sum f_i^2 / 3N )
----------------------------------------
   Max    1.154473    constrained

Stress-tensor-Voigt (kbar):       -0.98       -0.10       -1.01        0.31        0.31       -0.99
(Free)E + p*V (eV/cell)      -61.7959
Target enthalpy (eV/cell)      -62.5458

mulliken: Atomic and Orbital Populations:

Species: H                   
Atom  Qatom  Qorb
               1s      1s      2py     2pz     2px     
   1  0.988   0.887   0.128  -0.001  -0.008  -0.017
   2  1.012   0.879   0.128   0.000   0.002   0.002
   3  0.988   0.885   0.129  -0.002  -0.007  -0.018
   4  1.012   0.878   0.129   0.000   0.002   0.002

mulliken: Qtot =        4.000

coxmol: Writing XMOL coordinates into file siesta.xyz                                                          

siesta: Program's energy decomposition (eV):
siesta: Ebs     =       -42.750993
siesta: Eions   =        78.829286
siesta: Ena     =        21.934577
siesta: Ekin    =        61.885604
siesta: Enl     =       -22.847870
siesta: Eso     =         0.000000
siesta: Eldau   =         0.000000
siesta: DEna    =        -7.841373
siesta: DUscf   =         0.865200
siesta: DUext   =         0.000000
siesta: Enegf   =         0.000000
siesta: Exc     =       -37.712607
siesta: eta*DQ  =         0.000000
siesta: Emadel  =         0.000000
siesta: Emeta   =         0.000000
siesta: Emolmec =         0.000000
siesta: Ekinion =         0.000000
siesta: Eharris =       -62.545756
siesta: Etot    =       -62.545756
siesta: FreeEng =       -62.545756

siesta: Final energy (eV):
siesta:  Band Struct. =     -42.750993
siesta:       Kinetic =      61.885604
siesta:       Hartree =      66.923340
siesta:       Eldau   =       0.000000
siesta:       Eso     =       0.000000
siesta:    Ext. field =       0.000000
siesta:       Enegf   =       0.000000
siesta:   Exch.-corr. =     -37.712607
siesta:  Ion-electron =    -190.069965
siesta:       Ion-ion =      36.427872
siesta:       Ekinion =       0.000000
siesta:         Total =     -62.545756
siesta:         Fermi =      -4.510415

siesta: Atomic forces (eV/Ang):
siesta:      1   -0.930711    0.325945   -0.897579
siesta:      2    0.985784   -0.340876    0.936535
siesta:      3    0.981034   -0.274409    1.116130
siesta:      4   -1.036276    0.290673   -1.154473
siesta: ----------------------------------------
siesta:    Tot   -0.000169    0.001333    0.000614

siesta: Stress tensor (static) (eV/Ang**3):
siesta:    -0.000614    0.000191   -0.000617
siesta:     0.000191   -0.000060    0.000191
siesta:    -0.000617    0.000191   -0.000628

siesta: Cell volume =       1728.000000 Ang**3

siesta: Pressure (static):
siesta:                Solid            Molecule  Units
siesta:           0.00000473          0.00000002  Ry/Bohr**3
siesta:           0.00043396          0.00000163  eV/Ang**3
siesta:           0.69529582          0.00261448  kBar
(Free)E+ p_basis*V_orbitals  =         -61.900392
(Free)Eharris+ p_basis*V_orbitals  =         -61.900392

siesta: Electric dipole (a.u.)  =   -0.000154   -0.000343   -0.000971
siesta: Electric dipole (Debye) =   -0.000391   -0.000872   -0.002468
>> End of run:  27-MAR-2022   7:23:12
Job completed
