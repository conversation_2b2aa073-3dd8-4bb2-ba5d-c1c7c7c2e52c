Siesta Version  : v4.1-b4
Architecture    : GNU
Compiler version: GNU Fortran (Uos 8.3.0.3-3+rebuild) 8.3.0
Compiler flags  : mpif90 -O2 -fPIC -ftree-vectorize
PP flags        : -DMPI  -DFC_HAVE_ABORT -DGRID_DP -DPHI_GRID_SP -DSIESTA__DIAG_2STAGE
Libraries       : libsiestaLAPACK.a libsiestaLAPACK.a -lpthread -L/home/<USER>/siesta/mathlib/scaalapack -lscalapack -L/home/<USER>/siesta/mathlib/BLACS/LIB -lblacsF77init -lblacsCinit -lblacs  -L/home/<USER>/siesta/mathlib/lapack -lrefblas
PARALLEL version

* Running on 8 nodes in parallel
>> Start of run:  26-MAR-2022  12:42:54

                           ***********************       
                           *  WELCOME TO SIESTA  *       
                           ***********************       

reinit: Reading from standard input
reinit: Dumped input in INPUT_TMP.20962
************************** Dump of input data file ****************************
################## Species and atoms ##################
SystemName       siesta
SystemLabel      siesta
NumberOfSpecies   1
NumberOfAtoms     4
%block ChemicalSpeciesLabel
1 1 H
%endblock ChemicalSpeciesLabel
SolutionMethod   Diagon # ## OrderN or Diagon
MaxSCFIterations 500
PAO.BasisType    split
%block PAO.Basis
H     2
 n=1    0    2  S .7020340
   4.4302740  0.0
   1.000   1.000
 n=2    1    1
   4.7841521
   1.000
%endblock PAO.Basis
SpinPolarized    F
DM.MixingWeight      0.4
DM.NumberPulay       9
DM.Tolerance         1.d-4
################### FUNCTIONAL ###################
XC.functional    GGA    # Exchange-correlation functional type
XC.Authors       PBE    # Particular parametrization of xc func
MeshCutoff       200. Ry # Equivalent planewave cutoff for the grid
KgridCutoff      12.000000 Ang
WriteCoorInitial T
WriteCoorXmol    T
WriteMDhistory   T
WriteMullikenPop 1
WriteForces      T
###################  GEOMETRY  ###################
LatticeConstant  1.00 Ang
%block LatticeVectors
12.0 0.0 0.0
0.0 12.0 0.0
0.0 0.0 12.0
%endblock LatticeVectors
AtomicCoordinatesFormat Ang
%block AtomicCoordinatesAndAtomicSpecies
5.639943273918358 3.2746893910053436 5.4717239571929195 1
5.2616756280824815 3.9681618956211273 5.345195112955592 1
2.951097377870997 3.926620056317909 3.481831019268095 1
2.523192416100546 4.141464620868035 2.923012443835895 1
%endblock AtomicCoordinatesAndAtomicSpecies
************************** End of input data file *****************************

reinit: -----------------------------------------------------------------------
reinit: System Name: siesta
reinit: -----------------------------------------------------------------------
reinit: System Label: siesta
reinit: -----------------------------------------------------------------------

initatom: Reading input for the pseudopotentials and atomic orbitals ----------
Species number:   1 Atomic number:    1 Label: H

Ground state valence configuration:   1s01
Reading pseudopotential information in formatted form from H.psf

Valence configuration for pseudopotential generation:
1s( 1.00) rc: 1.25
2p( 0.00) rc: 1.25
3d( 0.00) rc: 1.25
4f( 0.00) rc: 1.25
For H, standard SIESTA heuristics set lmxkb to 2
 (one more than the basis l, including polarization orbitals).
Use PS.lmax or PS.KBprojectors blocks to override.

<basis_specs>
===============================================================================
H                    Z=   1    Mass=  1.0100        Charge= 0.17977+309
Lmxo=1 Lmxkb= 2    BasisType=split      Semic=F
L=0  Nsemic=0  Cnfigmx=1
          n=1  nzeta=2  polorb=0
            splnorm:   0.70203    
               vcte:    0.0000    
               rinn:    0.0000    
               qcoe:    0.0000    
               qyuk:    0.0000    
               qwid:   0.10000E-01
                rcs:    4.4303      0.0000    
            lambdas:    1.0000      1.0000    
L=1  Nsemic=0  Cnfigmx=2
          n=1  nzeta=1  polorb=0
            splnorm:   0.15000    
               vcte:    0.0000    
               rinn:    0.0000    
               qcoe:    0.0000    
               qyuk:    0.0000    
               qwid:   0.10000E-01
                rcs:    4.7842    
            lambdas:    1.0000    
-------------------------------------------------------------------------------
L=0  Nkbl=1  erefs: 0.17977+309
L=1  Nkbl=1  erefs: 0.17977+309
L=2  Nkbl=1  erefs: 0.17977+309
===============================================================================
</basis_specs>

atom: Called for H                     (Z =   1)

read_vps: Pseudopotential generation method:
read_vps: ATM3.3    Troullier-Martins                       
Total valence charge:    1.00000

xc_check: Exchange-correlation functional:
xc_check: GGA Perdew, Burke & Ernzerhof 1996
V l=0 = -2*Zval/r beyond r=  1.2343
V l=1 = -2*Zval/r beyond r=  1.2189
V l=2 = -2*Zval/r beyond r=  1.2189
All V_l potentials equal beyond r=  1.2343
This should be close to max(r_c) in ps generation
All pots = -2*Zval/r beyond r=  1.2343

VLOCAL1: 99.0% of the norm of Vloc inside     28.493 Ry
VLOCAL1: 99.9% of the norm of Vloc inside     64.935 Ry
atom: Maximum radius for 4*pi*r*r*local-pseudopot. charge    1.45251
atom: Maximum radius for r*vlocal+2*Zval:    1.21892
GHOST: No ghost state for L =  0
GHOST: No ghost state for L =  1
GHOST: No ghost state for L =  2

KBgen: Kleinman-Bylander projectors: 
   l= 0   rc=  1.364359   el= -0.477200   Ekb= -2.021939   kbcos= -0.344793
   l= 1   rc=  1.434438   el=  0.001076   Ekb= -0.443447   kbcos= -0.022843
   l= 2   rc=  1.470814   el=  0.002010   Ekb= -0.140543   kbcos= -0.002863

KBgen: Total number of  Kleinman-Bylander projectors:    9
atom: -------------------------------------------------------------------------

atom: SANKEY-TYPE ORBITALS:
atom: Selected multiple-zeta basis: split     

SPLIT: Orbitals with angular momentum L= 0

SPLIT: Basis orbitals for state 1s

   izeta = 1
                 lambda =    1.000000
                     rc =    4.479210
                 energy =   -0.451136
                kinetic =    1.010721
    potential(screened) =   -1.461857
       potential(ionic) =   -1.992374

   izeta = 2
                 rmatch =    1.797005
              splitnorm =    0.702034
                 energy =    1.204812
                kinetic =    4.569535
    potential(screened) =   -3.364723
       potential(ionic) =   -3.917241

SPLIT: Orbitals with angular momentum L= 1

SPLIT: Basis orbitals for state 2p

   izeta = 1
                 lambda =    1.000000
                     rc =    4.828263
                 energy =    0.494150
                kinetic =    0.904558
    potential(screened) =   -0.410408
       potential(ionic) =   -0.853691
atom: Total number of Sankey-type orbitals:  5

atm_pop: Valence configuration (for local Pseudopot. screening):
 1s( 1.00)                                                            
 2p( 0.00)                                                            
Vna: chval, zval:    1.00000   1.00000

Vna:  Cut-off radius for the neutral-atom potential:   4.479210

atom: _________________________________________________________________________

prinput: Basis input ----------------------------------------------------------

PAO.BasisType split     

%block ChemicalSpeciesLabel
    1    1 H                       # Species index, atomic number, species label
%endblock ChemicalSpeciesLabel

%block PAO.Basis                 # Define Basis set
H                     2                    # Species label, number of l-shells
 n=1   0   2                         # n, l, Nzeta 
   4.479      1.797   
   1.000      1.000   
 n=2   1   1                         # n, l, Nzeta 
   4.828   
   1.000   
%endblock PAO.Basis

prinput: ----------------------------------------------------------------------

coor:   Atomic-coordinates input format  =     Cartesian coordinates
coor:                                          (in Angstroms)

siesta: Atomic coordinates (Bohr) and species
siesta:     10.65795   6.18827  10.34006  1        1
siesta:      9.94313   7.49874  10.10096  1        2
siesta:      5.57677   7.42024   6.57971  1        3
siesta:      4.76814   7.82624   5.52370  1        4

siesta: System type = molecule  

initatomlists: Number of atoms, orbitals, and projectors:      4    20    36

coxmol: Writing XMOL coordinates into file siesta.xyz                                                          

siesta: ******************** Simulation parameters ****************************
siesta:
siesta: The following are some of the parameters of the simulation.
siesta: A complete list of the parameters used, including default values,
siesta: can be found in file out.fdf
siesta:
redata: Spin configuration                          = none
redata: Number of spin components                   = 1
redata: Time-Reversal Symmetry                      = T
redata: Spin-spiral                                 = F
redata: Long output                                 =   F
redata: Number of Atomic Species                    =        1
redata: Charge density info will appear in .RHO file
redata: Write Mulliken Pop.                         = Atomic and Orbital charges
redata: Matel table size (NRTAB)                    =     1024
redata: Mesh Cutoff                                 =   200.0000 Ry
redata: Net charge of the system                    =     0.0000 |e|
redata: Min. number of SCF Iter                     =        0
redata: Max. number of SCF Iter                     =      500
redata: SCF convergence failure will abort job
redata: SCF mix quantity                            = Hamiltonian
redata: Mix DM or H after convergence               =   F
redata: Recompute H after scf cycle                 =   F
redata: Mix DM in first SCF step                    =   T
redata: Write Pulay info on disk                    =   F
redata: New DM Mixing Weight                        =     0.4000
redata: New DM Occupancy tolerance                  = 0.000000000001
redata: No kicks to SCF
redata: DM Mixing Weight for Kicks                  =     0.5000
redata: Require Harris convergence for SCF          =   F
redata: Harris energy tolerance for SCF             =     0.000100 eV
redata: Require DM convergence for SCF              =   T
redata: DM tolerance for SCF                        =     0.000100
redata: Require EDM convergence for SCF             =   F
redata: EDM tolerance for SCF                       =     0.001000 eV
redata: Require H convergence for SCF               =   T
redata: Hamiltonian tolerance for SCF               =     0.001000 eV
redata: Require (free) Energy convergence for SCF   =   F
redata: (free) Energy tolerance for SCF             =     0.000100 eV
redata: Using Saved Data (generic)                  =   F
redata: Use continuation files for DM               =   F
redata: Neglect nonoverlap interactions             =   F
redata: Method of Calculation                       = Diagonalization
redata: Electronic Temperature                      =   299.9869 K
redata: Fix the spin of the system                  =   F
redata: Dynamics option                             = Single-point calculation
mix.SCF: Pulay mixing                            = Pulay
mix.SCF:    Variant                              = stable
mix.SCF:    History steps                        = 9
mix.SCF:    Linear mixing weight                 =     0.400000
mix.SCF:    Mixing weight                        =     0.400000
mix.SCF:    SVD condition                        = 0.1000E-07
redata: ***********************************************************************

%block SCF.Mixers
  Pulay
%endblock SCF.Mixers

%block SCF.Mixer.Pulay
  # Mixing method
  method pulay
  variant stable

  # Mixing options
  weight 0.4000
  weight.linear 0.4000
  history 9
%endblock SCF.Mixer.Pulay

DM_history_depth set to one: no extrapolation allowed by default for geometry relaxation
Size of DM history Fstack: 1
Total number of electrons:     4.000000
Total ionic charge:     4.000000

* ProcessorY, Blocksize:    2   2


* Orbital distribution balance (max,min):     4     2

 Kpoints in:           18 . Kpoints trimmed:           14

siesta: k-grid: Number of k-points =    14
siesta: k-grid: Cutoff (effective) =    18.000 Ang
siesta: k-grid: Supercell and displacements
siesta: k-grid:    0   3   0      0.000
siesta: k-grid:    0   0   3      0.000
siesta: k-grid:    3   0   0      0.000

diag: Algorithm                                     = D&C
diag: Parallel over k                               =   F
diag: Use parallel 2D distribution                  =   T
diag: Parallel block-size                           = 2
diag: Parallel distribution                         =     2 x     4
diag: Used triangular part                          = Lower
diag: Absolute tolerance                            =  0.100E-15
diag: Orthogonalization factor                      =  0.100E-05
diag: Memory factor                                 =  1.0000


ts: **************************************************************
ts: Save H and S matrices                           =    F
ts: Save DM and EDM matrices                        =    F
ts: Fix Hartree potential                           =    F
ts: Only save the overlap matrix S                  =    F
ts: **************************************************************

************************ Begin: TS CHECKS AND WARNINGS ************************
************************ End: TS CHECKS AND WARNINGS **************************


                     ====================================
                        Single-point calculation
                     ====================================

outcell: Unit cell vectors (Ang):
       12.000000    0.000000    0.000000
        0.000000   12.000000    0.000000
        0.000000    0.000000   12.000000

outcell: Cell vector modules (Ang)   :   12.000000   12.000000   12.000000
outcell: Cell angles (23,13,12) (deg):     90.0000     90.0000     90.0000
outcell: Cell volume (Ang**3)        :   1728.0000
<dSpData1D:S at geom step 0
  <sparsity:sparsity for geom step 0
    nrows_g=20 nrows=4 sparsity=.1600 nnzs=64, refcount: 7>
  <dData1D:(new from dSpData1D) n=64, refcount: 1>
refcount: 1>
new_DM -- step:     1
Initializing Density Matrix...
DM filled with atomic data:
<dSpData2D:DM initialized from atoms
  <sparsity:sparsity for geom step 0
    nrows_g=20 nrows=4 sparsity=.1600 nnzs=64, refcount: 8>
  <dData2D:DM n=64 m=1, refcount: 1>
refcount: 1>
No. of atoms with KB's overlaping orbs in proc 0. Max # of overlaps:       4      14
New grid distribution:   1
           1       1:   54    1:   27    1:   14
           2       1:   54    1:   27   15:   28
           3       1:   54    1:   27   29:   41
           4       1:   54    1:   27   42:   54
           5       1:   54   28:   54    1:   14
           6       1:   54   28:   54   15:   28
           7       1:   54   28:   54   29:   41
           8       1:   54   28:   54   42:   54

InitMesh: MESH =   108 x   108 x   108 =     1259712
InitMesh: (bp) =    54 x    54 x    54 =      157464
InitMesh: Mesh cutoff (required, used) =   200.000   223.865 Ry
ExtMesh (bp) on 0 =   102 x    75 x    62 =      474300
New grid distribution:   2
           1      17:   54   19:   54    1:   20
           2       1:   17    1:   18    1:   20
           3      24:   54    1:   18   21:   54
           4       1:   23    1:   18   21:   54
           5      18:   54    1:   18    1:   20
           6       1:   16   19:   54    1:   20
           7      23:   54   19:   54   21:   54
           8       1:   22   19:   54   21:   54
New grid distribution:   3
           1      14:   54   19:   54    1:   20
           2      16:   54    1:   18    1:   20
           3      25:   54    1:   17   21:   54
           4       1:   15    1:   18    1:   20
           5       1:   24    1:   17   21:   54
           6       1:   13   19:   54    1:   20
           7      24:   54   18:   54   21:   54
           8       1:   23   18:   54   21:   54
Setting up quadratic distribution...
ExtMesh (bp) on 0 =    86 x    84 x    68 =      491232
PhiOnMesh: Number of (b)points on node 0 =                27360
PhiOnMesh: nlist on node 0 =                11261

stepf: Fermi-Dirac step function

siesta: Program's energy decomposition (eV):
siesta: Ebs     =       -40.176717
siesta: Eions   =        78.829286
siesta: Ena     =        20.924128
siesta: Ekin    =        61.158026
siesta: Enl     =       -21.991485
siesta: Eso     =         0.000000
siesta: Eldau   =         0.000000
siesta: DEna    =        -7.384781
siesta: DUscf   =         0.850915
siesta: DUext   =         0.000000
siesta: Enegf   =         0.000000
siesta: Exc     =       -37.236701
siesta: eta*DQ  =         0.000000
siesta: Emadel  =         0.000000
siesta: Emeta   =         0.000000
siesta: Emolmec =         0.000000
siesta: Ekinion =         0.000000
siesta: Eharris =       -72.176817
siesta: Etot    =       -62.509185
siesta: FreeEng =       -62.509185

        iscf     Eharris(eV)        E_KS(eV)     FreeEng(eV)     dDmax    Ef(eV) dHmax(eV)
   scf:    1      -72.176817      -62.509185      -62.509185  0.600586 -0.512095  2.678248
timer: Routine,Calls,Time,% = IterSCF        1       0.753  63.54
   scf:    2      -62.577474      -62.544056      -62.544056  0.012338  0.292876  1.430929
   scf:    3      -62.570634      -62.558336      -62.558336  0.014718 -4.700680  0.009932
   scf:    4      -62.558339      -62.558337      -62.558337  0.000273 -4.706481  0.002691
   scf:    5      -62.558338      -62.558338      -62.558338  0.000085 -4.704305  0.000957

SCF Convergence by DM+H criterion
max |DM_out - DM_in|         :     0.0000847478
max |H_out - H_in|      (eV) :     0.0009566757
SCF cycle converged after 5 iterations

Using DM_out to compute the final energy and forces
No. of atoms with KB's overlaping orbs in proc 0. Max # of overlaps:       4      14

siesta: E_KS(eV) =              -62.5583

siesta: E_KS - E_eggbox =       -62.5583

siesta: Atomic forces (eV/Ang):
     1   -0.725962    1.329875   -0.241236
     2    0.722021   -1.331131    0.238159
     3    0.191707   -0.094924    0.246454
     4   -0.186659    0.094778   -0.241233
----------------------------------------
   Tot    0.001108   -0.001402    0.002145
----------------------------------------
   Max    1.331131
   Res    0.639808    sqrt( Sum f_i^2 / 3N )
----------------------------------------
   Max    1.331131    constrained

Stress-tensor-Voigt (kbar):        0.19        0.84       -0.09       -0.43       -0.11       -0.01
(Free)E + p*V (eV/cell)      -62.8935
Target enthalpy (eV/cell)      -62.5583

mulliken: Atomic and Orbital Populations:

Species: H                   
Atom  Qatom  Qorb
               1s      1s      2py     2pz     2px     
   1  0.990   0.880   0.107   0.002   0.000   0.002
   2  1.009   0.896   0.104   0.003   0.001   0.004
   3  1.015   0.883   0.121   0.000   0.005   0.005
   4  0.986   0.856   0.126   0.000   0.002   0.002

mulliken: Qtot =        4.000

coxmol: Writing XMOL coordinates into file siesta.xyz                                                          

siesta: Program's energy decomposition (eV):
siesta: Ebs     =       -41.233367
siesta: Eions   =        78.829286
siesta: Ena     =        20.924128
siesta: Ekin    =        58.736841
siesta: Enl     =       -21.099768
siesta: Eso     =         0.000000
siesta: Eldau   =         0.000000
siesta: DEna    =        -6.340657
siesta: DUscf   =         0.647128
siesta: DUext   =         0.000000
siesta: Enegf   =         0.000000
siesta: Exc     =       -36.596724
siesta: eta*DQ  =         0.000000
siesta: Emadel  =         0.000000
siesta: Emeta   =         0.000000
siesta: Emolmec =         0.000000
siesta: Ekinion =         0.000000
siesta: Eharris =       -62.558338
siesta: Etot    =       -62.558338
siesta: FreeEng =       -62.558338

siesta: Final energy (eV):
siesta:  Band Struct. =     -41.233367
siesta:       Kinetic =      58.736841
siesta:       Hartree =      59.625935
siesta:       Eldau   =       0.000000
siesta:       Eso     =       0.000000
siesta:    Ext. field =       0.000000
siesta:       Enegf   =       0.000000
siesta:   Exch.-corr. =     -36.596724
siesta:  Ion-electron =    -172.243291
siesta:       Ion-ion =      27.918901
siesta:       Ekinion =       0.000000
siesta:         Total =     -62.558338
siesta:         Fermi =      -4.704305

siesta: Atomic forces (eV/Ang):
siesta:      1   -0.725962    1.329875   -0.241236
siesta:      2    0.722021   -1.331131    0.238159
siesta:      3    0.191707   -0.094924    0.246454
siesta:      4   -0.186659    0.094778   -0.241233
siesta: ----------------------------------------
siesta:    Tot    0.001108   -0.001402    0.002145

siesta: Stress tensor (static) (eV/Ang**3):
siesta:     0.000118   -0.000268   -0.000003
siesta:    -0.000268    0.000522   -0.000066
siesta:    -0.000003   -0.000066   -0.000057

siesta: Cell volume =       1728.000000 Ang**3

siesta: Pressure (static):
siesta:                Solid            Molecule  Units
siesta:          -0.00000211          0.00000001  Ry/Bohr**3
siesta:          -0.00019394          0.00000097  eV/Ang**3
siesta:          -0.31072513          0.00154885  kBar
(Free)E+ p_basis*V_orbitals  =         -61.912973
(Free)Eharris+ p_basis*V_orbitals  =         -61.912973

siesta: Electric dipole (a.u.)  =    0.004302   -0.002566    0.001524
siesta: Electric dipole (Debye) =    0.010936   -0.006523    0.003873
>> End of run:  26-MAR-2022  12:42:57
Job completed
