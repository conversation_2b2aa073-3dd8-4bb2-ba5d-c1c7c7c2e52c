[tox]
envlist = py37
skipsdist = true

[travis]
python =
    3.7: py37

[testenv:flake8]
basepython = python
deps = flake8
commands = flake8 parametrization_clean tests

[testenv]
setenv =
    PYTHONPATH = {toxinidir}/parametrization_clean/
deps =
    -r{toxinidir}/requirements/test.txt
; If you want to make tox run the tests with the same versions, create a
; requirements.txt with the pinned versions and uncomment the following line:
;     -r{toxinidir}/requirements.txt
commands =
    pip install -U pip
#    pytest --basetemp={envtmpdir}
    py.test --cov-report term-missing --cov=parametrization_clean/

