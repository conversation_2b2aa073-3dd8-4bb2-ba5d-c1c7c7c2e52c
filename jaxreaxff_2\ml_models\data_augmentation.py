 import jax
import jax.numpy as jnp
import numpy as np
from typing import Dict, List, Tuple, Callable, Optional, Union, Any
import haiku as hk

class MolecularAugmentation:
    """分子数据增强类，用于生成分子系统的各种变体"""
    
    def __init__(
        self, 
        rotation_range: float = np.pi,
        translation_range: float = 1.0,
        perturbation_scale: float = 0.05,
        bond_permutation_prob: float = 0.05,
        atomic_substitution_prob: float = 0.01,
        atom_dropout_prob: float = 0.02,
        seed: Optional[int] = None
    ):
        """
        初始化分子数据增强器
        
        Args:
            rotation_range: 旋转角度范围（弧度）
            translation_range: 平移范围
            perturbation_scale: 坐标扰动比例
            bond_permutation_prob: 键类型置换概率
            atomic_substitution_prob: 原子类型替换概率
            atom_dropout_prob: 原子丢弃概率
            seed: 随机种子
        """
        self.rotation_range = rotation_range
        self.translation_range = translation_range
        self.perturbation_scale = perturbation_scale
        self.bond_permutation_prob = bond_permutation_prob
        self.atomic_substitution_prob = atomic_substitution_prob
        self.atom_dropout_prob = atom_dropout_prob
        
        # 初始化随机数生成器
        if seed is not None:
            self.rng = jax.random.PRNGKey(seed)
        else:
            self.rng = jax.random.PRNGKey(int(np.random.randint(0, 2**32)))
    
    def _get_next_rng(self) -> Tuple[jax.random.PRNGKey, jax.random.PRNGKey]:
        """获取下一个随机数生成器"""
        self.rng, next_rng = jax.random.split(self.rng)
        return next_rng
    
    def _create_rotation_matrix_3d(self, rng: jax.random.PRNGKey) -> jnp.ndarray:
        """
        创建3D旋转矩阵
        
        Args:
            rng: 随机数生成器
            
        Returns:
            rotation_matrix: 3D旋转矩阵
        """
        # 生成随机欧拉角
        rng1, rng2, rng3 = jax.random.split(rng, 3)
        alpha = jax.random.uniform(rng1, (), minval=-self.rotation_range, maxval=self.rotation_range)
        beta = jax.random.uniform(rng2, (), minval=-self.rotation_range, maxval=self.rotation_range)
        gamma = jax.random.uniform(rng3, (), minval=-self.rotation_range, maxval=self.rotation_range)
        
        # 计算旋转矩阵
        cosa, sina = jnp.cos(alpha), jnp.sin(alpha)
        cosb, sinb = jnp.cos(beta), jnp.sin(beta)
        cosg, sing = jnp.cos(gamma), jnp.sin(gamma)
        
        # 绕z轴旋转矩阵
        Rz = jnp.array([
            [cosa, -sina, 0],
            [sina, cosa, 0],
            [0, 0, 1]
        ])
        
        # 绕y轴旋转矩阵
        Ry = jnp.array([
            [cosb, 0, sinb],
            [0, 1, 0],
            [-sinb, 0, cosb]
        ])
        
        # 绕x轴旋转矩阵
        Rx = jnp.array([
            [1, 0, 0],
            [0, cosg, -sing],
            [0, sing, cosg]
        ])
        
        # 合成旋转矩阵
        rotation_matrix = jnp.matmul(Rz, jnp.matmul(Ry, Rx))
        return rotation_matrix
    
    def rotate_molecule(
        self, 
        positions: jnp.ndarray,
        rng: Optional[jax.random.PRNGKey] = None
    ) -> jnp.ndarray:
        """
        随机旋转分子
        
        Args:
            positions: 原子坐标，形状 [n_atoms, 3]
            rng: 随机数生成器（可选）
            
        Returns:
            rotated_positions: 旋转后的原子坐标
        """
        if rng is None:
            rng = self._get_next_rng()
            
        # 计算分子中心
        center = jnp.mean(positions, axis=0, keepdims=True)
        
        # 将分子移到原点
        centered_positions = positions - center
        
        # 创建旋转矩阵
        rotation_matrix = self._create_rotation_matrix_3d(rng)
        
        # 应用旋转
        rotated_positions = jnp.matmul(centered_positions, rotation_matrix.T)
        
        # 将分子移回原位置
        rotated_positions = rotated_positions + center
        
        return rotated_positions
    
    def translate_molecule(
        self, 
        positions: jnp.ndarray,
        rng: Optional[jax.random.PRNGKey] = None
    ) -> jnp.ndarray:
        """
        随机平移分子
        
        Args:
            positions: 原子坐标，形状 [n_atoms, 3]
            rng: 随机数生成器（可选）
            
        Returns:
            translated_positions: 平移后的原子坐标
        """
        if rng is None:
            rng = self._get_next_rng()
            
        # 生成随机平移向量
        rng1, rng2, rng3 = jax.random.split(rng, 3)
        dx = jax.random.uniform(rng1, (), minval=-self.translation_range, maxval=self.translation_range)
        dy = jax.random.uniform(rng2, (), minval=-self.translation_range, maxval=self.translation_range)
        dz = jax.random.uniform(rng3, (), minval=-self.translation_range, maxval=self.translation_range)
        
        translation = jnp.array([dx, dy, dz])
        
        # 应用平移
        translated_positions = positions + translation
        
        return translated_positions
    
    def perturb_positions(
        self, 
        positions: jnp.ndarray,
        rng: Optional[jax.random.PRNGKey] = None
    ) -> jnp.ndarray:
        """
        随机扰动原子坐标
        
        Args:
            positions: 原子坐标，形状 [n_atoms, 3]
            rng: 随机数生成器（可选）
            
        Returns:
            perturbed_positions: 扰动后的原子坐标
        """
        if rng is None:
            rng = self._get_next_rng()
            
        # 生成随机扰动
        perturbation = jax.random.normal(
            rng, 
            shape=positions.shape
        ) * self.perturbation_scale
        
        # 应用扰动
        perturbed_positions = positions + perturbation
        
        return perturbed_positions
    
    def permute_bond_types(
        self,
        bond_types: jnp.ndarray,
        bond_mask: jnp.ndarray,
        n_bond_types: int,
        rng: Optional[jax.random.PRNGKey] = None
    ) -> jnp.ndarray:
        """
        随机置换部分化学键类型
        
        Args:
            bond_types: 化学键类型矩阵，形状 [n_atoms, n_atoms]
            bond_mask: 化学键掩码，形状 [n_atoms, n_atoms]
            n_bond_types: 键类型数量
            rng: 随机数生成器（可选）
            
        Returns:
            permuted_bond_types: 置换后的化学键类型
        """
        if rng is None:
            rng = self._get_next_rng()
            
        # 生成是否置换的掩码
        rng1, rng2 = jax.random.split(rng)
        permute_mask = jax.random.bernoulli(
            rng1, 
            p=self.bond_permutation_prob, 
            shape=bond_types.shape
        )
        permute_mask = permute_mask * bond_mask  # 只在有化学键的地方置换
        
        # 生成新的键类型
        new_bond_types = jax.random.randint(
            rng2,
            shape=bond_types.shape,
            minval=1,
            maxval=n_bond_types+1
        )
        
        # 应用置换
        permuted_bond_types = jnp.where(
            permute_mask, 
            new_bond_types, 
            bond_types
        )
        
        return permuted_bond_types
    
    def substitute_atom_types(
        self,
        atom_types: jnp.ndarray,
        possible_types: List[int],
        rng: Optional[jax.random.PRNGKey] = None
    ) -> jnp.ndarray:
        """
        随机替换部分原子类型
        
        Args:
            atom_types: 原子类型，形状 [n_atoms]
            possible_types: 可能的原子类型列表
            rng: 随机数生成器（可选）
            
        Returns:
            substituted_types: 替换后的原子类型
        """
        if rng is None:
            rng = self._get_next_rng()
            
        # 生成是否替换的掩码
        rng1, rng2 = jax.random.split(rng)
        substitute_mask = jax.random.bernoulli(
            rng1, 
            p=self.atomic_substitution_prob, 
            shape=atom_types.shape
        )
        
        # 生成新的原子类型
        possible_types_array = jnp.array(possible_types)
        n_possible_types = len(possible_types)
        
        indices = jax.random.randint(
            rng2,
            shape=atom_types.shape,
            minval=0,
            maxval=n_possible_types
        )
        
        new_atom_types = jnp.take(possible_types_array, indices)
        
        # 应用替换
        substituted_types = jnp.where(
            substitute_mask, 
            new_atom_types, 
            atom_types
        )
        
        return substituted_types
    
    def augment_molecule(
        self,
        atom_types: jnp.ndarray,
        positions: jnp.ndarray,
        bond_types: Optional[jnp.ndarray] = None,
        n_bond_types: Optional[int] = None,
        possible_atom_types: Optional[List[int]] = None,
        rng: Optional[jax.random.PRNGKey] = None,
        apply_rotation: bool = True,
        apply_translation: bool = True,
        apply_perturbation: bool = True,
        apply_bond_permutation: bool = False,
        apply_atom_substitution: bool = False
    ) -> Tuple[jnp.ndarray, jnp.ndarray, Optional[jnp.ndarray]]:
        """
        对分子应用多种数据增强
        
        Args:
            atom_types: 原子类型，形状 [n_atoms]
            positions: 原子坐标，形状 [n_atoms, 3]
            bond_types: 化学键类型矩阵，形状 [n_atoms, n_atoms]
            n_bond_types: 键类型数量
            possible_atom_types: 可能的原子类型列表
            rng: 随机数生成器（可选）
            apply_rotation: 是否应用旋转
            apply_translation: 是否应用平移
            apply_perturbation: 是否应用坐标扰动
            apply_bond_permutation: 是否应用键类型置换
            apply_atom_substitution: 是否应用原子类型替换
            
        Returns:
            augmented_data: 增强后的分子数据，包含原子类型、坐标和键类型
        """
        if rng is None:
            rng = self._get_next_rng()
            
        # 分离多个随机数生成器
        rngs = jax.random.split(rng, 5)
        
        # 复制输入数据
        aug_atom_types = atom_types
        aug_positions = positions
        aug_bond_types = bond_types
        
        # 应用原子类型替换
        if apply_atom_substitution and possible_atom_types is not None:
            aug_atom_types = self.substitute_atom_types(
                aug_atom_types,
                possible_atom_types,
                rngs[0]
            )
            
        # 应用旋转
        if apply_rotation:
            aug_positions = self.rotate_molecule(aug_positions, rngs[1])
            
        # 应用平移
        if apply_translation:
            aug_positions = self.translate_molecule(aug_positions, rngs[2])
            
        # 应用坐标扰动
        if apply_perturbation:
            aug_positions = self.perturb_positions(aug_positions, rngs[3])
            
        # 应用键类型置换
        if apply_bond_permutation and bond_types is not None and n_bond_types is not None:
            # 创建键掩码
            bond_mask = (bond_types > 0).astype(jnp.float32)
            
            aug_bond_types = self.permute_bond_types(
                aug_bond_types,
                bond_mask,
                n_bond_types,
                rngs[4]
            )
            
        return aug_atom_types, aug_positions, aug_bond_types
    
    def generate_symmetry_operations(
        self,
        n_operations: int,
        rng: Optional[jax.random.PRNGKey] = None
    ) -> List[jnp.ndarray]:
        """
        生成对称操作（旋转矩阵）列表
        
        Args:
            n_operations: 生成的操作数量
            rng: 随机数生成器（可选）
            
        Returns:
            operations: 对称操作列表
        """
        if rng is None:
            rng = self._get_next_rng()
            
        # 分离多个随机数生成器
        rngs = jax.random.split(rng, n_operations)
        
        # 生成旋转矩阵
        operations = []
        for i in range(n_operations):
            rotation_matrix = self._create_rotation_matrix_3d(rngs[i])
            operations.append(rotation_matrix)
            
        return operations
    
    def apply_symmetry_operations(
        self,
        positions: jnp.ndarray,
        operations: List[jnp.ndarray]
    ) -> List[jnp.ndarray]:
        """
        应用对称操作列表到原子坐标
        
        Args:
            positions: 原子坐标，形状 [n_atoms, 3]
            operations: 对称操作列表
            
        Returns:
            transformed_positions: 变换后的原子坐标列表
        """
        # 计算分子中心
        center = jnp.mean(positions, axis=0, keepdims=True)
        
        # 将分子移到原点
        centered_positions = positions - center
        
        # 应用每个对称操作
        transformed_positions = []
        for op in operations:
            # 应用变换
            transformed = jnp.matmul(centered_positions, op.T)
            
            # 将分子移回原位置
            transformed = transformed + center
            
            transformed_positions.append(transformed)
            
        return transformed_positions
    
    def generate_augmented_batch(
        self,
        atom_types_batch: jnp.ndarray,
        positions_batch: jnp.ndarray,
        bond_types_batch: Optional[jnp.ndarray] = None,
        n_bond_types: Optional[int] = None,
        possible_atom_types: Optional[List[int]] = None,
        batch_multiplier: int = 4,
        rng: Optional[jax.random.PRNGKey] = None
    ) -> Tuple[jnp.ndarray, jnp.ndarray, Optional[jnp.ndarray]]:
        """
        为整个批次生成增强数据
        
        Args:
            atom_types_batch: 原子类型批次，形状 [batch_size, n_atoms]
            positions_batch: 原子坐标批次，形状 [batch_size, n_atoms, 3]
            bond_types_batch: 化学键类型批次，形状 [batch_size, n_atoms, n_atoms]
            n_bond_types: 键类型数量
            possible_atom_types: 可能的原子类型列表
            batch_multiplier: 批次扩增倍数
            rng: 随机数生成器（可选）
            
        Returns:
            augmented_batch: 增强后的批次数据
        """
        if rng is None:
            rng = self._get_next_rng()
            
        batch_size = atom_types_batch.shape[0]
        n_atoms = atom_types_batch.shape[1]
        
        # 准备输出数组
        total_size = batch_size * batch_multiplier
        aug_atom_types = jnp.zeros((total_size, n_atoms), dtype=atom_types_batch.dtype)
        aug_positions = jnp.zeros((total_size, n_atoms, 3), dtype=positions_batch.dtype)
        
        if bond_types_batch is not None:
            aug_bond_types = jnp.zeros(
                (total_size, n_atoms, n_atoms), 
                dtype=bond_types_batch.dtype
            )
        else:
            aug_bond_types = None
            
        # 分离多个随机数生成器
        rngs = jax.random.split(rng, total_size)
        
        # 对每个分子进行增强
        for i in range(batch_size):
            for j in range(batch_multiplier):
                idx = i * batch_multiplier + j
                
                # 获取原始数据
                atom_types = atom_types_batch[i]
                positions = positions_batch[i]
                bond_types = None if bond_types_batch is None else bond_types_batch[i]
                
                # 应用数据增强
                aug_atom_types_i, aug_positions_i, aug_bond_types_i = self.augment_molecule(
                    atom_types,
                    positions,
                    bond_types,
                    n_bond_types,
                    possible_atom_types,
                    rngs[idx],
                    apply_rotation=True,
                    apply_translation=True,
                    apply_perturbation=True,
                    apply_bond_permutation=(bond_types is not None),
                    apply_atom_substitution=(possible_atom_types is not None)
                )
                
                # 存储增强后的数据
                aug_atom_types = aug_atom_types.at[idx].set(aug_atom_types_i)
                aug_positions = aug_positions.at[idx].set(aug_positions_i)
                
                if aug_bond_types is not None and aug_bond_types_i is not None:
                    aug_bond_types = aug_bond_types.at[idx].set(aug_bond_types_i)
                    
        return aug_atom_types, aug_positions, aug_bond_types
    
    def augment_equivariant_data(
        self,
        atom_types: jnp.ndarray,
        positions: jnp.ndarray,
        energies: jnp.ndarray,
        forces: Optional[jnp.ndarray] = None,
        n_augmentations: int = 8,
        rng: Optional[jax.random.PRNGKey] = None
    ) -> Tuple[jnp.ndarray, jnp.ndarray, jnp.ndarray, Optional[jnp.ndarray]]:
        """
        生成等变数据增强（保持能量不变，力适当变换）
        
        Args:
            atom_types: 原子类型，形状 [n_atoms]
            positions: 原子坐标，形状 [n_atoms, 3]
            energies: 能量，标量
            forces: 力，形状 [n_atoms, 3]
            n_augmentations: 增强数量
            rng: 随机数生成器（可选）
            
        Returns:
            augmented_data: 增强后的等变数据
        """
        if rng is None:
            rng = self._get_next_rng()
            
        # 生成对称操作（旋转矩阵）
        operations = self.generate_symmetry_operations(n_augmentations, rng)
        
        # 应用对称操作到位置
        transformed_positions = self.apply_symmetry_operations(positions, operations)
        
        # 准备输出数组
        aug_atom_types = jnp.tile(atom_types, (n_augmentations, 1))
        aug_positions = jnp.stack(transformed_positions, axis=0)
        aug_energies = jnp.tile(energies, (n_augmentations,))
        
        # 如果提供了力，变换力
        if forces is not None:
            aug_forces = []
            for i, op in enumerate(operations):
                # 力的变换需要使用相同的旋转矩阵
                transformed_forces = jnp.matmul(forces, op.T)
                aug_forces.append(transformed_forces)
                
            aug_forces = jnp.stack(aug_forces, axis=0)
        else:
            aug_forces = None
            
        return aug_atom_types, aug_positions, aug_energies, aug_forces

class FeatureAugmentation:
    """特征增强类，用于增强分子表示"""
    
    def __init__(
        self,
        feature_noise_scale: float = 0.05,
        feature_dropout_prob: float = 0.1,
        feature_mask_prob: float = 0.15,
        seed: Optional[int] = None
    ):
        """
        初始化特征增强器
        
        Args:
            feature_noise_scale: 特征噪声比例
            feature_dropout_prob: 特征丢弃概率
            feature_mask_prob: 特征掩码概率
            seed: 随机种子
        """
        self.feature_noise_scale = feature_noise_scale
        self.feature_dropout_prob = feature_dropout_prob
        self.feature_mask_prob = feature_mask_prob
        
        # 初始化随机数生成器
        if seed is not None:
            self.rng = jax.random.PRNGKey(seed)
        else:
            self.rng = jax.random.PRNGKey(int(np.random.randint(0, 2**32)))
    
    def _get_next_rng(self) -> Tuple[jax.random.PRNGKey, jax.random.PRNGKey]:
        """获取下一个随机数生成器"""
        self.rng, next_rng = jax.random.split(self.rng)
        return next_rng
    
    def add_feature_noise(
        self,
        features: jnp.ndarray,
        scale: Optional[float] = None,
        rng: Optional[jax.random.PRNGKey] = None
    ) -> jnp.ndarray:
        """
        添加高斯噪声到特征
        
        Args:
            features: 输入特征，形状 [batch_size, n_atoms, feat_dim]
            scale: 噪声比例
            rng: 随机数生成器（可选）
            
        Returns:
            noisy_features: 添加噪声后的特征
        """
        if rng is None:
            rng = self._get_next_rng()
            
        if scale is None:
            scale = self.feature_noise_scale
            
        # 生成高斯噪声
        noise = jax.random.normal(rng, shape=features.shape) * scale
        
        # 添加噪声
        noisy_features = features + noise
        
        return noisy_features
    
    def feature_dropout(
        self,
        features: jnp.ndarray,
        dropout_prob: Optional[float] = None,
        rng: Optional[jax.random.PRNGKey] = None
    ) -> jnp.ndarray:
        """
        特征随机丢弃
        
        Args:
            features: 输入特征，形状 [batch_size, n_atoms, feat_dim]
            dropout_prob: 丢弃概率
            rng: 随机数生成器（可选）
            
        Returns:
            masked_features: 丢弃后的特征
        """
        if rng is None:
            rng = self._get_next_rng()
            
        if dropout_prob is None:
            dropout_prob = self.feature_dropout_prob
            
        # 创建丢弃掩码
        keep_prob = 1.0 - dropout_prob
        random_tensor = jax.random.uniform(rng, shape=features.shape)
        dropout_mask = (random_tensor < keep_prob).astype(features.dtype)
        
        # 应用丢弃
        masked_features = features * dropout_mask / keep_prob
        
        return masked_features
    
    def feature_masking(
        self,
        features: jnp.ndarray,
        mask_prob: Optional[float] = None,
        mask_value: float = 0.0,
        rng: Optional[jax.random.PRNGKey] = None
    ) -> Tuple[jnp.ndarray, jnp.ndarray]:
        """
        特征随机掩码（用于掩码预测任务）
        
        Args:
            features: 输入特征，形状 [batch_size, n_atoms, feat_dim]
            mask_prob: 掩码概率
            mask_value: 掩码值
            rng: 随机数生成器（可选）
            
        Returns:
            masked_features: 掩码后的特征
            mask: 掩码
        """
        if rng is None:
            rng = self._get_next_rng()
            
        if mask_prob is None:
            mask_prob = self.feature_mask_prob
            
        # 创建掩码
        random_tensor = jax.random.uniform(rng, shape=features.shape)
        mask = (random_tensor >= mask_prob).astype(features.dtype)
        
        # 应用掩码
        masked_features = features * mask + mask_value * (1.0 - mask)
        
        return masked_features, mask
    
    def cutmix_features(
        self,
        features1: jnp.ndarray,
        features2: jnp.ndarray,
        alpha: float = 0.2,
        rng: Optional[jax.random.PRNGKey] = None
    ) -> jnp.ndarray:
        """
        CutMix特征增强
        
        Args:
            features1: 第一组特征，形状 [batch_size, n_atoms, feat_dim]
            features2: 第二组特征，形状 [batch_size, n_atoms, feat_dim]
            alpha: Beta分布参数
            rng: 随机数生成器（可选）
            
        Returns:
            mixed_features: 混合后的特征
        """
        if rng is None:
            rng = self._get_next_rng()
            
        # 生成混合比例
        rng1, rng2 = jax.random.split(rng)
        lambd = jax.random.beta(rng1, alpha, alpha)
        
        # 确保features1和features2形状匹配
        assert features1.shape == features2.shape, "Features shapes must match"
        
        batch_size, n_atoms, feat_dim = features1.shape
        
        # 随机选择切割点
        r_idx = jax.random.randint(rng2, shape=(), minval=0, maxval=n_atoms)
        
        # 创建掩码
        mask = jnp.zeros((n_atoms,))
        mask = mask.at[:r_idx].set(1.0)
        mask = mask.reshape(1, -1, 1)
        
        # 混合特征
        mixed_features = features1 * mask + features2 * (1.0 - mask)
        
        return mixed_features
    
    def mixup_features(
        self,
        features1: jnp.ndarray,
        features2: jnp.ndarray,
        labels1: jnp.ndarray,
        labels2: jnp.ndarray,
        alpha: float = 0.2,
        rng: Optional[jax.random.PRNGKey] = None
    ) -> Tuple[jnp.ndarray, jnp.ndarray]:
        """
        Mixup特征增强
        
        Args:
            features1: 第一组特征，形状 [batch_size, n_atoms, feat_dim]
            features2: 第二组特征，形状 [batch_size, n_atoms, feat_dim]
            labels1: 第一组标签
            labels2: 第二组标签
            alpha: Beta分布参数
            rng: 随机数生成器（可选）
            
        Returns:
            混合后的特征和标签
        """
        if rng is None:
            rng = self._get_next_rng()
            
        # 生成混合比例
        lambd = jax.random.beta(rng, alpha, alpha)
        
        # 混合特征
        mixed_features = lambd * features1 + (1.0 - lambd) * features2
        
        # 混合标签
        mixed_labels = lambd * labels1 + (1.0 - lambd) * labels2
        
        return mixed_features, mixed_labels
    
    def apply_feature_augmentation(
        self,
        features: jnp.ndarray,
        rng: Optional[jax.random.PRNGKey] = None,
        apply_noise: bool = True,
        apply_dropout: bool = True
    ) -> jnp.ndarray:
        """
        应用多种特征增强
        
        Args:
            features: 输入特征，形状 [batch_size, n_atoms, feat_dim]
            rng: 随机数生成器（可选）
            apply_noise: 是否添加噪声
            apply_dropout: 是否应用丢弃
            
        Returns:
            augmented_features: 增强后的特征
        """
        if rng is None:
            rng = self._get_next_rng()
            
        # 分离多个随机数生成器
        rng1, rng2 = jax.random.split(rng)
        
        # 复制输入特征
        aug_features = features
        
        # 应用噪声
        if apply_noise:
            aug_features = self.add_feature_noise(aug_features, rng=rng1)
            
        # 应用丢弃
        if apply_dropout:
            aug_features = self.feature_dropout(aug_features, rng=rng2)
            
        return aug_features
    
    def augment_graph_features(
        self,
        node_features: jnp.ndarray,
        edge_features: jnp.ndarray,
        rng: Optional[jax.random.PRNGKey] = None,
        node_noise_scale: float = 0.05,
        edge_noise_scale: float = 0.03,
        node_dropout_prob: float = 0.1,
        edge_dropout_prob: float = 0.05
    ) -> Tuple[jnp.ndarray, jnp.ndarray]:
        """
        增强图特征（节点和边）
        
        Args:
            node_features: 节点特征，形状 [batch_size, n_nodes, node_feat_dim]
            edge_features: 边特征，形状 [batch_size, n_nodes, n_nodes, edge_feat_dim]
            rng: 随机数生成器（可选）
            node_noise_scale: 节点特征噪声比例
            edge_noise_scale: 边特征噪声比例
            node_dropout_prob: 节点特征丢弃概率
            edge_dropout_prob: 边特征丢弃概率
            
        Returns:
            augmented_features: 增强后的节点和边特征
        """
        if rng is None:
            rng = self._get_next_rng()
            
        # 分离多个随机数生成器
        rng1, rng2, rng3, rng4 = jax.random.split(rng, 4)
        
        # 增强节点特征
        aug_node_features = self.add_feature_noise(
            node_features, 
            scale=node_noise_scale, 
            rng=rng1
        )
        aug_node_features = self.feature_dropout(
            aug_node_features, 
            dropout_prob=node_dropout_prob, 
            rng=rng2
        )
        
        # 增强边特征
        aug_edge_features = self.add_feature_noise(
            edge_features, 
            scale=edge_noise_scale, 
            rng=rng3
        )
        aug_edge_features = self.feature_dropout(
            aug_edge_features, 
            dropout_prob=edge_dropout_prob, 
            rng=rng4
        )
        
        return aug_node_features, aug_edge_features