{"cells": [{"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([nan, 6., 1., 8.])"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["from reaxff.parser import ReaxffParameters\n", "\n", "ReaxffParameters('reaxff/example/cho.ff').atomtype"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"ename": "RuntimeError", "evalue": "value cannot be converted to type int64 without overflow", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[62], line 3\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01mtorch\u001b[39;00m\n\u001b[0;32m----> 3\u001b[0m \u001b[43mtorch\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m<PERSON>or\u001b[49m\u001b[43m(\u001b[49m\u001b[43m[\u001b[49m\u001b[43mtorch\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnan\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m,\u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m,\u001b[49m\u001b[38;5;241;43m6\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdtype\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtorch\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mlong\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[0;31mRuntimeError\u001b[0m: value cannot be converted to type int64 without overflow"]}], "source": ["import torch\n", "\n", "torch.tensor([torch.nan, 1,1,6], dtype=torch.long)\n", "\n"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[2, 2, 1]\n"]}], "source": ["import torch\n", "\n", "# Given tensors\n", "tensor1 = torch.tensor([1, 1, 6])\n", "tensor2 = torch.tensor([float('nan'), 6, 1, 8])\n", "\n", "# Find indices using list comprehension\n", "indices = [torch.nonzero(tensor2 == value).item() for value in tensor1]\n", "\n", "# Print the result\n", "print(indices)\n"]}], "metadata": {"kernelspec": {"display_name": "reaxff", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}