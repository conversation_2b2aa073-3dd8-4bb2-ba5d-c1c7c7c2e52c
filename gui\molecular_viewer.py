"""
分子结构可视化组件
用于渲染和交互式显示分子结构
"""

import numpy as np
# 临时禁用matplotlib导入以解决NumPy兼容性问题
try:
    import matplotlib.pyplot as plt
    from mpl_toolkits.mplot3d import Axes3D
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.backends.backend_qt5agg import NavigationToolbar2QT as NavigationToolbar
    from matplotlib.figure import Figure
    MATPLOTLIB_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ molecular_viewer matplotlib导入失败: {e}")
    MATPLOTLIB_AVAILABLE = False
    # 创建占位符类
    from PyQt5.QtWidgets import QWidget, QLabel, QVBoxLayout
    from PyQt5.QtCore import Qt

    class FigureCanvas(QWidget):
        def __init__(self, *args, **kwargs):
            super().__init__()
            self.setMinimumSize(400, 300)
            layout = QVBoxLayout(self)
            label = QLabel("⚠️ matplotlib不可用\n分子可视化功能暂时禁用")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)

    class NavigationToolbar(QWidget):
        def __init__(self, *args, **kwargs):
            super().__init__()
            self.setMaximumHeight(30)
import os

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QComboBox, 
                           QPushButton, QLabel, QFileDialog, QMessageBox,
                           QSlider, QCheckBox, QGroupBox)
from PyQt5.QtCore import Qt, pyqtSignal, pyqtSlot

# 尝试导入OpenGL支持，如果可用的话
try:
    from PyQt5.QtOpenGL import QGLWidget
    OPENGL_AVAILABLE = True
except ImportError:
    OPENGL_AVAILABLE = False
    print("PyQt5 OpenGL支持不可用，将使用Matplotlib代替")


class MolecularViewer(QWidget):
    """分子结构可视化组件
    
    提供3D分子结构可视化和交互功能
    """
    
    def __init__(self, parent=None):
        """初始化分子查看器
        
        Args:
            parent (QWidget, optional): 父窗口部件
        """
        super(MolecularViewer, self).__init__(parent)
        
        # 初始化数据
        self.coordinates = None
        self.atom_types = None
        self.current_frame = 0
        self.trajectory = None
        
        # 设置界面
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 创建3D视图区域
        self.view_layout = QVBoxLayout()
        
        # 创建基于Matplotlib的3D视图
        self.figure = Figure(figsize=(8, 6), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        self.toolbar = NavigationToolbar(self.canvas, self)
        
        self.axes = self.figure.add_subplot(111, projection='3d')
        self.axes.set_title("分子结构")
        self.axes.set_xlabel("X (Å)")
        self.axes.set_ylabel("Y (Å)")
        self.axes.set_zlabel("Z (Å)")
        
        # 添加到布局
        self.view_layout.addWidget(self.toolbar)
        self.view_layout.addWidget(self.canvas)
        
        # 控制面板
        controls_layout = QHBoxLayout()
        
        # 创建分子选择下拉框
        self.molecule_label = QLabel("分子:")
        self.molecule_combo = QComboBox()
        self.molecule_combo.addItem("未加载分子")
        self.molecule_combo.setEnabled(False)
        
        # 添加轨迹导航控件（如果是MD轨迹）
        self.frame_label = QLabel("帧:")
        self.frame_slider = QSlider(Qt.Horizontal)
        self.frame_slider.setEnabled(False)
        self.frame_slider.valueChanged.connect(self.on_frame_changed)
        
        # 创建元素显示复选框
        self.element_group = QGroupBox("显示元素")
        element_layout = QVBoxLayout()
        self.element_checkboxes = {}
        for element in ['H', 'C', 'O', 'N', 'S', 'P', 'F', 'Cl', 'Br', 'I']:
            checkbox = QCheckBox(element)
            checkbox.setChecked(True)
            checkbox.stateChanged.connect(self.update_view)
            self.element_checkboxes[element] = checkbox
            element_layout.addWidget(checkbox)
        self.element_group.setLayout(element_layout)
        self.element_group.setEnabled(False)
        
        # 添加到控制面板布局
        controls_layout.addWidget(self.molecule_label)
        controls_layout.addWidget(self.molecule_combo)
        controls_layout.addWidget(self.frame_label)
        controls_layout.addWidget(self.frame_slider)
        controls_layout.addStretch()
        
        # 添加控制按钮
        button_layout = QHBoxLayout()
        
        self.load_molecule_btn = QPushButton("加载分子")
        self.load_molecule_btn.clicked.connect(self.load_molecule)
        
        self.export_view_btn = QPushButton("导出视图")
        self.export_view_btn.clicked.connect(self.export_view)
        
        # 添加到按钮布局
        button_layout.addWidget(self.load_molecule_btn)
        button_layout.addWidget(self.export_view_btn)
        button_layout.addStretch()
        
        # 添加所有布局到主布局
        main_layout.addLayout(self.view_layout)
        main_layout.addLayout(controls_layout)
        main_layout.addLayout(button_layout)
        
        # 显示默认消息
        self.show_message("未加载分子结构")
    
    def show_message(self, message):
        """在3D视图中显示消息
        
        Args:
            message (str): 要显示的消息
        """
        self.axes.clear()
        self.axes.text(0, 0, 0, message, fontsize=12, ha='center', va='center')
        self.axes.set_xlim([-1, 1])
        self.axes.set_ylim([-1, 1])
        self.axes.set_zlim([-1, 1])
        self.canvas.draw()
    
    def load_molecule(self):
        """加载分子结构文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "加载分子结构", "", 
            "分子结构文件 (*.xyz *.pdb *.mol);;所有文件 (*.*)"
        )
        
        if file_path:
            try:
                # 这里应该有完整的分子文件解析逻辑
                # 现在只是模拟一些数据
                self.load_dummy_molecule()
                
                # 显示文件名
                file_name = os.path.basename(file_path)
                self.molecule_combo.clear()
                self.molecule_combo.addItem(file_name)
                self.molecule_combo.setEnabled(True)
                
                # 启用控件
                self.element_group.setEnabled(True)
                
                # 更新视图
                self.update_view()
                
            except Exception as e:
                QMessageBox.critical(self, "加载错误", f"无法加载分子文件: {str(e)}")
    
    def load_dummy_molecule(self):
        """加载示例分子数据（水分子）"""
        # 原子坐标
        self.coordinates = np.array([
            [0.0, 0.0, 0.0],    # O
            [0.0, 0.757, 0.586], # H
            [0.0, -0.757, 0.586]  # H
        ])
        
        # 原子类型
        self.atom_types = ['O', 'H', 'H']
        
        # 重置当前帧
        self.current_frame = 0
        
        # 清除轨迹数据
        self.trajectory = None
    
    def update_view(self):
        """更新分子视图"""
        if self.coordinates is None or self.atom_types is None:
            return
        
        # 清除当前图形
        self.axes.clear()
        
        # 获取显示设置
        visible_elements = [elem for elem, cb in self.element_checkboxes.items() if cb.isChecked()]
        
        # 绘制原子
        for i, (pos, atom_type) in enumerate(zip(self.coordinates, self.atom_types)):
            if atom_type in visible_elements:
                color = self._atom_color(atom_type)
                size = self._atom_size(atom_type)
                self.axes.scatter(*pos, s=size, c=color, edgecolors='k')
                self.axes.text(*pos, f"{atom_type}{i+1}", size=8)
        
        # 绘制键（这里简化处理，只连接距离小于1.8埃的原子）
        self._draw_bonds()
        
        # 设置轴标签
        self.axes.set_xlabel("X (Å)")
        self.axes.set_ylabel("Y (Å)")
        self.axes.set_zlabel("Z (Å)")
        
        # 确保坐标轴等比例
        self._set_axes_equal()
        
        # 刷新画布
        self.canvas.draw()
    
    def _draw_bonds(self):
        """绘制分子中的化学键"""
        if len(self.coordinates) < 2:
            return
            
        # 简化的键连接逻辑 - 在实际应用中应基于化学键长和元素类型
        for i in range(len(self.coordinates)):
            for j in range(i+1, len(self.coordinates)):
                # 计算两原子间距离
                dist = np.linalg.norm(self.coordinates[i] - self.coordinates[j])
                
                # 如果距离小于阈值，则绘制键
                if dist < 1.8:  # 阈值可根据具体元素调整
                    # 获取两点坐标
                    x = [self.coordinates[i][0], self.coordinates[j][0]]
                    y = [self.coordinates[i][1], self.coordinates[j][1]]
                    z = [self.coordinates[i][2], self.coordinates[j][2]]
                    
                    # 绘制连线
                    self.axes.plot(x, y, z, 'k-', linewidth=2)
    
    def _set_axes_equal(self):
        """确保3D坐标轴等比例显示"""
        if not hasattr(self, 'axes'):
            return
            
        limits = np.array([
            self.axes.get_xlim3d(),
            self.axes.get_ylim3d(),
            self.axes.get_zlim3d(),
        ])
        
        center = np.mean(limits, axis=1)
        radius = 0.5 * np.max(np.abs(limits[:, 1] - limits[:, 0]))
        
        self.axes.set_xlim3d([center[0] - radius, center[0] + radius])
        self.axes.set_ylim3d([center[1] - radius, center[1] + radius])
        self.axes.set_zlim3d([center[2] - radius, center[2] + radius])
    
    def _atom_color(self, atom_type):
        """根据原子类型返回颜色
        
        Args:
            atom_type (str): 原子类型
            
        Returns:
            str: 颜色字符串
        """
        colors = {
            'H': 'white', 'C': 'black', 'O': 'red', 'N': 'blue',
            'S': 'yellow', 'P': 'orange', 'F': 'green',
            'Cl': 'green', 'Br': 'brown', 'I': 'purple'
        }
        return colors.get(atom_type, 'gray')
    
    def _atom_size(self, atom_type):
        """根据原子类型返回大小
        
        Args:
            atom_type (str): 原子类型
            
        Returns:
            int: 散点大小
        """
        sizes = {
            'H': 100, 'C': 200, 'O': 180, 'N': 180, 'S': 220,
            'P': 220, 'F': 150, 'Cl': 200, 'Br': 220, 'I': 250
        }
        return sizes.get(atom_type, 150)
    
    def on_frame_changed(self, value):
        """轨迹帧改变时的处理
        
        Args:
            value (int): 新的帧索引
        """
        if self.trajectory is not None and 0 <= value < len(self.trajectory):
            self.current_frame = value
            self.coordinates = self.trajectory[value]
            self.update_view()
    
    def export_view(self):
        """导出当前视图为图像文件"""
        if self.coordinates is None:
            QMessageBox.warning(self, "警告", "没有分子结构可供导出")
            return
            
        # 打开保存文件对话框
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出视图", "", 
            "PNG图像 (*.png);;JPEG图像 (*.jpg);;PDF文件 (*.pdf);;所有文件 (*.*)"
        )
        
        if file_path:
            try:
                self.figure.savefig(file_path, dpi=300, bbox_inches='tight')
                QMessageBox.information(self, "导出成功", f"视图已保存到 {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "导出错误", f"无法保存视图: {str(e)}")
    
    def load_trajectory(self, trajectory):
        """加载分子动力学轨迹
        
        Args:
            trajectory (list): 轨迹数据，每帧包含原子坐标
        """
        if not trajectory or len(trajectory) == 0:
            return
            
        self.trajectory = trajectory
        self.coordinates = trajectory[0]
        self.current_frame = 0
        
        # 更新帧滑块
        self.frame_slider.setMinimum(0)
        self.frame_slider.setMaximum(len(trajectory) - 1)
        self.frame_slider.setValue(0)
        self.frame_slider.setEnabled(True)
        
        # 更新视图
        self.update_view() 