#!/usr/bin/env python3
"""
集成自动化工作流引擎到主应用程序
"""

import os
import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QTextEdit, QLabel, QComboBox, QSpinBox, QCheckBox, QGroupBox, QMessageBox
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtGui import QFont

# 导入工作流引擎
try:
    from automation.workflow_engine import WorkflowEngine, ReaxFFOptimizationWorkflow
    from automation.workflow_orchestrator import WorkflowEngine as OrchestratorEngine
    WORKFLOW_AVAILABLE = True
    print("工作流引擎导入成功")
except ImportError as e:
    print(f"工作流引擎导入失败: {e}")
    WORKFLOW_AVAILABLE = False

class WorkflowIntegrationPanel(QWidget):
    """工作流集成面板"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.workflow_engine = None
        self.reaxff_workflow = None
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("自动化工作流引擎")
        title.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        layout.addWidget(title)
        
        # 工作流配置组
        config_group = QGroupBox("工作流配置")
        config_layout = QVBoxLayout(config_group)
        
        # 优化算法选择
        algo_layout = QHBoxLayout()
        algo_layout.addWidget(QLabel("优化算法:"))
        self.algo_combo = QComboBox()
        self.algo_combo.addItems(["PSO", "GA", "QuantumAnnealing", "MultiObjective"])
        algo_layout.addWidget(self.algo_combo)
        config_layout.addLayout(algo_layout)
        
        # 参数设置
        param_layout = QHBoxLayout()
        param_layout.addWidget(QLabel("种群大小:"))
        self.population_spin = QSpinBox()
        self.population_spin.setRange(10, 1000)
        self.population_spin.setValue(50)
        param_layout.addWidget(self.population_spin)
        
        param_layout.addWidget(QLabel("最大迭代:"))
        self.max_iter_spin = QSpinBox()
        self.max_iter_spin.setRange(10, 10000)
        self.max_iter_spin.setValue(200)
        param_layout.addWidget(self.max_iter_spin)
        config_layout.addLayout(param_layout)
        
        # 高级选项
        advanced_layout = QHBoxLayout()
        self.quantum_check = QCheckBox("使用量子加速")
        self.ai_check = QCheckBox("使用AI辅助")
        advanced_layout.addWidget(self.quantum_check)
        advanced_layout.addWidget(self.ai_check)
        config_layout.addLayout(advanced_layout)
        
        layout.addWidget(config_group)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("启动工作流")
        self.start_btn.clicked.connect(self.start_workflow)
        button_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("停止工作流")
        self.stop_btn.clicked.connect(self.stop_workflow)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.stop_btn)
        
        self.status_btn = QPushButton("查看状态")
        self.status_btn.clicked.connect(self.show_status)
        button_layout.addWidget(self.status_btn)
        
        layout.addLayout(button_layout)
        
        # 日志显示
        log_group = QGroupBox("工作流日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setFont(QFont("Consolas", 9))
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        # 初始化工作流引擎
        self.init_workflow_engine()
        
    def init_workflow_engine(self):
        """初始化工作流引擎"""
        if not WORKFLOW_AVAILABLE:
            self.log_text.append("工作流引擎不可用")
            return
            
        try:
            # 创建工作流引擎
            self.workflow_engine = WorkflowEngine(max_workers=4)
            self.reaxff_workflow = ReaxFFOptimizationWorkflow(self.workflow_engine)
            
            self.log_text.append("工作流引擎初始化成功")
            self.log_text.append(f"   最大工作线程: {self.workflow_engine.max_workers}")
            
        except Exception as e:
            self.log_text.append(f"工作流引擎初始化失败: {e}")
    
    def start_workflow(self):
        """启动工作流"""
        if not self.workflow_engine:
            QMessageBox.warning(self, "错误", "工作流引擎未初始化")
            return
            
        try:
            # 获取配置
            config = {
                'data_path': './Datasets',  # 默认数据集路径
                'param_config': {
                    'method': 'hybrid',
                    'n_samples': 10
                },
                'optimizers': [self.algo_combo.currentText()],
                'population_size': self.population_spin.value(),
                'max_iterations': self.max_iter_spin.value(),
                'use_quantum': self.quantum_check.isChecked(),
                'use_ai_model': self.ai_check.isChecked()
            }
            
            # 创建工作流
            workflow_id = self.reaxff_workflow.create_optimization_pipeline(config)
            workflow = self.workflow_engine.workflows[workflow_id]
            
            # 启动工作流
            self.workflow_engine.submit_workflow(workflow)
            
            self.log_text.append(f"   工作流已启动: {workflow_id}")
            self.log_text.append(f"   优化算法: {config['optimizers']}")
            self.log_text.append(f"   种群大小: {config['population_size']}")
            self.log_text.append(f"   最大迭代: {config['max_iterations']}")
            
            # 更新按钮状态
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            
            # 启动状态监控线程
            self.monitor_thread = WorkflowMonitorThread(self.workflow_engine, workflow_id)
            self.monitor_thread.status_updated.connect(self.update_status)
            self.monitor_thread.start()
            
        except Exception as e:
            self.log_text.append(f"  启动工作流失败: {e}")
            QMessageBox.critical(self, "错误", f"启动工作流失败: {e}")
    
    def stop_workflow(self):
        """停止工作流"""
        if hasattr(self, 'monitor_thread'):
            self.monitor_thread.stop()
            
        self.log_text.append(" 工作流已停止")
        
        # 更新按钮状态
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
    
    def show_status(self):
        """显示工作流状态"""
        if not self.workflow_engine:
            QMessageBox.warning(self, "错误", "工作流引擎未初始化")
            return
            
        try:
            # 获取所有工作流状态
            status_info = []
            for workflow_id, workflow in self.workflow_engine.workflows.items():
                status = self.workflow_engine.get_workflow_status(workflow_id)
                status_info.append(f"工作流 {workflow_id}:")
                status_info.append(f"  名称: {status['name']}")
                status_info.append(f"  任务统计: {status['task_counts']}")
                status_info.append("")
            
            if status_info:
                status_text = "\n".join(status_info)
                QMessageBox.information(self, "工作流状态", status_text)
            else:
                QMessageBox.information(self, "工作流状态", "没有运行中的工作流")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"获取状态失败: {e}")
    
    def update_status(self, status_text):
        """更新状态显示"""
        self.log_text.append(status_text)

class WorkflowMonitorThread(QThread):
    """工作流监控线程"""
    status_updated = pyqtSignal(str)
    
    def __init__(self, workflow_engine, workflow_id):
        super().__init__()
        self.workflow_engine = workflow_engine
        self.workflow_id = workflow_id
        self.running = True
        
    def run(self):
        """运行监控"""
        while self.running:
            try:
                status = self.workflow_engine.get_workflow_status(self.workflow_id)
                
                # 检查是否完成
                if status['task_counts']['success'] > 0 and status['task_counts']['pending'] == 0:
                    self.status_updated.emit(" 工作流已完成")
                    break
                elif status['task_counts']['failed'] > 0:
                    self.status_updated.emit(" 工作流执行失败")
                    break
                else:
                    # 显示进度
                    total_tasks = sum(status['task_counts'].values())
                    completed_tasks = status['task_counts']['success']
                    if total_tasks > 0:
                        progress = (completed_tasks / total_tasks) * 100
                        self.status_updated.emit(f"📊 进度: {progress:.1f}% ({completed_tasks}/{total_tasks})")
                
                self.msleep(1000)  # 每秒检查一次
                
            except Exception as e:
                self.status_updated.emit(f" 监控错误: {e}")
                break
    
    def stop(self):
        """停止监控"""
        self.running = False

def integrate_with_main_window():
    """集成到主窗口"""
    print("🔧 集成工作流引擎到主窗口...")
    
    try:
        from gui.main_window import MainWindow
        
        # 创建应用
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建主窗口
        window = MainWindow()
        print("主窗口创建成功")
        
        # 创建工作流集成面板
        workflow_panel = WorkflowIntegrationPanel()
        print("工作流集成面板创建成功")
        
        # 这里可以将工作流面板添加到主窗口的某个位置
        # 例如添加到工具栏或菜单中
        
        print("工作流引擎集成完成")
        return True
        
    except Exception as e:
        print(f"工作流引擎集成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("工作流引擎集成测试")
    print("="*50)
    
    if not WORKFLOW_AVAILABLE:
        print("工作流引擎不可用，跳过集成测试")
        return
    
    # 测试工作流引擎基本功能
    print("\n 测试工作流引擎基本功能...")
    
    try:
        # 创建引擎
        engine = WorkflowEngine(max_workers=2)
        reaxff_workflow = ReaxFFOptimizationWorkflow(engine)
        
        # 创建配置
        config = {
            'data_path': './Datasets',
            'param_config': {'method': 'hybrid'},
            'optimizers': ['PSO', 'GA']
        }
        
        # 创建工作流
        workflow_id = reaxff_workflow.create_optimization_pipeline(config)
        print(f" 工作流创建成功: {workflow_id}")
        
        # 检查工作流
        workflow = engine.workflows[workflow_id]
        print(f" 工作流包含 {len(workflow.tasks)} 个任务")
        
        # 测试集成
        if integrate_with_main_window():
            print(" 工作流引擎集成测试通过")
        else:
            print(" 工作流引擎集成测试失败")
            
    except Exception as e:
        print(f" 工作流引擎测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 