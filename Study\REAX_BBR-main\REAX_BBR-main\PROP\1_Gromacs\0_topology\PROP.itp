;----------------------------TITLE -----------------------------------------------------------------------------------------
;   1,3-Propanediol
;
; This file was generated at 02:22 on 2023-09-25 by
;
;                  Automatic Topology Builder  
;
;                   REVISION 2023-06-14 20:38:16
;---------------------------------------------------------------------------------------------------------------------------
; Authors     : <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>
;
; Institute   : Molecular Dynamics group, 
;               School of Chemistry and Molecular Biosciences (SCMB),
;               The University of Queensland, QLD 4072, Australia
; URL         : https://atb.uq.edu.au
; Citations   : 1. Malde AK, Zuo L, Breeze M, Stroet M, Poger D, Nair PC, Oostenbrink C, Mark AE.
;                  An Automated force field Topology Builder (ATB) and repository: version 1.0.
;                  Journal of Chemical Theory and Computation, 2011, 7, 4026-4037.
;               2. Stroet M, Caron B, Visscher K, Geerke D, Malde AK, Mark AE.
;                  Automated Topology Builder version 3.0: Prediction of solvation free enthalpies in water and hexane.
;                  DOI:10.1021/acs.jctc.8b00768
;
; Disclaimer  : 
;      While every effort has been made to ensure the accuracy and validity of parameters provided below
;      the assignment of parameters is being based on an automated procedure combining data provided by a
;      given user as well as calculations performed using third party software. They are provided as a guide.
;      The authors of the ATB cannot guarantee that the parameters are complete or that the parameters provided
;      are appropriate for use in any specific application. Users are advised to treat these parameters with discretion
;      and to perform additional validation tests for their specific application if required. Neither the authors
;      of the ATB or The University of Queensland except any responsibly for how the parameters may be used.
;
; Release notes and warnings: 
;  (1) The topology is based on a set of atomic coordinates and other data provided by the user after
;      after quantum mechanical optimization of the structure using different levels of theory depending on
;      the nature of the molecule.
;  (2) In some cases the automatic bond, bond angle and dihedral type assignment is ambiguous.
;      In these cases alternative type codes are provided at the end of the line.
;  (3) While bonded parameters are taken where possible from the nominated force field non-standard bond, angle and dihedral
;      type code may be incorporated in cases where an exact match could not be found. These are marked as "non-standard"
;      or "uncertain" in comments.
;  (4) In some cases it is not possible to assign an appropriate parameter automatically. "%%" is used as a place holder
;      for those fields that could not be determined automatically. The parameters in these fields must be assigned manually
;      before the file can be used.
;---------------------------------------------------------------------------------------------------------------------------
; Input Structure : PROP
; Output          : ALL ATOM topology
;	Use in conjunction with the corresponding all atom PDB file.
;---------------------------------------------------------------------------------------------------------------------------
; Citing this topology file
; ATB molid: 32931
; ATB Topology Hash: ec7f5
;---------------------------------------------------------------------------------------------------------------------------
; Final Topology Generation was performed using: 
; A B3LYP/6-31G* optimized geometry.
; Bonded and van der Waals parameters were taken from the GROMOS 54A7 parameter set.
; Initial charges were estimated using the ESP method of Merz-Kollman.
; Final charges and charge groups were generated by method described in the ATB paper.
; If required, additional bonded parameters were generated from a Hessian matrix calculated at the B3LYP/6-31G* level of theory.
;---------------------------------------------------------------------------------------------------------------------------
;
;
[ moleculetype ]
; Name   nrexcl
PROP     3
[ atoms ]
;  nr  type  resnr  resid  atom  cgnr  charge    mass
    1     H    1    PROP     H8    1    0.449   1.0080
    2  OAlc    1    PROP     O2    2   -0.697  15.9994
    3  CPos    1    PROP     C3    3    0.168  12.0110
    4    HC    1    PROP     H6    4    0.009   1.0080
    5    HC    1    PROP     H7    5    0.009   1.0080
    6  CPos    1    PROP     C2    6    0.032  12.0110
    7    HC    1    PROP     H4    7    0.046   1.0080
    8    HC    1    PROP     H5    8    0.046   1.0080
    9  CPos    1    PROP     C1    9    0.168  12.0110
   10    HC    1    PROP     H1   10    0.009   1.0080
   11    HC    1    PROP     H2   11    0.009   1.0080
   12  OAlc    1    PROP     O1   12   -0.697  15.9994
   13     H    1    PROP     H3   13    0.449   1.0080
; total charge of the molecule:   0.000
[ bonds ]
;  ai   aj  funct   c0         c1
    1    2    2   0.0971   7.9547e+06
    2    3    2   0.1430   8.1800e+06
    3    4    2   0.1100   1.2100e+07
    3    5    2   0.1100   1.2100e+07
    3    6    2   0.1520   5.4300e+06
    6    7    2   0.1090   1.2300e+07
    6    8    2   0.1090   1.2300e+07
    6    9    2   0.1520   5.4300e+06
    9   10    2   0.1100   1.2100e+07
    9   11    2   0.1100   1.2100e+07
    9   12    2   0.1430   8.1800e+06
   12   13    2   0.0971   7.9547e+06
[ pairs ]
;  ai   aj  funct  ;  all 1-4 pairs but the ones excluded in GROMOS itp
    1    4    1
    1    5    1
    1    6    1
    2    7    1
    2    8    1
    2    9    1
    3   10    1
    3   11    1
    3   12    1
    4    7    1
    4    8    1
    4    9    1
    5    7    1
    5    8    1
    5    9    1
    6   13    1
    7   10    1
    7   11    1
    7   12    1
    8   10    1
    8   11    1
    8   12    1
   10   13    1
   11   13    1
[ angles ]
;  ai   aj   ak  funct   angle     fc
    1    2    3    2    109.50   450.00
    2    3    4    2    111.00   530.00
    2    3    5    2    111.00   530.00
    2    3    6    2    109.50   520.00
    4    3    5    2    107.57   484.00
    4    3    6    2    110.30   524.00
    5    3    6    2    110.30   524.00
    3    6    7    2    109.50   448.00
    3    6    8    2    109.50   448.00
    3    6    9    2    111.00   530.00
    7    6    8    2    107.57   484.00
    7    6    9    2    109.50   448.00
    8    6    9    2    109.50   448.00
    6    9   10    2    110.30   524.00
    6    9   11    2    110.30   524.00
    6    9   12    2    109.50   520.00
   10    9   11    2    107.57   484.00
   10    9   12    2    111.00   530.00
   11    9   12    2    111.00   530.00
    9   12   13    2    109.50   450.00
[ dihedrals ]
; GROMOS improper dihedrals
;  ai   aj   ak   al  funct   angle     fc
[ dihedrals ]
;  ai   aj   ak   al  funct    ph0      cp     mult
    1    2    3    6    1      0.00     1.26    3
    2    3    6    9    1      0.00     5.92    3
    3    6    9   12    1      0.00     5.92    3
    6    9   12   13    1      0.00     1.26    3
[ exclusions ]
;  ai   aj  funct  ;  GROMOS 1-4 exclusions