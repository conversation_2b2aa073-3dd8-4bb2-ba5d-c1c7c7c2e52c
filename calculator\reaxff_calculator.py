"""
ReaxFF力场计算器
确保计算结果的确定性和可重复性
"""

import numpy as np
import jax
import jax.numpy as jnp
from typing import Dict, List, Tuple, Optional
import hashlib
import json


class ReaxFFCalculator:
    """ReaxFF力场计算器
    
    特点：
    1. 确定性计算 - 使用固定的随机种子
    2. JAX加速 - 利用JAX进行高性能计算
    3. 自动微分 - 支持梯度计算
    """
    
    def __init__(self, param_file: Optional[str] = None, seed: int = 42):
        """初始化计算器
        
        Args:
            param_file: 力场参数文件路径
            seed: 随机种子，确保结果可重复
        """
        self.seed = seed
        self.key = jax.random.PRNGKey(seed)
        
        # 初始化参数
        self.parameters = self._initialize_parameters()
        
        # 如果提供了参数文件，加载它
        if param_file:
            self.load_parameters(param_file)
            
        # 编译JAX函数以提高性能
        self._compile_functions()
    
    def _initialize_parameters(self) -> Dict[str, jnp.ndarray]:
        """初始化默认参数"""
        # 使用确定性的默认值
        params = {
            # 键合参数
            'bond_k': jnp.array([400.0, 350.0, 300.0]),  # 键合力常数
            'bond_r0': jnp.array([1.54, 1.34, 1.20]),   # 平衡键长
            
            # 角度参数
            'angle_k': jnp.array([60.0, 55.0, 50.0]),    # 角度力常数
            'angle_theta0': jnp.array([109.47, 120.0, 180.0]),  # 平衡角度
            
            # 二面角参数
            'torsion_v': jnp.array([2.0, 1.5, 1.0]),     # 扭转势垒
            'torsion_n': jnp.array([3.0, 2.0, 1.0]),     # 周期性
            
            # 范德华参数
            'vdw_eps': jnp.array([0.1, 0.15, 0.2]),      # 势阱深度
            'vdw_sigma': jnp.array([3.5, 3.0, 2.5]),     # 碰撞直径
            
            # 静电参数
            'charge_scale': jnp.array([1.0]),             # 电荷缩放因子
        }
        
        return params
    
    def _compile_functions(self):
        """编译JAX函数以提高性能"""
        # JIT编译能量计算函数
        self._energy_fn = jax.jit(self._calculate_energy)
        self._force_fn = jax.jit(jax.grad(self._calculate_energy))
        
    def _calculate_energy(self, positions: jnp.ndarray, parameters: Dict[str, jnp.ndarray]) -> float:
        """计算系统总能量
        
        Args:
            positions: 原子位置坐标
            parameters: 力场参数
            
        Returns:
            总能量
        """
        # 这是一个简化的能量计算示例
        # 实际的ReaxFF能量计算会更复杂
        
        # 计算键合能量
        bond_energy = self._calculate_bond_energy(positions, parameters)
        
        # 计算角度能量
        angle_energy = self._calculate_angle_energy(positions, parameters)
        
        # 计算范德华能量
        vdw_energy = self._calculate_vdw_energy(positions, parameters)
        
        # 总能量
        total_energy = bond_energy + angle_energy + vdw_energy
        
        return total_energy
    
    def _calculate_bond_energy(self, positions: jnp.ndarray, parameters: Dict[str, jnp.ndarray]) -> float:
        """计算键合能量"""
        # 简化的谐振子模型
        # E_bond = 0.5 * k * (r - r0)^2
        
        # 这里仅作示例，实际需要根据连接信息计算
        energy = 0.0
        
        # 假设计算前两个原子之间的键
        if len(positions) >= 2:
            r = jnp.linalg.norm(positions[1] - positions[0])
            k = parameters['bond_k'][0]
            r0 = parameters['bond_r0'][0]
            energy = 0.5 * k * (r - r0)**2
            
        return energy
    
    def _calculate_angle_energy(self, positions: jnp.ndarray, parameters: Dict[str, jnp.ndarray]) -> float:
        """计算角度能量"""
        # 简化的角度势能
        # E_angle = 0.5 * k * (theta - theta0)^2
        
        energy = 0.0
        
        # 假设计算前三个原子形成的角度
        if len(positions) >= 3:
            # 计算角度
            v1 = positions[0] - positions[1]
            v2 = positions[2] - positions[1]
            
            cos_theta = jnp.dot(v1, v2) / (jnp.linalg.norm(v1) * jnp.linalg.norm(v2))
            theta = jnp.arccos(jnp.clip(cos_theta, -1.0, 1.0))
            
            k = parameters['angle_k'][0]
            theta0 = parameters['angle_theta0'][0] * jnp.pi / 180.0
            energy = 0.5 * k * (theta - theta0)**2
            
        return energy
    
    def _calculate_vdw_energy(self, positions: jnp.ndarray, parameters: Dict[str, jnp.ndarray]) -> float:
        """计算范德华能量"""
        # Lennard-Jones势能
        # E_vdw = 4 * eps * [(sigma/r)^12 - (sigma/r)^6]
        
        energy = 0.0
        n_atoms = len(positions)
        
        # 计算所有原子对的范德华相互作用
        for i in range(n_atoms):
            for j in range(i + 1, n_atoms):
                r = jnp.linalg.norm(positions[j] - positions[i])
                
                # 使用第一组参数作为示例
                eps = parameters['vdw_eps'][0]
                sigma = parameters['vdw_sigma'][0]
                
                # 避免除零
                r = jnp.maximum(r, 0.1)
                
                sr6 = (sigma / r) ** 6
                energy += 4 * eps * (sr6 * sr6 - sr6)
                
        return energy
    
    def calculate_energy(self, structure: Dict) -> float:
        """计算给定结构的能量
        
        Args:
            structure: 包含原子位置的结构字典
            
        Returns:
            能量值
        """
        positions = jnp.array(structure.get('positions', []))
        return float(self._energy_fn(positions, self.parameters))
    
    def calculate_forces(self, structure: Dict) -> np.ndarray:
        """计算给定结构的力
        
        Args:
            structure: 包含原子位置的结构字典
            
        Returns:
            力数组
        """
        positions = jnp.array(structure.get('positions', []))
        forces = -self._force_fn(positions, self.parameters)
        return np.array(forces)
    
    def calculate_energy_rmse(self, params: Dict, data: List[Dict]) -> float:
        """计算能量的均方根误差
        
        Args:
            params: 力场参数
            data: 训练数据列表
            
        Returns:
            RMSE值
        """
        # 更新参数
        self.parameters = params
        
        errors = []
        for sample in data:
            if 'energy' in sample and 'reference_energy' in sample:
                calc_energy = self.calculate_energy(sample)
                ref_energy = sample['reference_energy']
                errors.append((calc_energy - ref_energy) ** 2)
        
        if errors:
            return np.sqrt(np.mean(errors))
        return 0.0
    
    def calculate_force_rmse(self, params: Dict, data: List[Dict]) -> float:
        """计算力的均方根误差
        
        Args:
            params: 力场参数
            data: 训练数据列表
            
        Returns:
            RMSE值
        """
        # 更新参数
        self.parameters = params
        
        errors = []
        for sample in data:
            if 'forces' in sample and 'reference_forces' in sample:
                calc_forces = self.calculate_forces(sample)
                ref_forces = np.array(sample['reference_forces'])
                errors.extend(((calc_forces - ref_forces) ** 2).flatten())
        
        if errors:
            return np.sqrt(np.mean(errors))
        return 0.0
    
    def get_parameters(self) -> Dict[str, np.ndarray]:
        """获取当前参数"""
        return {k: np.array(v) for k, v in self.parameters.items()}
    
    def set_parameters(self, params: Dict[str, np.ndarray]):
        """设置参数"""
        self.parameters = {k: jnp.array(v) for k, v in params.items()}
        
    def load_parameters(self, filename: str):
        """从文件加载参数"""
        try:
            with open(filename, 'r') as f:
                data = json.load(f)
                self.set_parameters(data)
        except Exception as e:
            print(f"加载参数文件失败: {e}")
    
    def save_parameters(self, filename: str):
        """保存参数到文件"""
        try:
            params = self.get_parameters()
            # 转换为可JSON序列化的格式
            serializable_params = {k: v.tolist() for k, v in params.items()}
            
            with open(filename, 'w') as f:
                json.dump(serializable_params, f, indent=2)
        except Exception as e:
            print(f"保存参数文件失败: {e}")
    
    def get_parameter_fingerprint(self) -> str:
        """获取参数的指纹（用于验证参数的唯一性）"""
        params_str = json.dumps(self.get_parameters(), sort_keys=True)
        return hashlib.md5(params_str.encode()).hexdigest() 