;----------------------------TITLE -----------------------------------------------------------------------------------------
;   <PERSON><PERSON>e
;
; This file was generated at 09:57 on 2023-03-04 by
;
;                  Automatic Topology Builder  
;
;                   REVISION 2022-10-31 14:18:01
;---------------------------------------------------------------------------------------------------------------------------
; Authors     : <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON>, <PERSON>, <PERSON>
;
; Institute   : Molecular Dynamics group, 
;               School of Chemistry and Molecular Biosciences (SCMB),
;               The University of Queensland, QLD 4072, Australia
; URL         : https://atb.uq.edu.au
; Citations   : 1. <PERSON>de <PERSON>, Zuo L, Breeze M, Stroet M, Poger D, Nair PC, Oostenbrink C, Mark AE.
;                  An Automated force field Topology Builder (ATB) and repository: version 1.0.
;                  Journal of Chemical Theory and Computation, 2011, 7, 4026-4037.
;               2. Stroet M, Caron B, Visscher K, Geerke D, Malde AK, Mark AE.
;                  Automated Topology Builder version 3.0: Prediction of solvation free enthalpies in water and hexane.
;                  DOI:10.1021/acs.jctc.8b00768
;
; Disclaimer  : 
;      While every effort has been made to ensure the accuracy and validity of parameters provided below
;      the assignment of parameters is being based on an automated procedure combining data provided by a
;      given user as well as calculations performed using third party software. They are provided as a guide.
;      The authors of the ATB cannot guarantee that the parameters are complete or that the parameters provided
;      are appropriate for use in any specific application. Users are advised to treat these parameters with discretion
;      and to perform additional validation tests for their specific application if required. Neither the authors
;      of the ATB or The University of Queensland except any responsibly for how the parameters may be used.
;
; Release notes and warnings: 
;  (1) The topology is based on a set of atomic coordinates and other data provided by the user after
;      after quantum mechanical optimization of the structure using different levels of theory depending on
;      the nature of the molecule.
;  (2) In some cases the automatic bond, bond angle and dihedral type assignment is ambiguous.
;      In these cases alternative type codes are provided at the end of the line.
;  (3) While bonded parameters are taken where possible from the nominated force field non-standard bond, angle and dihedral
;      type code may be incorporated in cases where an exact match could not be found. These are marked as "non-standard"
;      or "uncertain" in comments.
;  (4) In some cases it is not possible to assign an appropriate parameter automatically. "%%" is used as a place holder
;      for those fields that could not be determined automatically. The parameters in these fields must be assigned manually
;      before the file can be used.
;---------------------------------------------------------------------------------------------------------------------------
; Input Structure : ALK
; Output          : ALL ATOM topology
;   Use in conjunction with the corresponding all atom PDB file.
;---------------------------------------------------------------------------------------------------------------------------
; Citing this topology file
; ATB molid: 919006
; ATB Topology Hash: d71dc
;---------------------------------------------------------------------------------------------------------------------------
; Final Topology Generation was performed using: 
; A B3LYP/6-31G* optimized geometry.
; Bonded and van der Waals parameters were taken from the GROMOS 54A7 parameter set.
; Initial charges were estimated using the ESP method of Merz-Kollman.
; Final charges and charge groups were generated by method described in the ATB paper.
; If required, additional bonded parameters were generated from a Hessian matrix calculated at the B3LYP/6-31G* level of theory.
;---------------------------------------------------------------------------------------------------------------------------
;
;
[ moleculetype ]
; Name   nrexcl
ALK     3
[ atoms ]
;  nr  type  resnr  resid  atom  cgnr  charge    mass
    1    HC    1    ALK    H12    1    0.055   1.0080
    2     C    1    ALK     C5    2   -0.230  12.0110
    3    HC    1    ALK    H10    3    0.055   1.0080
    4    HC    1    ALK    H11    4    0.055   1.0080
    5     C    1    ALK     C4    5    0.129  12.0110
    6    HC    1    ALK     H8    6   -0.011   1.0080
    7    HC    1    ALK     H9    7   -0.011   1.0080
    8     C    1    ALK     C3    8   -0.148  12.0110
    9    HC    1    ALK     H6    9    0.032   1.0080
   10    HC    1    ALK     H7   10    0.032   1.0080
   11     C    1    ALK     C2   11    0.129  12.0110
   12    HC    1    ALK     H4   12   -0.011   1.0080
   13    HC    1    ALK     H5   13   -0.011   1.0080
   14     C    1    ALK     C1   14   -0.230  12.0110
   15    HC    1    ALK     H1   15    0.055   1.0080
   16    HC    1    ALK     H2   16    0.055   1.0080
   17    HC    1    ALK     H3   17    0.055   1.0080
; total charge of the molecule:  -0.000
[ bonds ]
;  ai   aj  funct   c0         c1
    1    2    2   0.1090   1.2300e+07
    2    3    2   0.1090   1.2300e+07
    2    4    2   0.1090   1.2300e+07
    2    5    2   0.1530   7.1500e+06
    5    6    2   0.1090   1.2300e+07
    5    7    2   0.1090   1.2300e+07
    5    8    2   0.1540   4.0057e+06
    8    9    2   0.1090   1.2300e+07
    8   10    2   0.1090   1.2300e+07
    8   11    2   0.1540   4.0057e+06
   11   12    2   0.1090   1.2300e+07
   11   13    2   0.1090   1.2300e+07
   11   14    2   0.1530   7.1500e+06
   14   15    2   0.1090   1.2300e+07
   14   16    2   0.1090   1.2300e+07
   14   17    2   0.1090   1.2300e+07
[ pairs ]
;  ai   aj  funct  ;  all 1-4 pairs but the ones excluded in GROMOS itp
    1    6    1
    1    7    1
    1    8    1
    2    9    1
    2   10    1
    2   11    1
    3    6    1
    3    7    1
    3    8    1
    4    6    1
    4    7    1
    4    8    1
    5   12    1
    5   13    1
    5   14    1
    6    9    1
    6   10    1
    6   11    1
    7    9    1
    7   10    1
    7   11    1
    8   15    1
    8   16    1
    8   17    1
    9   12    1
    9   13    1
    9   14    1
   10   12    1
   10   13    1
   10   14    1
   12   15    1
   12   16    1
   12   17    1
   13   15    1
   13   16    1
   13   17    1
[ angles ]
;  ai   aj   ak  funct   angle     fc
    1    2    3    2    107.60   507.00
    1    2    4    2    107.60   507.00
    1    2    5    2    111.30   632.00
    3    2    4    2    107.60   507.00
    3    2    5    2    111.30   632.00
    4    2    5    2    111.30   632.00
    2    5    6    2    108.00   465.00
    2    5    7    2    108.00   465.00
    2    5    8    2    111.00   530.00
    6    5    7    2    106.75   503.00
    6    5    8    2    108.00   465.00
    7    5    8    2    108.00   465.00
    5    8    9    2    109.50   448.00
    5    8   10    2    109.50   448.00
    5    8   11    2    120.00   560.00
    9    8   10    2    106.75   503.00
    9    8   11    2    109.50   448.00
   10    8   11    2    109.50   448.00
    8   11   12    2    108.00   465.00
    8   11   13    2    108.00   465.00
    8   11   14    2    111.00   530.00
   12   11   13    2    106.75   503.00
   12   11   14    2    108.00   465.00
   13   11   14    2    108.00   465.00
   11   14   15    2    111.30   632.00
   11   14   16    2    111.30   632.00
   11   14   17    2    111.30   632.00
   15   14   16    2    107.60   507.00
   15   14   17    2    107.60   507.00
   16   14   17    2    107.60   507.00
[ dihedrals ]
; GROMOS improper dihedrals
;  ai   aj   ak   al  funct   angle     fc
[ dihedrals ]
;  ai   aj   ak   al  funct    ph0      cp     mult
    2    5    8   11    1      0.00     5.92    3
    3    2    5    8    1      0.00     5.92    3
    5    8   11   14    1    180.00     1.00    3
    8   11   14   15    1      0.00     5.92    3
[ exclusions ]
;  ai   aj  funct  ;  GROMOS 1-4 exclusions