#!/usr/bin/env python3
"""
清理无用文件脚本
删除不需要的文件和目录，保持项目整洁
"""

import os
import shutil
import time
from pathlib import Path

class ProjectCleaner:
    """项目清理器"""
    
    def __init__(self):
        self.deleted_files = []
        self.deleted_dirs = []
        self.kept_files = []
        
        # 要删除的文件列表
        self.files_to_delete = [
            # 测试文件（已经整合到主程序）
            "test_imports.py",
            "demo_extended_formats.py", 
            "test_dataset_management.py",
            "test_optimization_fix.py",
            "final_fix_test.py",
            "test_fix.py",
            "test_enhanced_demo.py",
            
            # 临时演示文件
            "demo_search_path.py",
            "complete_demo.py",
            "complete_enhanced_demo.py",
            "quick_demo.py",
            "verify_paper_plots.py",
            
            # 旧的修复文件
            "fix_font_issues.py",
            
            # Web仪表板（如果不需要）
            "web_dashboard.py",
            
            # 重复的可视化面板
            "gui/enhanced_visualization_panel.py",
            
            # 测试反应路径
            "test_reaction_path.py",
            
            # 临时启动文件
            "start.py",
        ]
        
        # 要删除的目录列表
        self.dirs_to_delete = [
            # 缓存目录
            "__pycache__",
            "gui/__pycache__",
            "optimizer/__pycache__", 
            "data/__pycache__",
            "calculator/__pycache__",
            "ml/__pycache__",
            "quantum/__pycache__",
            
            # 可能的临时目录
            "temp",
            "tmp",
            ".pytest_cache",
            ".coverage",
            
            # 如果存在旧的测试目录
            "test_outputs",
            "demo_outputs",
        ]
        
        # 要保留的重要文件（即使在删除列表中也要保留）
        self.files_to_keep = [
            "main.py",
            "requirements.txt", 
            "README.md",
            "COMPLETE_FIX_GUIDE.md",
            "CRASH_AND_DATA_FIX.md",
            "REAL_DATA_OPTIMIZATION_FIX.md",
            "VISUALIZATION_DISPLAY_FIX.md",
            "FINAL_COMPLETE_FIX.md",
            "PAPER_QUALITY_PLOTS_GUIDE.md",
            "SEARCH_PATH_FEATURE.md",
            "revised_paper.md",
            "test_optimization_fix.py",  # 保留这个测试文件
        ]
    
    def is_safe_to_delete(self, file_path):
        """检查文件是否可以安全删除"""
        file_name = os.path.basename(file_path)
        
        # 检查是否在保留列表中
        if file_name in self.files_to_keep:
            return False
        
        # 检查是否在删除列表中
        if file_name in self.files_to_delete:
            return True
        
        # 检查文件扩展名
        _, ext = os.path.splitext(file_name)
        
        # 删除Python字节码文件
        if ext == '.pyc':
            return True
        
        # 删除临时文件
        if file_name.startswith('temp_') or file_name.startswith('tmp_'):
            return True
            
        # 删除日志文件（但保留重要的）
        if ext == '.log' and 'important' not in file_name.lower():
            return True
        
        return False
    
    def clean_files(self):
        """清理文件"""
        print("🧹 开始清理无用文件...")
        
        for file_path in self.files_to_delete:
            if os.path.exists(file_path):
                if self.is_safe_to_delete(file_path):
                    try:
                        os.remove(file_path)
                        self.deleted_files.append(file_path)
                        print(f"🗑️  已删除文件: {file_path}")
                    except Exception as e:
                        print(f"❌ 删除文件失败 {file_path}: {e}")
                else:
                    self.kept_files.append(file_path)
                    print(f"🔒 保留重要文件: {file_path}")
    
    def clean_directories(self):
        """清理目录"""
        print("\n📁 开始清理无用目录...")
        
        for dir_path in self.dirs_to_delete:
            if os.path.exists(dir_path) and os.path.isdir(dir_path):
                try:
                    # 递归删除目录及其内容
                    shutil.rmtree(dir_path)
                    self.deleted_dirs.append(dir_path)
                    print(f"🗑️  已删除目录: {dir_path}")
                except Exception as e:
                    print(f"❌ 删除目录失败 {dir_path}: {e}")
    
    def clean_pycache_recursive(self):
        """递归清理所有__pycache__目录"""
        print("\n🔄 递归清理__pycache__目录...")
        
        for root, dirs, files in os.walk('.'):
            if '__pycache__' in dirs:
                pycache_path = os.path.join(root, '__pycache__')
                try:
                    shutil.rmtree(pycache_path)
                    self.deleted_dirs.append(pycache_path)
                    print(f"🗑️  已删除: {pycache_path}")
                except Exception as e:
                    print(f"❌ 删除失败 {pycache_path}: {e}")
    
    def clean_pyc_files_recursive(self):
        """递归清理所有.pyc文件"""
        print("\n🔄 递归清理.pyc文件...")
        
        pyc_count = 0
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.endswith('.pyc'):
                    file_path = os.path.join(root, file)
                    try:
                        os.remove(file_path)
                        pyc_count += 1
                    except Exception as e:
                        print(f"❌ 删除.pyc文件失败 {file_path}: {e}")
        
        if pyc_count > 0:
            print(f"🗑️  已删除 {pyc_count} 个.pyc文件")
    
    def organize_documentation(self):
        """整理文档文件"""
        print("\n📚 整理文档文件...")
        
        docs_dir = "docs"
        if not os.path.exists(docs_dir):
            os.makedirs(docs_dir)
            print(f"📁 创建文档目录: {docs_dir}")
        
        # 移动.md文件到docs目录（除了根目录的README.md）
        md_files = [f for f in os.listdir('.') if f.endswith('.md') and f != 'README.md']
        
        for md_file in md_files:
            src_path = md_file
            dst_path = os.path.join(docs_dir, md_file)
            
            if not os.path.exists(dst_path):
                try:
                    shutil.move(src_path, dst_path)
                    print(f"📄 移动文档: {md_file} -> {dst_path}")
                except Exception as e:
                    print(f"❌ 移动文档失败 {md_file}: {e}")
    
    def generate_cleanup_report(self):
        """生成清理报告"""
        report_path = "cleanup_report.txt"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("项目清理报告\n")
            f.write("=" * 60 + "\n\n")
            
            f.write(f"清理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write(f"已删除文件 ({len(self.deleted_files)} 个):\n")
            f.write("-" * 30 + "\n")
            for file_path in self.deleted_files:
                f.write(f"  - {file_path}\n")
            
            f.write(f"\n已删除目录 ({len(self.deleted_dirs)} 个):\n")
            f.write("-" * 30 + "\n")
            for dir_path in self.deleted_dirs:
                f.write(f"  - {dir_path}\n")
            
            f.write(f"\n保留的重要文件 ({len(self.kept_files)} 个):\n")
            f.write("-" * 30 + "\n")
            for file_path in self.kept_files:
                f.write(f"  - {file_path}\n")
            
            f.write("\n" + "=" * 60 + "\n")
            f.write("清理完成！\n")
            f.write("项目现在更加整洁，专注于核心功能。\n")
        
        print(f"📋 清理报告已生成: {report_path}")
    
    def run_full_cleanup(self):
        """运行完整清理"""
        print("🚀 开始项目清理...")
        print("⚠️  注意：重要文件将被保留")
        
        # 确认清理
        response = input("\n确定要继续清理吗？(y/N): ")
        if response.lower() != 'y':
            print("❌ 清理已取消")
            return
        
        # 执行清理步骤
        self.clean_files()
        self.clean_directories()
        self.clean_pycache_recursive()
        self.clean_pyc_files_recursive()
        self.organize_documentation()
        
        # 生成报告
        self.generate_cleanup_report()
        
        print("\n" + "=" * 60)
        print("🎉 项目清理完成！")
        print("=" * 60)
        print(f"✅ 已删除 {len(self.deleted_files)} 个文件")
        print(f"✅ 已删除 {len(self.deleted_dirs)} 个目录")
        print(f"🔒 保留了 {len(self.kept_files)} 个重要文件")
        print("📋 详细报告请查看 cleanup_report.txt")
        print("\n🎯 项目现在专注于核心功能：")
        print("   - 先进的ReaxFF参数优化")
        print("   - 学术级别的可视化生成")
        print("   - 专业的力场文件生成")
        print("   - 完整的文档和指南")

def main():
    """主函数"""
    print("🧹 ReaxFFOpt 项目清理工具")
    print("=" * 50)
    
    cleaner = ProjectCleaner()
    cleaner.run_full_cleanup()

if __name__ == "__main__":
    main() 