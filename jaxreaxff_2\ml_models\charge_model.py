import jax
import jax.numpy as jnp
import flax.linen as nn
from typing import Dict, Any, Tuple, Optional
from .base_model import BaseModel

class ChargeModel(BaseModel):
    """用于预测原子电荷的模型"""
    
    def __init__(
        self,
        hidden_dim: int = 128,
        num_layers: int = 3,
        dropout_rate: float = 0.1,
        activation: str = 'relu',
        use_charge_conservation: bool = True
    ):
        """
        初始化电荷预测模型
        
        Args:
            hidden_dim: 隐藏层维度
            num_layers: 层数
            dropout_rate: Dropout比率
            activation: 激活函数
            use_charge_conservation: 是否使用电荷守恒约束
        """
        super().__init__()
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        self.dropout_rate = dropout_rate
        self.activation = activation
        self.use_charge_conservation = use_charge_conservation
        
        # 定义网络层
        self.layers = []
        for i in range(num_layers):
            self.layers.append(nn.Dense(hidden_dim))
            self.layers.append(nn.Dropout(dropout_rate))
            if activation == 'relu':
                self.layers.append(nn.relu)
            elif activation == 'gelu':
                self.layers.append(nn.gelu)
            else:
                raise ValueError(f"Unsupported activation: {activation}")
        
        # 输出层 (预测原子电荷)
        self.output_layer = nn.Dense(1)
        
        # 初始化参数
        self.params = None
    
    def initialize(self, rng: jax.random.PRNGKey, input_shape: Tuple[int, ...]) -> Dict[str, Any]:
        """
        初始化模型参数
        
        Args:
            rng: 随机数生成器
            input_shape: 输入张量的形状
            
        Returns:
            params: 模型参数字典
        """
        # 创建虚拟输入
        dummy_input = jnp.zeros(input_shape)
        
        # 初始化参数
        variables = self.init(rng, dummy_input, training=False)
        return variables['params']
    
    def __call__(
        self, 
        inputs: jnp.ndarray, 
        atom_mask: Optional[jnp.ndarray] = None,
        training: bool = False
    ) -> jnp.ndarray:
        """
        模型前向传播
        
        Args:
            inputs: 输入数据 [batch_size, n_atoms, feature_dim]
            atom_mask: 原子掩码 [batch_size, n_atoms] (可选)
            training: 是否处于训练模式
            
        Returns:
            charges: 预测的原子电荷 [batch_size, n_atoms]
        """
        batch_size, n_atoms, feature_dim = inputs.shape
        
        # 重塑输入以处理每个原子
        x = inputs.reshape(-1, feature_dim)
        
        # 通过所有层
        for layer in self.layers:
            if isinstance(layer, nn.Dropout):
                x = layer(x, deterministic=not training)
            else:
                x = layer(x)
        
        # 输出层
        charges = self.output_layer(x)
        
        # 重塑回原始形状
        charges = charges.reshape(batch_size, n_atoms)
        
        # 应用电荷守恒约束 (总电荷为零)
        if self.use_charge_conservation:
            if atom_mask is not None:
                # 计算每个系统中原子的平均电荷
                total_charges = jnp.sum(charges * atom_mask, axis=1, keepdims=True)  
                atom_counts = jnp.sum(atom_mask, axis=1, keepdims=True)
                avg_charge = total_charges / jnp.maximum(atom_counts, 1.0)
                
                # 减去平均电荷来确保总电荷为零
                charges = charges - avg_charge * atom_mask
            else:
                # 如果没有掩码，假设所有原子都有效
                total_charges = jnp.sum(charges, axis=1, keepdims=True)
                avg_charge = total_charges / n_atoms
                charges = charges - avg_charge
        
        return charges
    
    def compute_electronegativity(
        self, 
        inputs: jnp.ndarray,
        training: bool = False
    ) -> jnp.ndarray:
        """
        计算原子的电负性
        
        Args:
            inputs: 输入数据 [batch_size, n_atoms, feature_dim]
            training: 是否处于训练模式
            
        Returns:
            electronegativities: 原子电负性 [batch_size, n_atoms]
        """
        # 使用模型的特征提取部分
        batch_size, n_atoms, feature_dim = inputs.shape
        
        # 重塑输入以处理每个原子
        x = inputs.reshape(-1, feature_dim)
        
        # 仅使用前面的层
        for layer in self.layers[:-1]:  # 排除最后一层
            if isinstance(layer, nn.Dropout):
                x = layer(x, deterministic=not training)
            else:
                x = layer(x)
        
        # 重塑回原始形状
        x = x.reshape(batch_size, n_atoms, -1)
        
        # 计算每个原子的电负性值
        electronegativities = jnp.mean(x, axis=-1)
        
        return electronegativities
    
    def get_metrics(self) -> Dict[str, float]:
        """
        获取模型指标
        
        Returns:
            metrics: 模型指标字典
        """
        return {
            'hidden_dim': self.hidden_dim,
            'num_layers': self.num_layers,
            'dropout_rate': self.dropout_rate,
            'activation': self.activation,
            'use_charge_conservation': self.use_charge_conservation
        }
    
    def save(self, path: str):
        """
        保存模型参数
        
        Args:
            path: 保存路径
        """
        if self.params is None:
            raise ValueError("Model parameters not initialized")
        
        # 将参数转换为numpy数组
        params_np = jax.tree_map(lambda x: jnp.array(x), self.params)
        
        # 保存为numpy文件
        jnp.save(path, params_np)
    
    def load(self, path: str):
        """
        加载模型参数
        
        Args:
            path: 加载路径
        """
        # 加载numpy数组
        params_np = jnp.load(path)
        
        # 转换回JAX数组
        self.params = jax.tree_map(lambda x: jnp.array(x), params_np) 