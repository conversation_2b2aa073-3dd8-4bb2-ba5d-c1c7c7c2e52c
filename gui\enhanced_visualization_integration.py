#!/usr/bin/env python3
"""
增强的可视化集成面板 - 优化版本，参考jaxreaxff_3实现
支持多数据集检测和分别生成可视化
"""

import os
import sys
import numpy as np
import matplotlib
matplotlib.use('Qt5Agg')
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime
import json
import traceback

def detect_datasets_from_directory(base_dir="Datasets"):
    """检测数据集目录下的所有数据集 - 参考jaxreaxff_3实现"""
    datasets = []
    
    if not os.path.exists(base_dir):
        print(f"⚠️ 未找到数据集目录: {base_dir}")
        return datasets
    
    try:
        # 获取所有子目录
        for item in os.listdir(base_dir):
            material_path = os.path.join(base_dir, item)
            if os.path.isdir(material_path):
                # 检查是否包含有效的数据文件
                has_valid_data = False
                
                # 检查常见的数据文件
                data_files = ['geo', 'params', 'trainset.in', 'control']
                for data_file in data_files:
                    if os.path.exists(os.path.join(material_path, data_file)):
                        has_valid_data = True
                        break
                
                # 检查子目录
                for subitem in os.listdir(material_path):
                    sub_path = os.path.join(material_path, subitem)
                    if os.path.isdir(sub_path):
                        for data_file in data_files:
                            if os.path.exists(os.path.join(sub_path, data_file)):
                                has_valid_data = True
                                break
                
                if has_valid_data:
                    # 特殊处理HNO3数据集
                    if item == "HNO3":
                        datasets.append("nitric_acid")
                    else:
                        datasets.append(item)
                    
                    print(f"✅ 发现有效数据集: {item}")
                else:
                    print(f"⚠️ 跳过无效数据集目录: {item} (无有效数据文件)")
    
    except Exception as e:
        print(f"❌ 检测数据集时出错: {e}")
        traceback.print_exc()
    
    print(f"📊 总共发现 {len(datasets)} 个有效数据集: {datasets}")
    return datasets

# 导入学术可视化模块
try:
    from .enhanced_academic_visualization import AcademicVisualizationGenerator
    from ..forcefield.force_field_generator import ForceFieldGenerator
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import importlib.util
    
    # 动态导入学术可视化模块
    spec = importlib.util.spec_from_file_location(
        "enhanced_academic_visualization", 
        "gui/enhanced_academic_visualization.py"
    )
    if spec and spec.loader:
        enhanced_academic_visualization = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(enhanced_academic_visualization)
        AcademicVisualizationGenerator = enhanced_academic_visualization.AcademicVisualizationGenerator
    
    # 动态导入力场生成模块
    spec = importlib.util.spec_from_file_location(
        "force_field_generator", 
        "forcefield/force_field_generator.py"
    )
    if spec and spec.loader:
        force_field_generator = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(force_field_generator)
        ForceFieldGenerator = force_field_generator.ForceFieldGenerator

class AcademicVisualizationWorker(QThread):
    """学术可视化生成工作线程 - 优化版本"""
    
    visualization_finished = pyqtSignal(list)  # 生成的图片路径列表
    progress_updated = pyqtSignal(int, str)   # 进度和状态消息
    error_occurred = pyqtSignal(str)          # 错误信息
    
    def __init__(self, optimization_data, output_dir="academic_visualizations", material_name=None):
        super().__init__()
        self.optimization_data = optimization_data
        self.output_dir = output_dir
        self.material_name = material_name
        
    def run(self):
        """运行学术可视化生成"""
        try:
            self.progress_updated.emit(0, "开始生成学术级别可视化...")
            
            # 创建学术可视化生成器
            from gui.enhanced_academic_visualization import AcademicVisualizationGenerator
            
            # 确保输出目录存在
            os.makedirs(self.output_dir, exist_ok=True)
            
            # 使用数据集名称作为材料名称
            if not self.material_name:
                self.material_name = "unknown_material"
            
            # 创建生成器实例
            generator = AcademicVisualizationGenerator(
                output_dir=self.output_dir,
                material_name=self.material_name
            )
            
            self.progress_updated.emit(20, f"正在为 {self.material_name} 生成优化收敛图...")
            
            # 生成所有学术图表
            plot_paths = generator.generate_all_academic_plots(self.optimization_data)
            
            self.progress_updated.emit(100, f"学术可视化生成完成: {len(plot_paths)} 个图表")
            
            # 发送完成信号
            self.visualization_finished.emit(plot_paths)
            
        except Exception as e:
            error_msg = f"学术可视化生成失败: {str(e)}"
            print(error_msg)
            import traceback
            traceback.print_exc()
            self.error_occurred.emit(error_msg)

class ForceFieldWorker(QThread):
    """力场文件生成工作线程"""
    
    generation_finished = pyqtSignal(str)     # 生成的目录路径
    progress_updated = pyqtSignal(int, str)   # 进度和状态消息
    error_occurred = pyqtSignal(str)          # 错误信息
    
    def __init__(self, optimized_params, material_name, optimization_method="PSO"):
        super().__init__()
        self.optimized_params = optimized_params
        self.material_name = material_name
        self.optimization_method = optimization_method
        
    def run(self):
        """运行力场文件生成"""
        try:
            self.progress_updated.emit(10, f"初始化力场文件生成器（材料: {self.material_name}）...")
            
            # 创建生成器
            generator = ForceFieldGenerator()
            
            self.progress_updated.emit(30, "生成ffield文件...")
            self.progress_updated.emit(50, "生成参数文件...")
            self.progress_updated.emit(70, "生成控制文件...")
            self.progress_updated.emit(90, "生成优化报告...")
            
            # 生成力场文件
            output_dir = generator.generate_force_field_from_optimization(
                self.optimized_params, 
                self.material_name, 
                self.optimization_method
            )
            
            self.progress_updated.emit(100, "力场文件生成完成！")
            self.generation_finished.emit(output_dir)
            
        except Exception as e:
            self.error_occurred.emit(f"力场文件生成失败: {str(e)}")

class EnhancedVisualizationIntegrationPanel(QWidget):
    """增强的可视化集成面板"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.optimization_data = {}
        self.generated_academic_plots = []
        self.generated_force_field_dir = None
        self.current_material_name = None  # 当前材料名称
        self.current_dataset_info = {}     # 当前数据集信息
        
        self.init_ui()
        
        # 定时器用于检查数据更新
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.check_for_updates)
        self.update_timer.start(1000)  # 每秒检查一次
        
    def init_ui(self):
        """初始化界面"""
        main_layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        
        # 新增：材料信息显示面板
        material_info_group = QGroupBox("当前材料信息")
        material_info_group.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        material_info_layout = QVBoxLayout(material_info_group)
        
        self.material_info_label = QLabel("等待数据集导入...")
        self.material_info_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                color: #6c757d;
            }
        """)
        material_info_layout.addWidget(self.material_info_label)
        
        splitter.addWidget(material_info_group)
        
        # 上半部分：学术可视化控制面板
        academic_group = QGroupBox("学术级别可视化生成")
        academic_group.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        academic_layout = QVBoxLayout(academic_group)
        
        # 学术可视化按钮区
        academic_buttons_layout = QHBoxLayout()
        
        self.generate_academic_btn = QPushButton(" 生成学术级别图表")
        self.generate_academic_btn.setStyleSheet("""
            QPushButton {
                background-color: #2E8B57;
                color: white;
                border: none;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #3CB371;
            }
            QPushButton:pressed {
                background-color: #228B22;
            }
        """)
        self.generate_academic_btn.clicked.connect(self.generate_academic_visualizations)
        
        self.view_academic_btn = QPushButton(" 查看生成的图表")
        self.view_academic_btn.setEnabled(False)
        self.view_academic_btn.clicked.connect(self.view_academic_plots)
        
        self.export_academic_btn = QPushButton(" 导出学术图表")
        self.export_academic_btn.setEnabled(False)
        self.export_academic_btn.clicked.connect(self.export_academic_plots)
        
        academic_buttons_layout.addWidget(self.generate_academic_btn)
        academic_buttons_layout.addWidget(self.view_academic_btn)
        academic_buttons_layout.addWidget(self.export_academic_btn)
        academic_buttons_layout.addStretch()
        
        academic_layout.addLayout(academic_buttons_layout)
        
        # 学术可视化状态显示
        self.academic_status_label = QLabel("等待优化数据...")
        self.academic_status_label.setStyleSheet("color: #666; font-style: italic;")
        academic_layout.addWidget(self.academic_status_label)
        
        splitter.addWidget(academic_group)
        
        # 下半部分：力场文件生成控制面板
        forcefield_group = QGroupBox("ReaxFF力场文件生成")
        forcefield_group.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        forcefield_layout = QVBoxLayout(forcefield_group)
        
        # 力场生成按钮区
        forcefield_buttons_layout = QHBoxLayout()
        
        self.generate_forcefield_btn = QPushButton(" 生成力场文件")
        self.generate_forcefield_btn.setStyleSheet("""
            QPushButton {
                background-color: #4169E1;
                color: white;
                border: none;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #6495ED;
            }
            QPushButton:pressed {
                background-color: #0000CD;
            }
        """)
        self.generate_forcefield_btn.clicked.connect(self.generate_force_field)
        
        self.view_forcefield_btn = QPushButton(" 查看力场文件")
        self.view_forcefield_btn.setEnabled(False)
        self.view_forcefield_btn.clicked.connect(self.view_force_field_files)
        
        self.export_forcefield_btn = QPushButton(" 导出力场文件")
        self.export_forcefield_btn.setEnabled(False)
        self.export_forcefield_btn.clicked.connect(self.export_force_field_files)
        
        forcefield_buttons_layout.addWidget(self.generate_forcefield_btn)
        forcefield_buttons_layout.addWidget(self.view_forcefield_btn)
        forcefield_buttons_layout.addWidget(self.export_forcefield_btn)
        forcefield_buttons_layout.addStretch()
        
        forcefield_layout.addLayout(forcefield_buttons_layout)
        
        # 力场生成状态显示
        self.forcefield_status_label = QLabel("等待优化结果...")
        self.forcefield_status_label.setStyleSheet("color: #666; font-style: italic;")
        forcefield_layout.addWidget(self.forcefield_status_label)
        
        splitter.addWidget(forcefield_group)
        
        # 底部：生成日志显示
        log_group = QGroupBox("生成日志")
        log_group.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
            }
        """)
        log_layout.addWidget(self.log_text)
        
        # 日志控制按钮
        log_buttons_layout = QHBoxLayout()
        clear_log_btn = QPushButton("🗑️ 清除日志")
        clear_log_btn.clicked.connect(self.log_text.clear)
        save_log_btn = QPushButton("💾 保存日志")
        save_log_btn.clicked.connect(self.save_log)
        
        log_buttons_layout.addWidget(clear_log_btn)
        log_buttons_layout.addWidget(save_log_btn)
        log_buttons_layout.addStretch()
        
        log_layout.addLayout(log_buttons_layout)
        splitter.addWidget(log_group)
        
        # 设置分割器比例
        splitter.setSizes([80, 200, 200, 100])
        
        main_layout.addWidget(splitter)
        
        self.log_message("🚀 增强可视化面板已初始化")
        
    def check_for_updates(self):
        """检查数据更新"""
        # 检查是否有可用的优化数据
        parent_window = self.parent()
        while parent_window and not hasattr(parent_window, 'search_path_history'):
            parent_window = parent_window.parent()
        
        if parent_window and hasattr(parent_window, 'search_path_history'):
            # 获取当前材料信息
            material_info = self.get_current_material_info(parent_window)
            if material_info != self.current_dataset_info:
                self.current_dataset_info = material_info
                self.update_material_info_display()
            
            if hasattr(parent_window, 'loss_history') and parent_window.loss_history:
                # 更新优化数据
                self.optimization_data = {
                    'iterations': list(range(1, len(parent_window.loss_history) + 1)),
                    'total_losses': parent_window.loss_history,
                    'train_losses': getattr(parent_window, 'train_loss_history', []),
                    'val_losses': getattr(parent_window, 'val_loss_history', []),
                    'search_path_history': parent_window.search_path_history
                }
                
                # 更新状态标签
                data_count = len(self.optimization_data['iterations'])
                material_text = f"（{self.current_material_name}）" if self.current_material_name else ""
                self.academic_status_label.setText(f" 已获取 {data_count} 个迭代数据，可生成学术图表 {material_text}")
                self.academic_status_label.setStyleSheet("color: #28a745; font-weight: bold;")
                
                if hasattr(parent_window, 'parameter_panel') and hasattr(parent_window.parameter_panel, 'parameter_table'):
                    # 获取优化参数
                    optimized_params = {}
                    table = parent_window.parameter_panel.parameter_table
                    for row in range(table.rowCount()):
                        param_name_item = table.item(row, 0)
                        param_value_item = table.item(row, 1)

                        # 🔥 修复：安全地获取参数名和值
                        param_name = param_name_item.text() if param_name_item and param_name_item.text().strip() else f"param_{row}"

                        # 🔥 修复：安全地转换参数值
                        try:
                            param_value_text = param_value_item.text() if param_value_item else ""
                            param_value = float(param_value_text) if param_value_text.strip() else 0.0
                        except (ValueError, AttributeError):
                            param_value = 0.0

                        optimized_params[param_name] = param_value
                    
                    if optimized_params:
                        self.optimization_data['optimized_params'] = optimized_params
                        material_text = f"（{self.current_material_name}）" if self.current_material_name else ""
                        self.forcefield_status_label.setText(f" 已获取 {len(optimized_params)} 个优化参数，可生成力场文件 {material_text}")
                        self.forcefield_status_label.setStyleSheet("color: #28a745; font-weight: bold;")
                        
    def get_current_material_info(self, parent_window):
        """获取当前材料信息 - 优化版本，参考jaxreaxff_3实现"""
        material_info = {
            'name': None,
            'datasets': [],
            'structures_count': 0,
            'parameters_count': 0,
            'dataset_paths': {}  # 新增：存储数据集路径信息
        }
        
        try:
            if hasattr(parent_window, 'dataset_handler') and parent_window.dataset_handler:
                # 获取数据集信息
                all_structures = parent_window.dataset_handler.get_all_structures()
                all_parameters = parent_window.dataset_handler.get_all_parameters()
                
                # 获取数据集路径信息
                if hasattr(parent_window.dataset_handler, 'dataset_paths'):
                    material_info['dataset_paths'] = parent_window.dataset_handler.dataset_paths
                
                if all_structures:
                    # 获取所有数据集名称
                    dataset_names = list(all_structures.keys())
                    if dataset_names:
                        # 清理数据集名称，移除路径前缀
                        cleaned_names = []
                        for name in dataset_names:
                            # 处理层次结构名称，如 "disulfide/valSet" -> "valSet"
                            if '/' in name:
                                parts = name.split('/')
                                if len(parts) >= 2:
                                    # 使用最后一部分作为显示名称
                                    display_name = parts[-1]
                                    # 特殊处理：如果最后一部分是通用名称，使用父级名称
                                    if display_name.lower() in ['valset', 'testset', 'trainset']:
                                        display_name = parts[0]  # 使用父级名称
                                else:
                                    display_name = name
                            else:
                                display_name = name
                            
                            # 特殊处理HNO3数据集
                            if display_name.lower() == 'hno3':
                                display_name = 'nitric_acid'
                            
                            cleaned_names.append(display_name)
                        
                        material_info['datasets'] = cleaned_names
                        material_info['structures_count'] = sum(len(structs) for structs in all_structures.values())
                        
                        # 设置材料名称
                        if len(cleaned_names) == 1:
                            material_info['name'] = cleaned_names[0]
                        else:
                            # 多个数据集时，使用组合名称
                            material_info['name'] = f"multi_material_{len(cleaned_names)}"
                        
                if all_parameters:
                    material_info['parameters_count'] = sum(len(params) for params in all_parameters.values())
                    
                # 更新当前材料名称
                if material_info['name'] and material_info['name'] != self.current_material_name:
                    self.current_material_name = material_info['name']
                    self.log_message(f" 检测到 {len(material_info['datasets'])} 个数据集: {', '.join(material_info['datasets'])}")
                    
        except Exception as e:
            self.log_message(f" 获取材料信息时出错: {e}")
            import traceback
            traceback.print_exc()
            
        return material_info
    
    def update_material_info_display(self):
        """更新材料信息显示 - 支持多数据集显示"""
        try:
            # 获取当前材料信息
            parent_window = self.parent()
            if parent_window:
                material_info = self.get_current_material_info(parent_window)
                
                if material_info['name'] and material_info['datasets']:
                    # 构建数据集列表显示文本
                    datasets = material_info['datasets']
                    if len(datasets) == 1:
                        datasets_text = datasets[0]
                        material_type = "单一材料"
                    else:
                        datasets_text = ', '.join(datasets[:3])
                        if len(datasets) > 3:
                            datasets_text += f' (+{len(datasets)-3} 更多)'
                        material_type = f"混合材料 ({len(datasets)} 个数据集)"
                    
                    info_text = f"""
 {material_type}: {datasets_text}
 数据集数量: {len(datasets)}
 结构总数: {material_info['structures_count']}
 参数总数: {material_info['parameters_count']}
                    """.strip()
                    
                    # 如果有多个数据集，提醒用户可以选择处理方式
                    if len(datasets) > 1:
                        info_text += f"\n 提示: 生成图表时可选择分别处理或合并处理"
                    
                    self.material_info_label.setText(info_text)
                    
                    # 根据数据集数量调整样式
                    if len(datasets) > 1:
                        # 多数据集 - 使用蓝色主题
                        style = """
                            QLabel {
                                background-color: #e3f2fd;
                                border: 1px solid #2196f3;
                                border-radius: 4px;
                                padding: 8px;
                                color: #0d47a1;
                                font-weight: bold;
                            }
                        """
                    else:
                        # 单数据集 - 使用绿色主题
                        style = """
                            QLabel {
                                background-color: #e8f5e8;
                                border: 1px solid #28a745;
                                border-radius: 4px;
                                padding: 8px;
                                color: #155724;
                                font-weight: bold;
                            }
                        """
                    self.material_info_label.setStyleSheet(style)
                    
                    # 更新当前数据集信息
                    self.current_dataset_info = material_info
                    
                else:
                    self.material_info_label.setText("等待数据集导入...")
                    self.material_info_label.setStyleSheet("""
                        QLabel {
                            background-color: #f8f9fa;
                            border: 1px solid #dee2e6;
                            border-radius: 4px;
                            padding: 8px;
                            color: #6c757d;
                        }
                    """)
                    self.current_dataset_info = {}
            
        except Exception as e:
            print(f" 更新材料信息显示时出错: {e}")
            self.material_info_label.setText("材料信息获取错误")
            self.material_info_label.setStyleSheet("""
                QLabel {
                    background-color: #fff3cd;
                    border: 1px solid #ffeaa7;
                    border-radius: 4px;
                    padding: 8px;
                    color: #856404;
                }
            """)
    
    def generate_academic_visualizations(self):
        """生成学术级别可视化 - 支持多数据集分别处理"""
        
        # 检查是否有真实优化数据
        has_real_data = (self.optimization_data and
                        'iterations' in self.optimization_data and
                        len(self.optimization_data.get('iterations', [])) > 5)

        if not has_real_data:
            # 直接提示用户需要先运行优化
            QMessageBox.information(
                self, "数据不足",
                "当前没有足够的优化数据来生成学术图表。\n\n"
                "请先导入数据集并运行优化，然后再生成图表。\n\n"
                "操作步骤：\n"
                "1. 导入数据集文件夹\n"
                "2. 选择要优化的参数\n"
                "3. 点击'开始优化'按钮\n"
                "4. 优化完成后再生成图表"
            )
            self.log_message(" 提示用户先运行优化。")
            return  # 确保完全退出
        
        # 获取父窗口的数据集信息
        material_info = self.get_current_material_info(self.parent())
        
        # 检查是否有多个数据集
        if len(material_info['datasets']) > 1:
            # 询问用户如何处理多个数据集
            choice_dialog = QMessageBox(self)
            choice_dialog.setWindowTitle("多数据集处理")
            choice_dialog.setText(f"检测到 {len(material_info['datasets'])} 个数据集：\n{', '.join(material_info['datasets'])}")
            choice_dialog.setInformativeText("请选择处理方式：")
            
            separate_btn = choice_dialog.addButton(" 分别生成", QMessageBox.AcceptRole)
            combined_btn = choice_dialog.addButton(" 合并生成", QMessageBox.AcceptRole)
            cancel_btn = choice_dialog.addButton(" 取消", QMessageBox.RejectRole)
            
            choice_dialog.exec_()
            clicked_button = choice_dialog.clickedButton()
            
            if clicked_button == cancel_btn:
                self.log_message(" 用户取消了可视化生成")
                return
            elif clicked_button == separate_btn:
                # 为每个数据集分别生成图表
                self.generate_separate_visualizations(material_info['datasets'])
                return
            # 否则继续合并生成
        
        # 单个数据集或合并生成的情况
        if not self.current_material_name:
            # 🔧 修复：使用实际的数据集名称，而不是unknown_material
            if material_info['name']:
                self.current_material_name = material_info['name']
            elif material_info['datasets']:
                # 如果有数据集列表，使用第一个数据集名称
                self.current_material_name = material_info['datasets'][0]
            else:
                self.current_material_name = "material"
        
        self.log_message(f" 开始为 {self.current_material_name} 生成学术级别可视化图表...")
        
        # 创建进度对话框
        progress = QProgressDialog("正在生成学术级别图表...", "取消", 0, 100, self)
        progress.setWindowModality(Qt.WindowModal)
        progress.setWindowTitle("生成学术图表")
        progress.show()
        
        # 创建工作线程
        self.academic_worker = AcademicVisualizationWorker(
            self.optimization_data,
            self.current_material_name
        )
        self.academic_worker.progress_updated.connect(progress.setValue)
        self.academic_worker.progress_updated.connect(
            lambda p, msg: progress.setLabelText(msg) if isinstance(msg, str) else None
        )
        self.academic_worker.visualization_finished.connect(self.on_academic_visualization_finished)
        self.academic_worker.error_occurred.connect(self.on_academic_error)
        
        # 连接取消信号
        progress.canceled.connect(self.academic_worker.terminate)
        
        self.academic_worker.start()
        
        # 显示进度对话框
        if progress.exec_() == QProgressDialog.Rejected:
            self.log_message(" 用户取消了可视化生成")
    

        
    def on_academic_visualization_finished(self, plot_paths):
        """学术可视化完成"""
        self.generated_academic_plots = plot_paths
        self.view_academic_btn.setEnabled(True)
        self.export_academic_btn.setEnabled(True)
        
        self.log_message(f" 学术图表生成完成！共生成 {len(plot_paths)} 个图表")
        for path in plot_paths:
            self.log_message(f"    {os.path.basename(path)}")
        
        QMessageBox.information(self, "生成完成", 
                              f"学术级别图表生成完成！\n共生成 {len(plot_paths)} 个高质量图表")
        
    def on_academic_error(self, error_msg):
        """学术可视化错误"""
        self.log_message(f" 学术图表生成失败: {error_msg}")
        QMessageBox.critical(self, "生成失败", f"学术图表生成失败：\n{error_msg}")
        
    def generate_force_field(self):
        """生成力场文件"""
        if 'optimized_params' not in self.optimization_data:
            QMessageBox.warning(self, "参数不足", "请先完成优化获取参数后再生成力场文件")
            return
        
        # 获取材料名称
        material_name, ok = QInputDialog.getText(self, "材料名称", "请输入材料名称:")
        if not ok or not material_name:
            material_name = "unknown_material"
        
        self.log_message(f" 开始为 {material_name} 生成力场文件...")
        
        # 创建进度对话框
        progress = QProgressDialog("正在生成力场文件...", "取消", 0, 100, self)
        progress.setWindowModality(Qt.WindowModal)
        progress.setWindowTitle("生成力场文件")
        
        # 创建工作线程
        self.forcefield_worker = ForceFieldWorker(
            self.optimization_data['optimized_params'],
            material_name,
            "PSO"  # 默认优化方法
        )
        self.forcefield_worker.progress_updated.connect(progress.setValue)
        self.forcefield_worker.progress_updated.connect(lambda p, msg: progress.setLabelText(msg))
        self.forcefield_worker.generation_finished.connect(self.on_force_field_finished)
        self.forcefield_worker.error_occurred.connect(self.on_force_field_error)
        
        # 连接取消信号
        progress.canceled.connect(self.forcefield_worker.terminate)
        
        self.forcefield_worker.start()
        progress.exec_()
        
    def on_force_field_finished(self, output_dir):
        """力场文件生成完成"""
        self.generated_force_field_dir = output_dir
        self.view_forcefield_btn.setEnabled(True)
        self.export_forcefield_btn.setEnabled(True)
        
        self.log_message(f" 力场文件生成完成！")
        self.log_message(f"    输出目录: {output_dir}")
        
        QMessageBox.information(self, "生成完成", 
                              f"ReaxFF力场文件生成完成！\n输出目录: {output_dir}")
        
    def on_force_field_error(self, error_msg):
        """力场文件生成错误"""
        self.log_message(f" 力场文件生成失败: {error_msg}")
        QMessageBox.critical(self, "生成失败", f"力场文件生成失败：\n{error_msg}")
        
    def view_academic_plots(self):
        """查看学术图表"""
        if not self.generated_academic_plots:
            return
        
        # 创建图表查看窗口
        viewer_window = AcademicPlotViewer(self.generated_academic_plots, self)
        viewer_window.exec_()
        
    def view_force_field_files(self):
        """查看力场文件"""
        if not self.generated_force_field_dir:
            return
        
        # 打开文件夹
        import subprocess
        import platform
        
        try:
            if platform.system() == "Windows":
                subprocess.Popen(f'explorer "{self.generated_force_field_dir}"')
            elif platform.system() == "Darwin":  # macOS
                subprocess.Popen(["open", self.generated_force_field_dir])
            else:  # Linux
                subprocess.Popen(["xdg-open", self.generated_force_field_dir])
                
            self.log_message(f" 已打开力场文件目录: {self.generated_force_field_dir}")
        except Exception as e:
            self.log_message(f" 打开目录失败: {e}")
            QMessageBox.warning(self, "打开失败", f"无法打开目录：\n{e}")
    
    def export_academic_plots(self):
        """导出学术图表"""
        if not self.generated_academic_plots:
            return
        
        export_dir = QFileDialog.getExistingDirectory(self, "选择导出目录")
        if export_dir:
            try:
                import shutil
                for plot_path in self.generated_academic_plots:
                    filename = os.path.basename(plot_path)
                    dest_path = os.path.join(export_dir, filename)
                    shutil.copy2(plot_path, dest_path)
                
                self.log_message(f" 学术图表已导出到: {export_dir}")
                QMessageBox.information(self, "导出完成", f"学术图表已导出到：\n{export_dir}")
            except Exception as e:
                self.log_message(f" 导出失败: {e}")
                QMessageBox.critical(self, "导出失败", f"导出失败：\n{e}")
    
    def export_force_field_files(self):
        """导出力场文件"""
        if not self.generated_force_field_dir:
            return
        
        export_dir = QFileDialog.getExistingDirectory(self, "选择导出目录")
        if export_dir:
            try:
                import shutil
                dest_dir = os.path.join(export_dir, os.path.basename(self.generated_force_field_dir))
                shutil.copytree(self.generated_force_field_dir, dest_dir)
                
                self.log_message(f" 力场文件已导出到: {dest_dir}")
                QMessageBox.information(self, "导出完成", f"力场文件已导出到：\n{dest_dir}")
            except Exception as e:
                self.log_message(f" 导出失败: {e}")
                QMessageBox.critical(self, "导出失败", f"导出失败：\n{e}")
    
    def save_log(self):
        """保存日志"""
        filename, _ = QFileDialog.getSaveFileName(self, "保存日志", "generation_log.txt", "文本文件 (*.txt)")
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())
                self.log_message(f" 日志已保存到: {filename}")
            except Exception as e:
                QMessageBox.critical(self, "保存失败", f"保存日志失败：\n{e}")
    
    def log_message(self, message):
        """添加日志消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.log_text.append(formatted_message)
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

class AcademicPlotViewer(QDialog):
    """图表查看器"""
    
    def __init__(self, plot_paths, parent=None):
        super().__init__(parent)
        self.plot_paths = plot_paths
        self.current_index = 0
        
        self.setWindowTitle("图表查看器")
        self.setGeometry(100, 100, 1000, 800)
        
        self.init_ui()
        self.show_current_plot()
        
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 图表显示区域
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("border: 1px solid #ccc; background-color: white;")
        layout.addWidget(self.image_label)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        prev_btn = QPushButton("◀ 上一张")
        prev_btn.clicked.connect(self.prev_plot)
        
        self.plot_info_label = QLabel()
        self.plot_info_label.setAlignment(Qt.AlignCenter)
        self.plot_info_label.setStyleSheet("font-weight: bold; color: #333;")
        
        next_btn = QPushButton("下一张 ▶")
        next_btn.clicked.connect(self.next_plot)
        
        button_layout.addWidget(prev_btn)
        button_layout.addWidget(self.plot_info_label)
        button_layout.addWidget(next_btn)
        
        layout.addLayout(button_layout)
        
    def show_current_plot(self):
        """显示当前图表"""
        if 0 <= self.current_index < len(self.plot_paths):
            plot_path = self.plot_paths[self.current_index]
            
            # 加载图片
            pixmap = QPixmap(plot_path)
            if not pixmap.isNull():
                # 调整图片大小以适应标签
                scaled_pixmap = pixmap.scaled(self.image_label.size(), 
                                            Qt.KeepAspectRatio, 
                                            Qt.SmoothTransformation)
                self.image_label.setPixmap(scaled_pixmap)
            
            # 更新信息标签
            plot_name = os.path.basename(plot_path)
            self.plot_info_label.setText(f"{plot_name} ({self.current_index + 1}/{len(self.plot_paths)})")
            
    def prev_plot(self):
        """上一张图表"""
        if self.current_index > 0:
            self.current_index -= 1
            self.show_current_plot()
            
    def next_plot(self):
        """下一张图表"""
        if self.current_index < len(self.plot_paths) - 1:
            self.current_index += 1
            self.show_current_plot()
    
    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        # 重新显示当前图表以适应新尺寸
        self.show_current_plot()

    def generate_separate_visualizations(self, dataset_names):
        """为多个数据集分别生成可视化 - 优化版本，参考jaxreaxff_3实现"""
        self.log_message(f" 开始为 {len(dataset_names)} 个数据集分别生成图表...")
        
        # 创建总进度对话框
        total_progress = QProgressDialog("正在为多个数据集生成图表...", "取消", 0, len(dataset_names), self)
        total_progress.setWindowModality(Qt.WindowModal)
        total_progress.setWindowTitle("批量生成学术图表")
        total_progress.show()
        
        generated_dirs = []
        
        for i, dataset_name in enumerate(dataset_names):
            total_progress.setValue(i)
            total_progress.setLabelText(f"正在处理数据集: {dataset_name} ({i+1}/{len(dataset_names)})")
            
            if total_progress.wasCanceled():
                self.log_message(" 用户取消了批量生成")
                break
            
            try:
                # 为每个数据集创建独立的输出目录
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                dataset_output_dir = f"academic_visualizations_{dataset_name}_{timestamp}"
                
                # 创建该数据集的可视化工作器
                worker = AcademicVisualizationWorker(
                    self.optimization_data,
                    dataset_output_dir,
                    dataset_name  # 传递数据集名称
                )
                
                # 连接信号
                worker.visualization_finished.connect(lambda paths, name=dataset_name: 
                    self.on_dataset_visualization_finished(paths, name))
                worker.error_occurred.connect(lambda error, name=dataset_name: 
                    self.on_dataset_visualization_error(error, name))
                
                # 启动工作线程
                worker.start()
                
                # 等待完成（简化处理）
                worker.wait()
                
                generated_dirs.append(dataset_output_dir)
                self.log_message(f" 数据集 {dataset_name} 的图表生成完成，保存在: {dataset_output_dir}")
                
            except Exception as e:
                self.log_message(f" 数据集 {dataset_name} 生成失败: {e}")
                import traceback
                traceback.print_exc()
        
        total_progress.setValue(len(dataset_names))
        
        if generated_dirs:
            QMessageBox.information(self, "批量生成完成", 
                                  f" 已为 {len(generated_dirs)} 个数据集分别生成学术图表！\n\n"
                                  f"生成的目录:\n" + "\n".join(generated_dirs))
            
            self.log_message(f" 批量可视化生成完成！共处理 {len(generated_dirs)} 个数据集")
            
            # 更新生成的图表列表
            self.generated_academic_plots.extend(generated_dirs)
        else:
            QMessageBox.warning(self, "生成失败", "没有成功生成任何图表，请检查数据集和优化数据。")
    
    def on_dataset_visualization_finished(self, plot_paths, dataset_name):
        """单个数据集可视化完成回调"""
        self.log_message(f" 数据集 {dataset_name} 的可视化图表生成完成: {len(plot_paths)} 个文件")
    
    def on_dataset_visualization_error(self, error_msg, dataset_name):
        """单个数据集可视化错误回调"""
        self.log_message(f" 数据集 {dataset_name} 的可视化生成失败: {error_msg}") 