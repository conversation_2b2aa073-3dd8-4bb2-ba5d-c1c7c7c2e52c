[ moleculetype ]
; molname	nrexcl
SOL		2

[ atoms ]
;   nr   type  resnr residue  atom   cgnr     charge       mass
#ifndef HEAVY_H
     1     OW      1    SOL     OW      1      -0.82   15.99940
     2      H      1    SOL    HW1      1       0.41    1.00800
     3      H      1    SOL    HW2      1       0.41    1.00800
#else
     1     OW      1    SOL     OW      1      -0.82    9.95140
     2      H      1    SOL    HW1      1       0.41    4.03200
     3      H      1    SOL    HW2      1       0.41    4.03200
#endif

#ifndef FLEXIBLE
[ settles ]
; OW	funct	doh	dhh
1	1	0.1	0.16330

[ exclusions ]
1	2	3
2	1	3
3	1	2
#else
[ bonds ]
; i	j	funct	length	force.c.
1	2	1	0.1	345000	0.1     345000
1	3	1	0.1	345000	0.1     345000
	
[ angles ]
; i	j	k	funct	angle	force.c.
2	1	3	1	109.47	383	109.47	383
#endif
