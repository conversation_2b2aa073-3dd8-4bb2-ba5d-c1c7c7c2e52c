import jax
import jax.numpy as jnp
import flax.linen as nn
from typing import Dict, List, Tuple, Callable, Optional, Union, Any, Sequence
from functools import partial
import numpy as np
import optax
from .base_model import BaseModel

class MultiHeadAttention(nn.Module):
    """多头注意力模块"""
    
    hidden_dim: int
    num_heads: int
    dropout_rate: float = 0.1
    attention_dropout_rate: float = 0.1
    
    @nn.compact
    def __call__(
        self, 
        q: jnp.ndarray,  # [batch, q_len, hidden_dim]
        k: jnp.ndarray,  # [batch, kv_len, hidden_dim]
        v: jnp.ndarray,  # [batch, kv_len, hidden_dim]
        mask: Optional[jnp.ndarray] = None,  # [batch, q_len, kv_len]
        deterministic: bool = True
    ) -> jnp.ndarray:
        """执行多头注意力计算"""
        batch_size, q_len, hidden_dim = q.shape
        kv_len = k.shape[1]
        
        # 检查隐藏维度是否能被头数整除
        assert hidden_dim % self.num_heads == 0, f"Hidden dim {hidden_dim} must be divisible by num_heads {self.num_heads}"
        head_dim = hidden_dim // self.num_heads
        
        # 定义线性投影
        dense_q = nn.Dense(features=hidden_dim, name='query')
        dense_k = nn.Dense(features=hidden_dim, name='key')
        dense_v = nn.Dense(features=hidden_dim, name='value')
        dense_o = nn.Dense(features=hidden_dim, name='output')
        
        # 投影查询、键、值
        q = dense_q(q)  # [batch, q_len, hidden_dim]
        k = dense_k(k)  # [batch, kv_len, hidden_dim]
        v = dense_v(v)  # [batch, kv_len, hidden_dim]
        
        # 重塑形状，分离头
        def reshape_for_multihead(x, head_dim):
            # 从 [batch, len, hidden_dim] 重塑到 [batch, len, num_heads, head_dim]
            new_shape = x.shape[:-1] + (self.num_heads, head_dim)
            x = x.reshape(new_shape)
            # 转置到 [batch, num_heads, len, head_dim]
            return x.transpose((0, 2, 1, 3))
        
        q = reshape_for_multihead(q, head_dim)  # [batch, num_heads, q_len, head_dim]
        k = reshape_for_multihead(k, head_dim)  # [batch, num_heads, kv_len, head_dim]
        v = reshape_for_multihead(v, head_dim)  # [batch, num_heads, kv_len, head_dim]
        
        # 计算注意力分数
        scale = jnp.sqrt(head_dim).astype(k.dtype)
        attn_logits = jnp.matmul(q, jnp.swapaxes(k, -1, -2)) / scale  # [batch, num_heads, q_len, kv_len]
        
        # 应用掩码 (如果提供)
        if mask is not None:
            # 扩展掩码形状以匹配注意力形状
            mask = mask[:, jnp.newaxis, :, :]  # [batch, 1, q_len, kv_len]
            attn_logits = jnp.where(mask == 0, -1e9, attn_logits)
            
        # 应用softmax获取注意力权重
        attn_weights = jax.nn.softmax(attn_logits, axis=-1)  # [batch, num_heads, q_len, kv_len]
        
        # 应用注意力dropout
        if not deterministic:
            attn_weights = nn.Dropout(
                rate=self.attention_dropout_rate,
                deterministic=deterministic
            )(attn_weights)
            
        # 计算加权和
        context = jnp.matmul(attn_weights, v)  # [batch, num_heads, q_len, head_dim]
        
        # 重塑回原始形状
        context = context.transpose((0, 2, 1, 3))  # [batch, q_len, num_heads, head_dim]
        context = context.reshape((batch_size, q_len, hidden_dim))  # [batch, q_len, hidden_dim]
        
        # 最终线性层
        output = dense_o(context)  # [batch, q_len, hidden_dim]
        
        # 应用dropout
        if not deterministic:
            output = nn.Dropout(
                rate=self.dropout_rate,
                deterministic=deterministic
            )(output)
            
        return output

class TransformerEncoderBlock(nn.Module):
    """Transformer编码器块"""
    
    hidden_dim: int
    num_heads: int
    mlp_dim: int
    dropout_rate: float = 0.1
    attention_dropout_rate: float = 0.1
    
    @nn.compact
    def __call__(
        self, 
        inputs: jnp.ndarray,
        mask: Optional[jnp.ndarray] = None,
        deterministic: bool = True
    ) -> jnp.ndarray:
        """执行Transformer编码器块计算"""
        # 注意力子层
        x = nn.LayerNorm()(inputs)
        attn_output = MultiHeadAttention(
            hidden_dim=self.hidden_dim,
            num_heads=self.num_heads,
            dropout_rate=self.dropout_rate,
            attention_dropout_rate=self.attention_dropout_rate
        )(x, x, x, mask=mask, deterministic=deterministic)
        x = inputs + attn_output
        
        # 前馈网络子层
        y = nn.LayerNorm()(x)
        y = nn.Dense(features=self.mlp_dim)(y)
        y = nn.gelu(y)
        
        if not deterministic:
            y = nn.Dropout(rate=self.dropout_rate)(y, deterministic=deterministic)
            
        y = nn.Dense(features=self.hidden_dim)(y)
        
        if not deterministic:
            y = nn.Dropout(rate=self.dropout_rate)(y, deterministic=deterministic)
            
        return x + y

class TransformerEncoder(nn.Module):
    """Transformer编码器"""
    
    hidden_dim: int
    num_heads: int
    num_layers: int
    mlp_dim: int
    dropout_rate: float = 0.1
    attention_dropout_rate: float = 0.1
    
    @nn.compact
    def __call__(
        self, 
        inputs: jnp.ndarray,
        mask: Optional[jnp.ndarray] = None,
        deterministic: bool = True
    ) -> jnp.ndarray:
        """执行Transformer编码器计算"""
        x = inputs
        
        # 应用多个编码器层
        for i in range(self.num_layers):
            x = TransformerEncoderBlock(
                hidden_dim=self.hidden_dim,
                num_heads=self.num_heads,
                mlp_dim=self.mlp_dim,
                dropout_rate=self.dropout_rate,
                attention_dropout_rate=self.attention_dropout_rate
            )(x, mask=mask, deterministic=deterministic)
            
        # 最终层归一化
        x = nn.LayerNorm()(x)
        
        return x

def create_attention_mask(atom_mask: jnp.ndarray) -> jnp.ndarray:
    """
    创建注意力掩码
    
    Args:
        atom_mask: 原子掩码 [batch_size, max_atoms]
        
    Returns:
        attention_mask: 注意力掩码 [batch_size, max_atoms, max_atoms]
    """
    # 创建广播掩码
    return atom_mask[:, :, jnp.newaxis] * atom_mask[:, jnp.newaxis, :]

def sinusoidal_position_encoding(
    max_len: int, 
    hidden_dim: int, 
    min_freq: float = 1.0, 
    max_freq: float = 1.0e4
) -> jnp.ndarray:
    """
    创建正弦位置编码
    
    Args:
        max_len: 最大序列长度
        hidden_dim: 隐藏层维度
        min_freq: 最小频率
        max_freq: 最大频率
        
    Returns:
        position_encoding: 位置编码 [max_len, hidden_dim]
    """
    # 确保hidden_dim是偶数
    assert hidden_dim % 2 == 0, f"Hidden dimension must be even, got {hidden_dim}"
    
    # 计算频率带
    freq_bands = jnp.logspace(
        jnp.log10(min_freq), 
        jnp.log10(max_freq), 
        hidden_dim // 2
    )
    
    # 创建位置索引
    positions = jnp.arange(max_len)[:, jnp.newaxis]  # [max_len, 1]
    
    # 计算角度
    angles = positions * freq_bands[jnp.newaxis, :]  # [max_len, hidden_dim//2]
    
    # 计算正弦和余弦
    sin_encoded = jnp.sin(angles)  # [max_len, hidden_dim//2]
    cos_encoded = jnp.cos(angles)  # [max_len, hidden_dim//2]
    
    # 交错正弦和余弦
    position_encoding = jnp.concatenate([sin_encoded, cos_encoded], axis=1)
    
    return position_encoding

class AtomTransformer(BaseModel):
    """原子Transformer模型"""
    
    hidden_dim: int = 128
    num_heads: int = 8
    num_layers: int = 3
    mlp_dim: int = 256
    num_atom_types: int = 10
    max_atoms: int = 50
    dropout_rate: float = 0.1
    attention_dropout_rate: float = 0.1
    
    @nn.compact
    def __call__(
        self, 
        atom_types: jnp.ndarray,  # [batch_size, max_atoms]
        atom_positions: jnp.ndarray,  # [batch_size, max_atoms, 3]
        atom_mask: jnp.ndarray,  # [batch_size, max_atoms]
        predict_forces: bool = False,
        training: bool = False
    ) -> Dict[str, jnp.ndarray]:
        """
        前向传播
        
        Args:
            atom_types: 原子类型
            atom_positions: 原子坐标
            atom_mask: 原子掩码 (1表示有效原子，0表示填充)
            predict_forces: 是否预测力
            training: 是否处于训练模式
            
        Returns:
            outputs: 包含能量和可选力的字典
        """
        batch_size, max_atoms = atom_types.shape
        deterministic = not training
        
        # 为原子类型创建嵌入
        atom_embedding = nn.Embed(
            num_embeddings=self.num_atom_types,
            features=self.hidden_dim
        )(atom_types)  # [batch_size, max_atoms, hidden_dim]
        
        # 创建位置编码
        if self.is_mutable_collection('params'):
            # 初始化时创建位置编码
            position_encoding = sinusoidal_position_encoding(
                self.max_atoms, 
                self.hidden_dim
            )
            self.param(
                'position_encoding', 
                lambda _: position_encoding, 
                position_encoding
            )
        else:
            # 使用现有的位置编码
            position_encoding = self.param('position_encoding', None)
        
        # 添加位置编码到嵌入
        x = atom_embedding + position_encoding
        
        # 创建注意力掩码
        attention_mask = create_attention_mask(atom_mask)
        
        # 计算距离并创建RBF特征
        if predict_forces:
            # 使用自动微分跟踪位置梯度
            positions_vgrad = atom_positions
        else:
            # 停止梯度，如果不需要力
            positions_vgrad = jax.lax.stop_gradient(atom_positions)
        
        # 计算原子对距离
        diffs = positions_vgrad[:, :, jnp.newaxis, :] - positions_vgrad[:, jnp.newaxis, :, :]
        distances = jnp.sqrt(jnp.sum(diffs**2, axis=-1))  # [batch_size, max_atoms, max_atoms]
        
        # 创建RBF特征 (正余弦编码)
        num_rbf = 16
        rbf_centers = jnp.linspace(0.0, 5.0, num_rbf)  # [num_rbf]
        rbf_width = 0.5
        rbf_feats = jnp.exp(-(distances[:, :, :, jnp.newaxis] - rbf_centers)**2 / rbf_width**2)
        rbf_feats = rbf_feats.reshape(batch_size, max_atoms, max_atoms, num_rbf)
        
        # 将RBF特征转换为注意力偏置
        rbf_dense = nn.Dense(features=self.num_heads)(rbf_feats)  # [batch_size, max_atoms, max_atoms, num_heads]
        attention_bias = jnp.transpose(rbf_dense, (0, 3, 1, 2))  # [batch_size, num_heads, max_atoms, max_atoms]
        
        # 将嵌入通过Transformer编码器
        encoder_outputs = TransformerEncoder(
            hidden_dim=self.hidden_dim,
            num_heads=self.num_heads,
            num_layers=self.num_layers,
            mlp_dim=self.mlp_dim,
            dropout_rate=self.dropout_rate,
            attention_dropout_rate=self.attention_dropout_rate
        )(x, mask=attention_mask, deterministic=deterministic)
        
        # 聚合原子表示以获得系统表示
        system_mask = atom_mask.reshape(batch_size, max_atoms, 1)  # [batch_size, max_atoms, 1]
        masked_outputs = encoder_outputs * system_mask
        system_repr = jnp.sum(masked_outputs, axis=1) / jnp.sum(system_mask, axis=1)  # [batch_size, hidden_dim]
        
        # 预测能量
        energy_dense1 = nn.Dense(features=self.hidden_dim)(system_repr)
        energy_dense1 = nn.gelu(energy_dense1)
        energy_dense2 = nn.Dense(features=1)(energy_dense1)
        predicted_energy = energy_dense2.squeeze(-1)  # [batch_size]
        
        outputs = {'energy': predicted_energy}
        
        # 如果需要，计算力
        if predict_forces:
            forces = -1 * jax.grad(lambda pos: jnp.sum(
                AtomTransformer(
                    hidden_dim=self.hidden_dim,
                    num_heads=self.num_heads,
                    num_layers=self.num_layers,
                    mlp_dim=self.mlp_dim,
                    num_atom_types=self.num_atom_types,
                    max_atoms=self.max_atoms,
                    dropout_rate=self.dropout_rate,
                    attention_dropout_rate=self.attention_dropout_rate
                ).apply(
                    {'params': self.variables['params']},
                    atom_types,
                    pos,
                    atom_mask,
                    False,
                    False
                )['energy']
            ))(atom_positions)
            outputs['forces'] = forces
            
        return outputs

class MolecularTransformer(nn.Module):
    """分子Transformer模型，用于长程相互作用建模"""
    
    hidden_dim: int = 256
    ff_dim: int = 512
    num_layers: int = 6
    num_heads: int = 8
    dropout_rate: float = 0.1
    max_atoms: int = 100
    num_atom_types: int = 10
    max_distance: float = 10.0
    use_3d_attention: bool = True
    
    def setup(self):
        # 原子嵌入
        self.atom_embedding = nn.Embed(
            num_embeddings=self.num_atom_types + 1,  # +1 for padding
            features=self.hidden_dim
        )
        
        # Transformer编码器层
        self.encoder_layers = [
            TransformerEncoderBlock(
                hidden_dim=self.hidden_dim,
                num_heads=self.num_heads,
                mlp_dim=self.ff_dim,
                dropout_rate=self.dropout_rate
            )
            for _ in range(self.num_layers)
        ]
        
        # 最终层归一化
        self.final_layer_norm = nn.LayerNorm()
        
        # 输出层
        self.output_projection = nn.Dense(features=1)
        
    def _create_3d_positional_embedding(self, positions):
        """创建3D位置嵌入"""
        batch_size, num_atoms, _ = positions.shape
        
        # 计算相对距离
        pos_diff = positions[:, :, None, :] - positions[:, None, :, :]  # [B, N, N, 3]
        distance = jnp.sqrt(jnp.sum(pos_diff**2, axis=-1))  # [B, N, N]
        
        # 创建距离编码
        min_freq = 1.0
        max_freq = 1.0e4
        num_freqs = self.hidden_dim // 6  # 每个坐标分配hidden_dim/6的维度
        
        # 创建频率带
        freq_bands = jnp.logspace(
            jnp.log10(min_freq), 
            jnp.log10(max_freq), 
            num_freqs
        )
        
        # 计算角度
        angles = distance[:, :, :, None] * freq_bands[None, None, None, :]  # [B, N, N, num_freqs]
        
        # 计算正弦和余弦
        sin_encoded = jnp.sin(angles)  # [B, N, N, num_freqs]
        cos_encoded = jnp.cos(angles)  # [B, N, N, num_freqs]
        
        # 对相对位置的每个坐标分量进行编码
        pos_diff_norm = pos_diff / (distance[:, :, :, None] + 1e-8)  # 归一化方向向量 [B, N, N, 3]
        
        # 为x, y, z方向创建编码
        dir_encoding = []
        for i in range(3):
            # 方向编码，考虑方向上的正弦和余弦
            dir_angles = pos_diff_norm[:, :, :, i, None] * freq_bands[None, None, None, :]
            dir_sin = jnp.sin(dir_angles)
            dir_cos = jnp.cos(dir_angles)
            dir_encoding.append(jnp.concatenate([dir_sin, dir_cos], axis=-1))
        
        # 连接所有编码
        pos_encoding = jnp.concatenate(
            [sin_encoded, cos_encoded] + dir_encoding, 
            axis=-1
        )
        
        return pos_encoding
    
    def __call__(
        self,
        atom_types,  # [B, N]
        positions,   # [B, N, 3]
        atom_mask,   # [B, N]
        training=False
    ):
        """
        前向传播
        
        Args:
            atom_types: 原子类型索引 [batch_size, num_atoms]
            positions: 原子坐标 [batch_size, num_atoms, 3]
            atom_mask: 原子掩码 [batch_size, num_atoms] (1=有效原子, 0=填充)
            training: 是否处于训练模式
            
        Returns:
            output: 预测值
        """
        deterministic = not training
        batch_size, num_atoms = atom_types.shape
        
        # 创建嵌入
        x = self.atom_embedding(atom_types)  # [B, N, D]
        
        # 创建注意力掩码
        attention_mask = create_attention_mask(atom_mask)  # [B, N, N]
        
        # 如果使用3D注意力，创建位置编码
        if self.use_3d_attention:
            pos_encodings = self._create_3d_positional_embedding(positions)
            
            # 创建每个原子对的位置感知偏置
            pos_bias = nn.Dense(features=self.num_heads)(pos_encodings)  # [B, N, N, H]
            pos_bias = jnp.transpose(pos_bias, (0, 3, 1, 2))  # [B, H, N, N]
        else:
            pos_bias = None
        
        # 应用Transformer编码器层
        for layer in self.encoder_layers:
            x = layer(x, mask=attention_mask, deterministic=deterministic)
        
        # 最终层归一化
        x = self.final_layer_norm(x)  # [B, N, D]
        
        # 通过掩码，仅考虑有效原子
        masked_x = x * atom_mask[:, :, None]  # [B, N, D]
        
        # 聚合特征
        pooled_x = jnp.sum(masked_x, axis=1) / jnp.sum(atom_mask, axis=1, keepdims=True)  # [B, D]
        
        # 预测输出
        output = self.output_projection(pooled_x)  # [B, 1]
        
        return output.squeeze(-1)  # [B]

class LongRangeInteractionModel(BaseModel):
    """长程相互作用模型，结合Transformer和分子力场"""
    
    hidden_dim: int = 256
    ff_dim: int = 512
    num_layers: int = 6
    num_heads: int = 8
    dropout_rate: float = 0.1
    max_atoms: int = 100
    num_atom_types: int = 10
    max_distance: float = 10.0
    use_3d_attention: bool = True
    cutoff_short: float = 3.0  # 短程相互作用截断
    cutoff_long: float = 8.0   # 长程相互作用截断
    
    def setup(self):
        # 短程相互作用模型
        self.short_range_mlp = nn.Sequential([
            nn.Dense(features=self.hidden_dim),
            nn.gelu,
            nn.Dense(features=self.hidden_dim),
            nn.gelu,
            nn.Dense(features=self.hidden_dim)
        ])
        
        # 长程相互作用模型 (Transformer)
        self.long_range_transformer = MolecularTransformer(
            hidden_dim=self.hidden_dim,
            ff_dim=self.ff_dim,
            num_layers=self.num_layers,
            num_heads=self.num_heads,
            dropout_rate=self.dropout_rate,
            max_atoms=self.max_atoms,
            num_atom_types=self.num_atom_types,
            max_distance=self.max_distance,
            use_3d_attention=self.use_3d_attention
        )
        
        # 输出层
        self.output_projection = nn.Dense(features=1)
        
    def _compute_pair_features(self, atom_types, positions, atom_mask):
        """计算原子对特征"""
        batch_size, num_atoms = atom_types.shape
        
        # 创建原子类型对
        atom_types_i = atom_types[:, :, None]  # [B, N, 1]
        atom_types_j = atom_types[:, None, :]  # [B, 1, N]
        
        # 计算距离
        rij = positions[:, :, None, :] - positions[:, None, :, :]  # [B, N, N, 3]
        dij = jnp.sqrt(jnp.sum(rij**2, axis=-1) + 1e-8)  # [B, N, N]
        
        # 创建截断函数
        def cutoff_fn(d, cutoff):
            """平滑截断函数"""
            x = d / cutoff
            x = jnp.where(x < 1.0, 0.5 * jnp.cos(jnp.pi * x) + 0.5, 0.0)
            return x
        
        # 计算短程和长程截断
        cutoff_short = cutoff_fn(dij, self.cutoff_short)  # [B, N, N]
        cutoff_long = cutoff_fn(dij, self.cutoff_long) - cutoff_fn(dij, self.cutoff_short)  # [B, N, N]
        
        # 应用掩码
        mask_ij = atom_mask[:, :, None] * atom_mask[:, None, :]  # [B, N, N]
        cutoff_short = cutoff_short * mask_ij
        cutoff_long = cutoff_long * mask_ij
        
        return {
            'distance': dij,
            'direction': rij / (dij[:, :, :, None] + 1e-8),
            'atom_types_i': atom_types_i,
            'atom_types_j': atom_types_j,
            'cutoff_short': cutoff_short,
            'cutoff_long': cutoff_long,
            'mask': mask_ij
        }
        
    def __call__(
        self,
        atom_types,  # [B, N]
        positions,   # [B, N, 3]
        atom_mask,   # [B, N]
        training=False
    ):
        """
        前向传播
        
        Args:
            atom_types: 原子类型索引 [batch_size, num_atoms]
            positions: 原子坐标 [batch_size, num_atoms, 3]
            atom_mask: 原子掩码 [batch_size, num_atoms] (1=有效原子, 0=填充)
            training: 是否处于训练模式
            
        Returns:
            output: 预测的能量
        """
        # 计算原子对特征
        pair_features = self._compute_pair_features(atom_types, positions, atom_mask)
        
        # 短程相互作用
        short_range_energy = self._compute_short_range(atom_types, positions, atom_mask, pair_features)
        
        # 长程相互作用
        long_range_energy = self.long_range_transformer(
            atom_types,
            positions,
            atom_mask,
            training=training
        )
        
        # 总能量
        total_energy = short_range_energy + long_range_energy
        
        return total_energy
    
    def _compute_short_range(self, atom_types, positions, atom_mask, pair_features):
        """计算短程相互作用能量"""
        batch_size, num_atoms = atom_types.shape
        
        # 为每个原子创建嵌入
        atom_embed = nn.Embed(
            num_embeddings=self.num_atom_types + 1,  # +1 for padding
            features=self.hidden_dim // 2
        )(atom_types)  # [B, N, D//2]
        
        # 计算原子对表示
        dij = pair_features['distance']  # [B, N, N]
        cutoff_short = pair_features['cutoff_short']  # [B, N, N]
        
        # 创建径向基函数
        rbf_centers = jnp.linspace(0.0, self.cutoff_short, 16)
        rbf_width = 0.5
        rbf_feats = jnp.exp(-(dij[:, :, :, None] - rbf_centers)**2 / rbf_width**2)  # [B, N, N, 16]
        
        # 创建原子对特征
        atom_i = atom_embed[:, :, None, :].repeat(num_atoms, axis=2)  # [B, N, N, D//2]
        atom_j = atom_embed[:, None, :, :].repeat(num_atoms, axis=1)  # [B, N, N, D//2]
        
        # 连接特征
        pair_feats = jnp.concatenate([atom_i, atom_j, rbf_feats], axis=-1)  # [B, N, N, D+16]
        
        # 通过MLP
        pair_energy = self.short_range_mlp(pair_feats)  # [B, N, N, D]
        pair_energy = jnp.sum(pair_energy, axis=-1)  # [B, N, N]
        
        # 应用截断和掩码
        masked_energy = pair_energy * cutoff_short * pair_features['mask']
        
        # 避免双重计数
        masked_energy = 0.5 * masked_energy
        
        # 对所有原子对求和
        short_range_energy = jnp.sum(masked_energy, axis=(1, 2))  # [B]
        
        return short_range_energy 