;
;	File 'mdout.mdp' was generated
;	By user: jw (1000)
;	On host: jw
;	At date: Fri Sep  8 10:42:25 2023
;
;	Created by:
;	                     :-) GROMACS - gmx grompp, 2022.2 (-:
;	
;	Executable:   /home/<USER>/Programs/gromacs/bin/gmx_mpi
;	Data prefix:  /home/<USER>/Programs/gromacs
;	Working dir:  /home/<USER>/Desktop/Alkane_Combustion/C5/1_Gromacs/3_eq_NVT_1000K
;	Command line:
;	  gmx_mpi grompp -f eq.mdp -c em.gro -p system.top -o eq.tpr -maxwarn 2

; VARIOUS PREPROCESSING OPTIONS
; Preprocessor information: use cpp syntax.
; e.g.: -I/home/<USER>/doe -I/home/<USER>/roe
include                  = 
; e.g.: -DPOSRES -DFLEXIBLE (note these variable names are case sensitive)
define                   = 

; RUN CONTROL PARAMETERS
integrator               = md
; Start time and timestep in ps
tinit                    = 0
dt                       = 0.001
nsteps                   = 5000000
; For exact run continuation or redoing part of a run
init-step                = 0
; Part index is updated automatically on checkpointing (keeps files separate)
simulation-part          = 1
; Multiple time-stepping
mts                      = no
; mode for center of mass motion removal
comm-mode                = Linear
; number of steps for center of mass motion removal
nstcomm                  = 100
; group(s) for center of mass motion removal
comm-grps                = 

; LANGEVIN DYNAMICS OPTIONS
; Friction coefficient (amu/ps) and random seed
bd-fric                  = 0
ld-seed                  = -1

; ENERGY MINIMIZATION OPTIONS
; Force tolerance and initial step-size
emtol                    = 10
emstep                   = 0.01
; Max number of iterations in relax-shells
niter                    = 20
; Step size (ps^2) for minimization of flexible constraints
fcstep                   = 0
; Frequency of steepest descents steps when doing CG
nstcgsteep               = 1000
nbfgscorr                = 10

; TEST PARTICLE INSERTION OPTIONS
rtpi                     = 0.05

; OUTPUT CONTROL OPTIONS
; Output frequency for coords (x), velocities (v) and forces (f)
nstxout                  = 500
nstvout                  = 500
nstfout                  = 0
; Output frequency for energies to log file and energy file
nstlog                   = 500
nstcalcenergy            = 100
nstenergy                = 500
; Output frequency and precision for .xtc file
nstxout-compressed       = 0
compressed-x-precision   = 1000
; This selects the subset of atoms for the compressed
; trajectory file. You can select multiple groups. By
; default, all atoms will be written.
compressed-x-grps        = 
; Selection of energy groups
energygrps               = 

; NEIGHBORSEARCHING PARAMETERS
; cut-off scheme (Verlet: particle based cut-offs)
cutoff-scheme            = Verlet
; nblist update frequency
nstlist                  = 10
; Periodic boundary conditions: xyz, no, xy
pbc                      = xyz
periodic-molecules       = no
; Allowed energy error due to the Verlet buffer in kJ/mol/ps per atom,
; a value of -1 means: use rlist
verlet-buffer-tolerance  = 0.005
; nblist cut-off        
rlist                    = 1
; long-range cut-off for switched potentials

; OPTIONS FOR ELECTROSTATICS AND VDW
; Method for doing electrostatics
coulombtype              = PME
coulomb-modifier         = Potential-shift-Verlet
rcoulomb-switch          = 0
rcoulomb                 = 0.9
; Relative dielectric constant for the medium and the reaction field
epsilon-r                = 1
epsilon-rf               = 0
; Method for doing Van der Waals
vdw-type                 = Cut-off
vdw-modifier             = Potential-shift-Verlet
; cut-off lengths       
rvdw-switch              = 0
rvdw                     = 0.9
; Apply long range dispersion corrections for Energy and Pressure
DispCorr                 = EnerPres
; Extension of the potential lookup tables beyond the cut-off
table-extension          = 1
; Separate tables between energy group pairs
energygrp-table          = 
; Spacing for the PME/PPPM FFT grid
fourierspacing           = 0.16
; FFT grid size, when a value is 0 fourierspacing will be used
fourier-nx               = 0
fourier-ny               = 0
fourier-nz               = 0
; EWALD/PME/PPPM parameters
pme_order                = 4
ewald-rtol               = 1e-05
ewald-rtol-lj            = 0.001
lj-pme-comb-rule         = Geometric
ewald-geometry           = 3d
epsilon-surface          = 0
implicit-solvent         = no

; OPTIONS FOR WEAK COUPLING ALGORITHMS
; Temperature coupling  
tcoupl                   = V-rescale
nsttcouple               = -1
nh-chain-length          = 10
print-nose-hoover-chain-variables = no
; Groups to couple separately
tc-grps                  = ALK	O2
; Time constant (ps) and reference temperature (K)
tau_t                    = 0.1   0.1
ref_t                    = 1000   1000
; pressure coupling     
pcoupl                   = no
pcoupltype               = isotropic
nstpcouple               = -1
; Time constant (ps), compressibility (1/bar) and reference P (bar)
tau_p                    = 10.0
compressibility          = 1.0
ref_p                    = 1.0
; Scaling of reference coordinates, No, All or COM
refcoord_scaling         = com

; OPTIONS FOR QMMM calculations
QMMM                     = no
; Groups treated with MiMiC
QMMM-grps                = 

; SIMULATED ANNEALING  
; Type of annealing for each temperature group (no/single/periodic)
annealing                = 
; Number of time points to use for specifying annealing in each group
annealing-npoints        = 
; List of times at the annealing points for each group
annealing-time           = 
; Temp. at each annealing point, for each group.
annealing-temp           = 

; GENERATE VELOCITIES FOR STARTUP RUN
gen_vel                  = yes
gen-temp                 = 300
gen-seed                 = -2359299

; OPTIONS FOR BONDS    
constraints              = h-bonds
; Type of constraint algorithm
constraint_algorithm     = lincs
; Do not constrain the start configuration
continuation             = no
; Use successive overrelaxation to reduce the number of shake iterations
Shake-SOR                = no
; Relative tolerance of shake
shake-tol                = 0.0001
; Highest order in the expansion of the constraint coupling matrix
lincs_order              = 4
; Number of iterations in the final step of LINCS. 1 is fine for
; normal simulations, but use 2 to conserve energy in NVE runs.
; For energy minimization with constraints it should be 4 to 8.
lincs_iter               = 1
; Lincs will write a warning to the stderr if in one step a bond
; rotates over more degrees than
lincs-warnangle          = 30
; Convert harmonic bonds to morse potentials
morse                    = no

; ENERGY GROUP EXCLUSIONS
; Pairs of energy groups for which all non-bonded interactions are excluded
energygrp-excl           = 

; WALLS                
; Number of walls, type, atom types, densities and box-z scale factor for Ewald
nwall                    = 0
wall-type                = 9-3
wall-r-linpot            = -1
wall-atomtype            = 
wall-density             = 
wall-ewald-zfac          = 3

; COM PULLING          
pull                     = no

; AWH biasing          
awh                      = no

; ENFORCED ROTATION    
; Enforced rotation: No or Yes
rotation                 = no

; Group to display and/or manipulate in interactive MD session
IMD-group                = 

; NMR refinement stuff 
; Distance restraints type: No, Simple or Ensemble
disre                    = No
; Force weighting of pairs in one distance restraint: Conservative or Equal
disre-weighting          = Conservative
; Use sqrt of the time averaged times the instantaneous violation
disre-mixed              = no
disre-fc                 = 1000
disre-tau                = 0
; Output frequency for pair distances to energy file
nstdisreout              = 100
; Orientation restraints: No or Yes
orire                    = no
; Orientation restraints force constant and tau for time averaging
orire-fc                 = 0
orire-tau                = 0
orire-fitgrp             = 
; Output frequency for trace(SD) and S to energy file
nstorireout              = 100

; Free energy variables
free-energy              = no
couple-moltype           = 
couple-lambda0           = vdw-q
couple-lambda1           = vdw-q
couple-intramol          = no
init-lambda              = -1
init-lambda-state        = -1
delta-lambda             = 0
nstdhdl                  = 50
fep-lambdas              = 
mass-lambdas             = 
coul-lambdas             = 
vdw-lambdas              = 
bonded-lambdas           = 
restraint-lambdas        = 
temperature-lambdas      = 
calc-lambda-neighbors    = 1
init-lambda-weights      = 
dhdl-print-energy        = no
sc-function              = beutler
sc-alpha                 = 0
sc-power                 = 1
sc-r-power               = 6
sc-sigma                 = 0.3
sc-coul                  = no
sc-gapsys-scale-linpoint-lj = 0.85
sc-gapsys-scale-linpoint-q = 0.3
sc-gapsys-sigma-lj       = 0.3
separate-dhdl-file       = yes
dhdl-derivatives         = yes
dh_hist_size             = 0
dh_hist_spacing          = 0.1

; Non-equilibrium MD stuff
acc-grps                 = 
accelerate               = 
freezegrps               = 
freezedim                = 
cos-acceleration         = 0
deform                   = 

; simulated tempering variables
simulated-tempering      = no
simulated-tempering-scaling = geometric
sim-temp-low             = 300
sim-temp-high            = 300

; Ion/water position swapping for computational electrophysiology setups
; Swap positions along direction: no, X, Y, Z
swapcoords               = no
adress                   = no

; User defined thingies
user1-grps               = 
user2-grps               = 
userint1                 = 0
userint2                 = 0
userint3                 = 0
userint4                 = 0
userreal1                = 0
userreal2                = 0
userreal3                = 0
userreal4                = 0
; Electric fields
; Format for electric-field-x, etc. is: four real variables:
; amplitude (V/nm), frequency omega (1/ps), time for the pulse peak (ps),
; and sigma (ps) width of the pulse. Omega = 0 means static field,
; sigma = 0 means no pulse, leaving the field to be a cosine function.
electric-field-x         = 0 0 0 0
electric-field-y         = 0 0 0 0
electric-field-z         = 0 0 0 0

; Density guided simulation
density-guided-simulation-active = false

; QM/MM with CP2K
qmmm-cp2k-active         = false
