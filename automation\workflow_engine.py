"""
自动化工作流引擎 - Jenkins/Airflow风格的调度系统
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, field
from enum import Enum
import threading
import queue
import pickle
import os

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRY = "retry"


@dataclass
class Task:
    """工作流任务"""
    id: str
    name: str
    function: Callable
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    dependencies: List[str] = field(default_factory=list)
    retry_count: int = 0
    max_retries: int = 3
    timeout: Optional[int] = None
    status: TaskStatus = TaskStatus.PENDING
    result: Any = None
    error: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'status': self.status.value,
            'retry_count': self.retry_count,
            'max_retries': self.max_retries,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'error': self.error
        }


@dataclass
class Workflow:
    """工作流定义"""
    id: str
    name: str
    tasks: Dict[str, Task] = field(default_factory=dict)
    schedule: Optional[str] = None  # cron表达式
    created_at: datetime = field(default_factory=datetime.now)
    
    def add_task(self, task: Task):
        """添加任务"""
        self.tasks[task.id] = task
        
    def get_ready_tasks(self) -> List[Task]:
        """获取可执行的任务"""
        ready_tasks = []
        for task in self.tasks.values():
            if task.status == TaskStatus.PENDING:
                # 检查依赖是否完成
                deps_completed = all(
                    self.tasks[dep_id].status == TaskStatus.SUCCESS
                    for dep_id in task.dependencies
                    if dep_id in self.tasks
                )
                if deps_completed:
                    ready_tasks.append(task)
        return ready_tasks


class WorkflowEngine:
    """工作流执行引擎"""
    
    def __init__(self, max_workers: int = 4, storage_path: str = "workflows"):
        self.max_workers = max_workers
        self.storage_path = storage_path
        self.workflows: Dict[str, Workflow] = {}
        self.running_tasks: Dict[str, threading.Thread] = {}
        self.task_queue = queue.Queue()
        self.worker_pool = []
        self.running = False
        
        # 创建存储目录
        os.makedirs(storage_path, exist_ok=True)
        
        # 启动工作线程
        self._start_workers()
        
    def _start_workers(self):
        """启动工作线程"""
        for i in range(self.max_workers):
            worker = threading.Thread(target=self._worker_loop, daemon=True)
            worker.start()
            self.worker_pool.append(worker)
            
    def _worker_loop(self):
        """工作线程循环"""
        while True:
            try:
                task = self.task_queue.get(timeout=1)
                if task is None:  # 停止信号
                    break
                self._execute_task(task)
                self.task_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"工作线程错误: {e}")
                
    def _execute_task(self, task: Task):
        """执行单个任务"""
        task.status = TaskStatus.RUNNING
        task.start_time = datetime.now()
        
        try:
            logger.info(f"开始执行任务: {task.name}")
            
            # 执行任务函数
            if task.timeout:
                # 带超时的执行
                result = self._execute_with_timeout(task)
            else:
                result = task.function(*task.args, **task.kwargs)
                
            task.result = result
            task.status = TaskStatus.SUCCESS
            task.end_time = datetime.now()
            
            logger.info(f"任务完成: {task.name}")
            
        except Exception as e:
            task.error = str(e)
            task.end_time = datetime.now()
            
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                task.status = TaskStatus.RETRY
                logger.warning(f"任务失败，准备重试: {task.name}, 错误: {e}")
                # 重新加入队列
                self.task_queue.put(task)
            else:
                task.status = TaskStatus.FAILED
                logger.error(f"任务最终失败: {task.name}, 错误: {e}")
                
    def _execute_with_timeout(self, task: Task):
        """带超时的任务执行"""
        result_queue = queue.Queue()
        
        def target():
            try:
                result = task.function(*task.args, **task.kwargs)
                result_queue.put(('success', result))
            except Exception as e:
                result_queue.put(('error', e))
                
        thread = threading.Thread(target=target)
        thread.start()
        thread.join(timeout=task.timeout)
        
        if thread.is_alive():
            # 超时处理
            raise TimeoutError(f"任务超时: {task.timeout}秒")
            
        try:
            status, result = result_queue.get_nowait()
            if status == 'error':
                raise result
            return result
        except queue.Empty:
            raise RuntimeError("任务执行异常结束")
            
    def create_workflow(self, workflow_id: str, name: str) -> Workflow:
        """创建工作流"""
        workflow = Workflow(id=workflow_id, name=name)
        self.workflows[workflow_id] = workflow
        return workflow
        
    def submit_workflow(self, workflow: Workflow):
        """提交工作流执行"""
        logger.info(f"提交工作流: {workflow.name}")
        
        # 将准备好的任务加入队列
        ready_tasks = workflow.get_ready_tasks()
        for task in ready_tasks:
            self.task_queue.put(task)
            
    def get_workflow_status(self, workflow_id: str) -> Dict:
        """获取工作流状态"""
        if workflow_id not in self.workflows:
            return {'error': 'Workflow not found'}
            
        workflow = self.workflows[workflow_id]
        
        # 统计任务状态
        status_counts = {}
        for status in TaskStatus:
            status_counts[status.value] = sum(
                1 for task in workflow.tasks.values() 
                if task.status == status
            )
            
        return {
            'workflow_id': workflow_id,
            'name': workflow.name,
            'created_at': workflow.created_at.isoformat(),
            'task_counts': status_counts,
            'tasks': [task.to_dict() for task in workflow.tasks.values()]
        }
        
    def save_workflow(self, workflow_id: str):
        """保存工作流到磁盘"""
        if workflow_id in self.workflows:
            filepath = os.path.join(self.storage_path, f"{workflow_id}.pkl")
            with open(filepath, 'wb') as f:
                pickle.dump(self.workflows[workflow_id], f)
                
    def load_workflow(self, workflow_id: str) -> Optional[Workflow]:
        """从磁盘加载工作流"""
        filepath = os.path.join(self.storage_path, f"{workflow_id}.pkl")
        if os.path.exists(filepath):
            with open(filepath, 'rb') as f:
                workflow = pickle.load(f)
                self.workflows[workflow_id] = workflow
                return workflow
        return None


class ReaxFFOptimizationWorkflow:
    """ReaxFF优化专用工作流"""
    
    def __init__(self, engine: WorkflowEngine):
        self.engine = engine
        
    def create_optimization_pipeline(self, 
                                   config: Dict,
                                   workflow_id: str = None) -> str:
        """创建优化流水线
        
        Args:
            config: 优化配置
            workflow_id: 工作流ID
            
        Returns:
            工作流ID
        """
        if workflow_id is None:
            workflow_id = f"reaxff_opt_{int(time.time())}"
            
        workflow = self.engine.create_workflow(workflow_id, "ReaxFF参数优化")
        
        # 1. 数据预处理任务
        preprocess_task = Task(
            id="preprocess",
            name="数据预处理",
            function=self._preprocess_data,
            args=(config.get('data_path'),),
            timeout=300
        )
        workflow.add_task(preprocess_task)
        
        # 2. 初始参数生成任务
        param_init_task = Task(
            id="param_init",
            name="生成初始参数",
            function=self._generate_initial_params,
            args=(config.get('param_config'),),
            dependencies=["preprocess"],
            timeout=600
        )
        workflow.add_task(param_init_task)
        
        # 3. 优化任务（并行）
        optimizers = config.get('optimizers', ['PSO', 'GA'])
        for i, optimizer in enumerate(optimizers):
            opt_task = Task(
                id=f"optimize_{optimizer.lower()}",
                name=f"{optimizer}优化",
                function=self._run_optimization,
                args=(optimizer, config),
                dependencies=["param_init"],
                timeout=3600
            )
            workflow.add_task(opt_task)
            
        # 4. 结果合并任务
        merge_task = Task(
            id="merge_results",
            name="合并优化结果",
            function=self._merge_results,
            dependencies=[f"optimize_{opt.lower()}" for opt in optimizers],
            timeout=300
        )
        workflow.add_task(merge_task)
        
        # 5. 验证任务
        validate_task = Task(
            id="validate",
            name="结果验证",
            function=self._validate_results,
            dependencies=["merge_results"],
            timeout=600
        )
        workflow.add_task(validate_task)
        
        # 6. 报告生成任务
        report_task = Task(
            id="generate_report",
            name="生成报告",
            function=self._generate_report,
            args=(workflow_id,),
            dependencies=["validate"],
            timeout=300
        )
        workflow.add_task(report_task)
        
        return workflow_id
        
    def _preprocess_data(self, data_path: str) -> Dict:
        """数据预处理"""
        logger.info("开始数据预处理")
        # 模拟数据预处理
        time.sleep(2)
        return {'status': 'success', 'processed_files': 10}
        
    def _generate_initial_params(self, param_config: Dict) -> Dict:
        """生成初始参数"""
        logger.info("生成初始参数")
        # 模拟参数生成
        time.sleep(3)
        return {'status': 'success', 'param_sets': 5}
        
    def _run_optimization(self, optimizer: str, config: Dict) -> Dict:
        """运行优化"""
        logger.info(f"运行{optimizer}优化")
        # 模拟优化过程
        time.sleep(10)
        return {
            'optimizer': optimizer,
            'best_error': 0.001 + hash(optimizer) % 100 / 100000,
            'iterations': 100
        }
        
    def _merge_results(self) -> Dict:
        """合并结果"""
        logger.info("合并优化结果")
        time.sleep(1)
        return {'status': 'success', 'best_overall_error': 0.0005}
        
    def _validate_results(self) -> Dict:
        """验证结果"""
        logger.info("验证结果")
        time.sleep(2)
        return {'status': 'success', 'validation_score': 0.95}
        
    def _generate_report(self, workflow_id: str) -> Dict:
        """生成报告"""
        logger.info("生成优化报告")
        time.sleep(1)
        return {'status': 'success', 'report_path': f'reports/{workflow_id}.html'}


# 使用示例
def create_sample_workflow():
    """创建示例工作流"""
    engine = WorkflowEngine(max_workers=2)
    reaxff_workflow = ReaxFFOptimizationWorkflow(engine)
    
    config = {
        'data_path': '/path/to/data',
        'param_config': {'method': 'GAN'},
        'optimizers': ['PSO', 'GA', 'QuantumAnnealing']
    }
    
    workflow_id = reaxff_workflow.create_optimization_pipeline(config)
    workflow = engine.workflows[workflow_id]
    engine.submit_workflow(workflow)
    
    return engine, workflow_id
