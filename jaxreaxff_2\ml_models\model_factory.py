from typing import Dict, Any, Optional, Type
from .base_model import BaseModel
from .energy_model import EnergyModel
from .force_model import ForceModel
from .charge_model import ChargeModel
from .multitask_model import MultitaskModel

class ModelFactory:
    """模型工厂类，用于创建不同类型的模型实例"""
    
    _models: Dict[str, Type[BaseModel]] = {
        'energy': EnergyModel,
        'force': ForceModel,
        'charge': ChargeModel,
        'multitask': MultitaskModel
    }
    
    @classmethod
    def create_model(
        cls,
        model_type: str,
        **kwargs
    ) -> BaseModel:
        """
        创建模型实例
        
        Args:
            model_type: 模型类型 ('energy', 'force', 'charge', 或 'multitask')
            **kwargs: 模型参数
            
        Returns:
            model: 模型实例
            
        Raises:
            ValueError: 如果模型类型不支持
        """
        if model_type not in cls._models:
            raise ValueError(
                f"Unsupported model type: {model_type}. "
                f"Supported types are: {list(cls._models.keys())}"
            )
            
        model_class = cls._models[model_type]
        return model_class(**kwargs)
    
    @classmethod
    def register_model(cls, name: str, model_class: Type[BaseModel]):
        """
        注册新的模型类型
        
        Args:
            name: 模型类型名称
            model_class: 模型类
        """
        cls._models[name] = model_class
    
    @classmethod
    def get_available_models(cls) -> Dict[str, Type[BaseModel]]:
        """
        获取所有可用的模型类型
        
        Returns:
            models: 模型类型字典
        """
        return cls._models.copy() 