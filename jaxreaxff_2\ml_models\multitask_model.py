import jax
import jax.numpy as jnp
import flax.linen as nn
from typing import Dict, Any, Tuple, Optional, List
from .base_model import BaseModel

class SharedEncoder(nn.Module):
    """
    共享编码器，用于提取所有任务共用的特征
    """
    hidden_dim: int
    num_layers: int
    dropout_rate: float
    activation: str
    
    def setup(self):
        self.layers = []
        for i in range(self.num_layers):
            self.layers.append(nn.Dense(self.hidden_dim))
            self.layers.append(nn.Dropout(self.dropout_rate))
            if self.activation == 'relu':
                self.layers.append(nn.relu)
            elif self.activation == 'gelu':
                self.layers.append(nn.gelu)
            else:
                raise ValueError(f"Unsupported activation: {self.activation}")
    
    def __call__(self, inputs: jnp.ndarray, training: bool = False) -> jnp.ndarray:
        x = inputs
        
        for layer in self.layers:
            if isinstance(layer, nn.Dropout):
                x = layer(x, deterministic=not training)
            else:
                x = layer(x)
                
        return x

class TaskSpecificHead(nn.Module):
    """
    任务特定的输出头
    """
    hidden_dim: int
    output_dim: int
    num_layers: int
    dropout_rate: float
    activation: str
    
    def setup(self):
        self.layers = []
        for i in range(self.num_layers):
            self.layers.append(nn.Dense(self.hidden_dim))
            self.layers.append(nn.Dropout(self.dropout_rate))
            if self.activation == 'relu':
                self.layers.append(nn.relu)
            elif self.activation == 'gelu':
                self.layers.append(nn.gelu)
            else:
                raise ValueError(f"Unsupported activation: {self.activation}")
                
        self.output_layer = nn.Dense(self.output_dim)
    
    def __call__(self, inputs: jnp.ndarray, training: bool = False) -> jnp.ndarray:
        x = inputs
        
        for layer in self.layers:
            if isinstance(layer, nn.Dropout):
                x = layer(x, deterministic=not training)
            else:
                x = layer(x)
                
        return self.output_layer(x)

class MultitaskModel(BaseModel):
    """多任务学习模型，可以同时预测能量、力和电荷"""
    
    def __init__(
        self,
        shared_hidden_dim: int = 256,
        shared_num_layers: int = 3,
        task_hidden_dim: int = 128,
        task_num_layers: int = 2,
        dropout_rate: float = 0.1,
        activation: str = 'relu',
        tasks: List[str] = ['energy', 'force', 'charge'],
        use_charge_conservation: bool = True
    ):
        """
        初始化多任务学习模型
        
        Args:
            shared_hidden_dim: 共享编码器的隐藏层维度
            shared_num_layers: 共享编码器的层数
            task_hidden_dim: 任务特定头的隐藏层维度
            task_num_layers: 任务特定头的层数
            dropout_rate: Dropout比率
            activation: 激活函数
            tasks: 要执行的任务列表
            use_charge_conservation: 是否使用电荷守恒约束
        """
        super().__init__()
        self.shared_hidden_dim = shared_hidden_dim
        self.shared_num_layers = shared_num_layers
        self.task_hidden_dim = task_hidden_dim
        self.task_num_layers = task_num_layers
        self.dropout_rate = dropout_rate
        self.activation = activation
        self.tasks = tasks
        self.use_charge_conservation = use_charge_conservation
        
        # 定义共享编码器
        self.encoder = SharedEncoder(
            hidden_dim=shared_hidden_dim,
            num_layers=shared_num_layers,
            dropout_rate=dropout_rate,
            activation=activation
        )
        
        # 定义任务特定的输出头
        self.task_heads = {}
        if 'energy' in tasks:
            self.task_heads['energy'] = TaskSpecificHead(
                hidden_dim=task_hidden_dim,
                output_dim=1,  # 能量是标量
                num_layers=task_num_layers,
                dropout_rate=dropout_rate,
                activation=activation
            )
            
        if 'force' in tasks:
            self.task_heads['force'] = TaskSpecificHead(
                hidden_dim=task_hidden_dim,
                output_dim=3,  # 力是三维向量
                num_layers=task_num_layers,
                dropout_rate=dropout_rate,
                activation=activation
            )
            
        if 'charge' in tasks:
            self.task_heads['charge'] = TaskSpecificHead(
                hidden_dim=task_hidden_dim,
                output_dim=1,  # 电荷是标量
                num_layers=task_num_layers,
                dropout_rate=dropout_rate,
                activation=activation
            )
            
        # 初始化参数
        self.params = None
    
    def initialize(self, rng: jax.random.PRNGKey, input_shape: Tuple[int, ...]) -> Dict[str, Any]:
        """
        初始化模型参数
        
        Args:
            rng: 随机数生成器
            input_shape: 输入张量的形状
            
        Returns:
            params: 模型参数字典
        """
        # 创建虚拟输入
        dummy_input = jnp.zeros(input_shape)
        
        # 初始化参数
        variables = self.init(rng, dummy_input, training=False)
        return variables['params']
    
    def __call__(
        self, 
        inputs: jnp.ndarray, 
        atom_mask: Optional[jnp.ndarray] = None,
        training: bool = False
    ) -> Dict[str, jnp.ndarray]:
        """
        模型前向传播
        
        Args:
            inputs: 输入数据 [batch_size, n_atoms, feature_dim]
            atom_mask: 原子掩码 [batch_size, n_atoms] (可选)
            training: 是否处于训练模式
            
        Returns:
            outputs: 包含各任务预测结果的字典
        """
        batch_size, n_atoms, feature_dim = inputs.shape
        
        # 用共享编码器提取特征
        # 首先重塑以处理每个原子
        x_flat = inputs.reshape(-1, feature_dim)
        encoded_flat = self.encoder(x_flat, training=training)
        
        # 重塑回原始形状
        encoded = encoded_flat.reshape(batch_size, n_atoms, -1)
        
        # 对每个任务应用特定的输出头
        outputs = {}
        
        if 'energy' in self.tasks:
            # 对于能量，我们需要汇总所有原子的贡献
            atom_energies = self.task_heads['energy'](encoded_flat, training=training)
            atom_energies = atom_energies.reshape(batch_size, n_atoms)
            
            # 如果提供了掩码，使用它来掩盖虚拟原子
            if atom_mask is not None:
                system_energy = jnp.sum(atom_energies * atom_mask, axis=1, keepdims=True)
            else:
                system_energy = jnp.sum(atom_energies, axis=1, keepdims=True)
                
            outputs['energy'] = system_energy
        
        if 'force' in self.tasks:
            # 对于力，我们需要预测每个原子的三维向量
            atom_forces_flat = self.task_heads['force'](encoded_flat, training=training)
            atom_forces = atom_forces_flat.reshape(batch_size, n_atoms, 3)
            
            # 如果提供了掩码，使用它来掩盖虚拟原子
            if atom_mask is not None:
                atom_forces = atom_forces * atom_mask[:, :, None]
                
            outputs['force'] = atom_forces
            
        if 'charge' in self.tasks:
            # 对于电荷，我们需要预测每个原子的电荷值
            atom_charges_flat = self.task_heads['charge'](encoded_flat, training=training)
            atom_charges = atom_charges_flat.reshape(batch_size, n_atoms)
            
            # 应用电荷守恒约束 (总电荷为零)
            if self.use_charge_conservation:
                if atom_mask is not None:
                    # 计算每个系统中原子的平均电荷
                    total_charges = jnp.sum(atom_charges * atom_mask, axis=1, keepdims=True)  
                    atom_counts = jnp.sum(atom_mask, axis=1, keepdims=True)
                    avg_charge = total_charges / jnp.maximum(atom_counts, 1.0)
                    
                    # 减去平均电荷来确保总电荷为零
                    atom_charges = atom_charges - avg_charge * atom_mask
                else:
                    # 如果没有掩码，假设所有原子都有效
                    total_charges = jnp.sum(atom_charges, axis=1, keepdims=True)
                    avg_charge = total_charges / n_atoms
                    atom_charges = atom_charges - avg_charge
            
            outputs['charge'] = atom_charges
            
        return outputs
    
    def predict(self, inputs: jnp.ndarray, atom_mask: Optional[jnp.ndarray] = None) -> Dict[str, jnp.ndarray]:
        """
        模型预测
        
        Args:
            inputs: 输入数据
            atom_mask: 原子掩码 (可选)
            
        Returns:
            predictions: 模型预测结果
        """
        return self(inputs, atom_mask=atom_mask, training=False)
    
    def get_metrics(self) -> Dict[str, float]:
        """
        获取模型指标
        
        Returns:
            metrics: 模型指标字典
        """
        return {
            'shared_hidden_dim': self.shared_hidden_dim,
            'shared_num_layers': self.shared_num_layers,
            'task_hidden_dim': self.task_hidden_dim,
            'task_num_layers': self.task_num_layers,
            'dropout_rate': self.dropout_rate,
            'activation': self.activation,
            'tasks': self.tasks,
            'use_charge_conservation': self.use_charge_conservation
        }
    
    def save(self, path: str):
        """
        保存模型参数
        
        Args:
            path: 保存路径
        """
        if self.params is None:
            raise ValueError("Model parameters not initialized")
        
        # 将参数转换为numpy数组
        params_np = jax.tree_map(lambda x: jnp.array(x), self.params)
        
        # 保存为numpy文件
        jnp.save(path, params_np)
    
    def load(self, path: str):
        """
        加载模型参数
        
        Args:
            path: 加载路径
        """
        # 加载numpy数组
        params_np = jnp.load(path)
        
        # 转换回JAX数组
        self.params = jax.tree_map(lambda x: jnp.array(x), params_np) 