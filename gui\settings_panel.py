"""
ReaxFFOpt 设置面板
用于管理程序全局设置
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QCheckBox, QGroupBox, QFormLayout,
                            QComboBox, QSpinBox, QDoubleSpinBox, QTabWidget,
                            QLineEdit, QFileDialog, QColorDialog, QFontDialog)
from PyQt5.QtCore import Qt, pyqtSignal, QSettings
from PyQt5.QtGui import QFont, QColor


class SettingsPanel(QWidget):
    """设置面板类，用于配置应用程序的各种设置"""
    
    # 定义信号
    settings_changed = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 创建设置对象
        self.settings = QSettings("ReaxFFOpt", "ReaxFFOptApp")
        
        # 创建主布局
        main_layout = QVBoxLayout(self)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        
        # ===== 常规设置选项卡 =====
        general_tab = QWidget()
        general_layout = QVBoxLayout(general_tab)
        
        # 数据目录设置
        data_group = QGroupBox("数据目录")
        data_layout = QFormLayout(data_group)
        
        self.data_dir_edit = QLineEdit()
        self.data_dir_edit.setText(self.settings.value("data_directory", ""))
        self.data_dir_edit.setReadOnly(True)
        
        data_dir_layout = QHBoxLayout()
        data_dir_layout.addWidget(self.data_dir_edit)
        
        self.browse_data_button = QPushButton("浏览...")
        self.browse_data_button.clicked.connect(self.browse_data_directory)
        data_dir_layout.addWidget(self.browse_data_button)
        
        data_layout.addRow("数据存储目录:", data_dir_layout)
        general_layout.addWidget(data_group)
        
        # 结果输出设置
        output_group = QGroupBox("结果输出")
        output_layout = QFormLayout(output_group)
        
        self.auto_save_check = QCheckBox("自动保存结果")
        self.auto_save_check.setChecked(self.settings.value("auto_save_results", True, type=bool))
        output_layout.addRow("", self.auto_save_check)
        
        self.save_interval_spin = QSpinBox()
        self.save_interval_spin.setRange(1, 60)
        self.save_interval_spin.setValue(self.settings.value("save_interval", 10, type=int))
        output_layout.addRow("保存间隔(分钟):", self.save_interval_spin)
        
        general_layout.addWidget(output_group)
        
        # 性能设置
        performance_group = QGroupBox("性能设置")
        performance_layout = QFormLayout(performance_group)
        
        self.max_threads_spin = QSpinBox()
        self.max_threads_spin.setRange(1, 32)
        self.max_threads_spin.setValue(self.settings.value("max_threads", 4, type=int))
        performance_layout.addRow("最大线程数:", self.max_threads_spin)
        
        self.memory_limit_spin = QSpinBox()
        self.memory_limit_spin.setRange(1, 64)
        self.memory_limit_spin.setValue(self.settings.value("memory_limit", 8, type=int))
        self.memory_limit_spin.setSuffix(" GB")
        performance_layout.addRow("内存限制:", self.memory_limit_spin)
        
        general_layout.addWidget(performance_group)
        
        # ===== 可视化选项卡 =====
        visual_tab = QWidget()
        visual_layout = QVBoxLayout(visual_tab)
        
        # 图表设置
        plot_group = QGroupBox("图表设置")
        plot_layout = QFormLayout(plot_group)
        
        self.plot_style_combo = QComboBox()
        self.plot_style_combo.addItems(["默认", "科学风格", "暗色主题", "彩色"])
        current_style = self.settings.value("plot_style", "默认")
        self.plot_style_combo.setCurrentText(current_style)
        plot_layout.addRow("图表风格:", self.plot_style_combo)
        
        self.dpi_spin = QSpinBox()
        self.dpi_spin.setRange(72, 600)
        self.dpi_spin.setValue(self.settings.value("plot_dpi", 100, type=int))
        plot_layout.addRow("图表DPI:", self.dpi_spin)
        
        self.show_grid_check = QCheckBox("显示网格线")
        self.show_grid_check.setChecked(self.settings.value("show_grid", True, type=bool))
        plot_layout.addRow("", self.show_grid_check)
        
        visual_layout.addWidget(plot_group)
        
        # 颜色设置
        color_group = QGroupBox("颜色设置")
        color_layout = QFormLayout(color_group)
        
        # 主曲线颜色
        self.primary_color_button = QPushButton()
        self.primary_color = QColor(self.settings.value("primary_color", "#1f77b4"))
        self.update_color_button(self.primary_color_button, self.primary_color)
        self.primary_color_button.clicked.connect(self.choose_primary_color)
        color_layout.addRow("主曲线颜色:", self.primary_color_button)
        
        # 次曲线颜色
        self.secondary_color_button = QPushButton()
        self.secondary_color = QColor(self.settings.value("secondary_color", "#ff7f0e"))
        self.update_color_button(self.secondary_color_button, self.secondary_color)
        self.secondary_color_button.clicked.connect(self.choose_secondary_color)
        color_layout.addRow("次曲线颜色:", self.secondary_color_button)
        
        visual_layout.addWidget(color_group)
        
        # 字体设置
        font_group = QGroupBox("字体设置")
        font_layout = QFormLayout(font_group)
        
        self.font_button = QPushButton("选择字体...")
        self.font_button.clicked.connect(self.choose_font)
        
        font_family = self.settings.value("font_family", "Arial")
        font_size = self.settings.value("font_size", 10, type=int)
        self.current_font = QFont(font_family, font_size)
        self.font_label = QLabel(f"{font_family}, {font_size}pt")
        
        font_box = QHBoxLayout()
        font_box.addWidget(self.font_label)
        font_box.addWidget(self.font_button)
        
        font_layout.addRow("图表字体:", font_box)
        
        visual_layout.addWidget(font_group)
        
        # ===== 高级选项选项卡 =====
        advanced_tab = QWidget()
        advanced_layout = QVBoxLayout(advanced_tab)
        
        # 计算引擎设置
        engine_group = QGroupBox("计算引擎")
        engine_layout = QFormLayout(engine_group)
        
        self.engine_combo = QComboBox()
        self.engine_combo.addItems(["内置ReaxFF", "外部LAMMPS", "ASE"])
        current_engine = self.settings.value("calculation_engine", "内置ReaxFF")
        self.engine_combo.setCurrentText(current_engine)
        engine_layout.addRow("默认计算引擎:", self.engine_combo)
        
        self.lammps_path_edit = QLineEdit()
        self.lammps_path_edit.setText(self.settings.value("lammps_path", ""))
        
        lammps_path_layout = QHBoxLayout()
        lammps_path_layout.addWidget(self.lammps_path_edit)
        
        self.browse_lammps_button = QPushButton("浏览...")
        self.browse_lammps_button.clicked.connect(self.browse_lammps_path)
        lammps_path_layout.addWidget(self.browse_lammps_button)
        
        engine_layout.addRow("LAMMPS路径:", lammps_path_layout)
        
        advanced_layout.addWidget(engine_group)
        
        # 量子计算设置
        quantum_group = QGroupBox("量子计算设置")
        quantum_layout = QFormLayout(quantum_group)
        
        self.quantum_backend_combo = QComboBox()
        self.quantum_backend_combo.addItems(["模拟器", "IBM Quantum"])
        current_backend = self.settings.value("quantum_backend", "模拟器")
        self.quantum_backend_combo.setCurrentText(current_backend)
        quantum_layout.addRow("量子后端:", self.quantum_backend_combo)
        
        self.api_key_edit = QLineEdit()
        self.api_key_edit.setText(self.settings.value("quantum_api_key", ""))
        self.api_key_edit.setEchoMode(QLineEdit.Password)
        quantum_layout.addRow("API密钥:", self.api_key_edit)
        
        advanced_layout.addWidget(quantum_group)
        
        # 添加选项卡到选项卡小部件
        tab_widget.addTab(general_tab, "常规设置")
        tab_widget.addTab(visual_tab, "可视化设置")
        tab_widget.addTab(advanced_tab, "高级设置")
        
        main_layout.addWidget(tab_widget)
        
        # 添加控制按钮
        buttons_layout = QHBoxLayout()
        
        self.apply_button = QPushButton("应用")
        self.apply_button.clicked.connect(self.apply_settings)
        buttons_layout.addWidget(self.apply_button)
        
        self.reset_button = QPushButton("重置")
        self.reset_button.clicked.connect(self.reset_settings)
        buttons_layout.addWidget(self.reset_button)
        
        main_layout.addLayout(buttons_layout)
    
    def browse_data_directory(self):
        """浏览并选择数据目录"""
        directory = QFileDialog.getExistingDirectory(
            self, "选择数据目录", self.data_dir_edit.text()
        )
        
        if directory:
            self.data_dir_edit.setText(directory)
    
    def browse_lammps_path(self):
        """浏览并选择LAMMPS可执行文件路径"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择LAMMPS可执行文件", self.lammps_path_edit.text()
        )
        
        if file_path:
            self.lammps_path_edit.setText(file_path)
    
    def choose_primary_color(self):
        """选择主曲线颜色"""
        color = QColorDialog.getColor(self.primary_color, self, "选择主曲线颜色")
        
        if color.isValid():
            self.primary_color = color
            self.update_color_button(self.primary_color_button, color)
    
    def choose_secondary_color(self):
        """选择次曲线颜色"""
        color = QColorDialog.getColor(self.secondary_color, self, "选择次曲线颜色")
        
        if color.isValid():
            self.secondary_color = color
            self.update_color_button(self.secondary_color_button, color)
    
    def update_color_button(self, button, color):
        """更新颜色按钮的样式"""
        button.setStyleSheet(
            f"background-color: {color.name()}; min-width: 60px; min-height: 20px;"
        )
    
    def choose_font(self):
        """选择图表字体"""
        font, ok = QFontDialog.getFont(self.current_font, self, "选择图表字体")
        
        if ok:
            self.current_font = font
            self.font_label.setText(f"{font.family()}, {font.pointSize()}pt")
    
    def apply_settings(self):
        """应用设置"""
        # 保存设置到QSettings
        self.settings.setValue("data_directory", self.data_dir_edit.text())
        self.settings.setValue("auto_save_results", self.auto_save_check.isChecked())
        self.settings.setValue("save_interval", self.save_interval_spin.value())
        self.settings.setValue("max_threads", self.max_threads_spin.value())
        self.settings.setValue("memory_limit", self.memory_limit_spin.value())
        
        self.settings.setValue("plot_style", self.plot_style_combo.currentText())
        self.settings.setValue("plot_dpi", self.dpi_spin.value())
        self.settings.setValue("show_grid", self.show_grid_check.isChecked())
        self.settings.setValue("primary_color", self.primary_color.name())
        self.settings.setValue("secondary_color", self.secondary_color.name())
        self.settings.setValue("font_family", self.current_font.family())
        self.settings.setValue("font_size", self.current_font.pointSize())
        
        self.settings.setValue("calculation_engine", self.engine_combo.currentText())
        self.settings.setValue("lammps_path", self.lammps_path_edit.text())
        self.settings.setValue("quantum_backend", self.quantum_backend_combo.currentText())
        self.settings.setValue("quantum_api_key", self.api_key_edit.text())
        
        # 创建设置字典并发出信号
        settings_dict = {
            "data_directory": self.data_dir_edit.text(),
            "auto_save_results": self.auto_save_check.isChecked(),
            "save_interval": self.save_interval_spin.value(),
            "max_threads": self.max_threads_spin.value(),
            "memory_limit": self.memory_limit_spin.value(),
            "plot_style": self.plot_style_combo.currentText(),
            "plot_dpi": self.dpi_spin.value(),
            "show_grid": self.show_grid_check.isChecked(),
            "primary_color": self.primary_color.name(),
            "secondary_color": self.secondary_color.name(),
            "font_family": self.current_font.family(),
            "font_size": self.current_font.pointSize(),
            "calculation_engine": self.engine_combo.currentText(),
            "lammps_path": self.lammps_path_edit.text(),
            "quantum_backend": self.quantum_backend_combo.currentText()
        }
        
        # 发出设置更改信号
        self.settings_changed.emit(settings_dict)
    
    def reset_settings(self):
        """重置设置为默认值"""
        # 常规设置
        self.data_dir_edit.setText("")
        self.auto_save_check.setChecked(True)
        self.save_interval_spin.setValue(10)
        self.max_threads_spin.setValue(4)
        self.memory_limit_spin.setValue(8)
        
        # 可视化设置
        self.plot_style_combo.setCurrentText("默认")
        self.dpi_spin.setValue(100)
        self.show_grid_check.setChecked(True)
        
        self.primary_color = QColor("#1f77b4")
        self.update_color_button(self.primary_color_button, self.primary_color)
        
        self.secondary_color = QColor("#ff7f0e")
        self.update_color_button(self.secondary_color_button, self.secondary_color)
        
        self.current_font = QFont("Arial", 10)
        self.font_label.setText("Arial, 10pt")
        
        # 高级设置
        self.engine_combo.setCurrentText("内置ReaxFF")
        self.lammps_path_edit.setText("")
        self.quantum_backend_combo.setCurrentText("模拟器")
        self.api_key_edit.setText("") 