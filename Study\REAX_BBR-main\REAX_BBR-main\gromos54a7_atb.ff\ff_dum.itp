; These constraints are used for vsite constructions as generated by pdb2gmx.
; Values depend on the details of the forcefield, vis. bondlengths and angles
; These parameters are designed to be used with the GROMOS96 forcefields
; G43a1, G43a2 and G43b1.

; Constraints for the rigid NH3/CH3 groups depend on the hygrogen mass,
; since an increased hydrogen mass translates into increased momentum of
; inertia which translates into a larger distance between the dummy masses.
#ifdef HEAVY_H
; now the constraints for the rigid NH3 groups
#define DC_MNC1 0.175695
#define DC_MNC2 0.188288
#define DC_MNMN 0.158884
; now the constraints for the rigid CH3 groups
#define DC_MCN  0.198911
#define DC_MCS  0.226838
#define DC_MCC  0.204247
#define DC_MCNR 0.199798
#define DC_MCMC 0.184320
#else
; now the constraints for the rigid NH3 groups
#define DC_MNC1 0.144494
#define DC_MNC2 0.158002
#define DC_MNMN 0.079442
; now the constraints for the rigid CH3 groups
#define DC_MCN  0.161051
#define DC_MCS  0.190961
#define DC_MCC  0.166809
#define DC_MCNR 0.162009
#define DC_MCMC 0.092160
#endif
; and the angle-constraints for OH and SH groups in proteins:
#define DC_CS  0.23721
#define DC_CO  0.19849
#define DC_PO  0.21603
