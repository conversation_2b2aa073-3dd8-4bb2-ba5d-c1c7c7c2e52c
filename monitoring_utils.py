#!/usr/bin/env python3
"""
监控工具 - 简单的优化过程监控
"""

import time
from datetime import datetime
from typing import Dict, Optional

class OptimizationMonitor:
    """简单的优化监控器"""
    
    def __init__(self):
        self.is_running = False
        self.start_time = None
        self.current_iteration = 0
        self.current_loss = float('inf')
        self.best_loss = float('inf')
        self.loss_history = []
        
    def start(self, dataset_name: str = "Unknown", param_count: int = 0):
        """开始监控"""
        self.is_running = True
        self.start_time = datetime.now()
        self.current_iteration = 0
        self.loss_history = []
        print(f"🚀 开始优化监控: {dataset_name}, 参数数量: {param_count}")
        
    def update(self, iteration: int, loss: float):
        """更新进度"""
        if not self.is_running:
            return
            
        self.current_iteration = iteration
        self.current_loss = loss
        
        if loss < self.best_loss:
            self.best_loss = loss
            
        self.loss_history.append(loss)
        
        # 每10次迭代打印一次进度
        if iteration % 10 == 0:
            runtime = self._get_runtime()
            print(f"📊 迭代 {iteration}: 损失 = {loss:.6f}, 最佳 = {self.best_loss:.6f}, 运行时间 = {runtime}")
    
    def stop(self):
        """停止监控"""
        if self.is_running:
            runtime = self._get_runtime()
            print(f"⏹️ 优化完成: 最佳损失 = {self.best_loss:.6f}, 总运行时间 = {runtime}")
        self.is_running = False
        
    def _get_runtime(self) -> str:
        """获取运行时间"""
        if not self.start_time:
            return "00:00:00"
        runtime = datetime.now() - self.start_time
        hours, remainder = divmod(int(runtime.total_seconds()), 3600)
        minutes, seconds = divmod(remainder, 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

# 全局监控器实例
global_monitor = OptimizationMonitor()

def start_monitoring(dataset_name: str = "Unknown", param_count: int = 0):
    """启动监控"""
    global_monitor.start(dataset_name, param_count)

def update_progress(iteration: int, loss: float):
    """更新进度"""
    global_monitor.update(iteration, loss)

def stop_monitoring():
    """停止监控"""
    global_monitor.stop()

def monitor_optimization(func):
    """装饰器：自动监控优化函数"""
    def wrapper(*args, **kwargs):
        start_monitoring("装饰器监控", 0)
        try:
            result = func(*args, **kwargs)
            stop_monitoring()
            return result
        except Exception as e:
            stop_monitoring()
            raise e
    return wrapper

# 使用示例
if __name__ == '__main__':
    print("🧪 监控工具测试")
    
    # 模拟优化过程
    start_monitoring("cobalt数据集", 12)
    
    import numpy as np
    for i in range(50):
        # 模拟损失下降
        loss = 10.0 * np.exp(-i/20) + np.random.normal(0, 0.1)
        update_progress(i, loss)
        time.sleep(0.1)
    
    stop_monitoring()
    print("✅ 测试完成")
