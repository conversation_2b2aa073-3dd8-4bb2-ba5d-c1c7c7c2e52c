"""
自动化全流程计算范式
端到端工具链、异构计算资源管理和智能后处理
"""

import os
import json
import time
import subprocess
from typing import Dict, List, Optional, Union, Callable
from dataclasses import dataclass
from datetime import datetime
import numpy as np
import yaml
import docker
import kubernetes
from celery import Celery
import redis
import torch
import cv2
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import logging
from pathlib import Path

logger = logging.getLogger(__name__)


@dataclass
class ComputeResource:
    """计算资源定义"""
    name: str
    type: str  # 'cpu', 'gpu', 'quantum'
    cores: int
    memory_gb: float
    status: str = 'available'
    metadata: Dict = None


class WorkflowEngine:
    """工作流引擎 - 类似Airflow的调度系统"""
    
    def __init__(self, redis_url: str = 'redis://localhost:6379'):
        self.celery_app = Celery('reaxff_workflow', broker=redis_url)
        self.tasks = {}
        self.workflows = {}
        
        # 注册任务
        self._register_default_tasks()
        
    def _register_default_tasks(self):
        """注册默认任务"""
        
        @self.celery_app.task(name='generate_params')
        def generate_params_task(config):
            """生成初始参数"""
            from ml.generative_param_init import GenerativeParameterInitializer
            
            initializer = GenerativeParameterInitializer(config['param_bounds'])
            params = initializer.generate_initial_guess(
                method=config.get('method', 'hybrid'),
                n_samples=config.get('n_samples', 10)
            )
            return params
            
        @self.celery_app.task(name='run_lammps')
        def run_lammps_task(params, input_file):
            """运行LAMMPS模拟"""
            # 写入参数文件
            param_file = f"params_{datetime.now().timestamp()}.txt"
            with open(param_file, 'w') as f:
                json.dump(params, f)
                
            # 运行LAMMPS
            cmd = f"lammps -in {input_file} -var paramfile {param_file}"
            result = subprocess.run(cmd, shell=True, capture_output=True)
            
            # 解析输出
            output = result.stdout.decode()
            energy = self._parse_lammps_energy(output)
            
            # 清理临时文件
            os.remove(param_file)
            
            return {'energy': energy, 'output': output}
            
        @self.celery_app.task(name='evaluate_error')
        def evaluate_error_task(simulation_result, target_data):
            """评估误差"""
            sim_energy = simulation_result['energy']
            target_energy = target_data['energy']
            
            error = abs(sim_energy - target_energy) / abs(target_energy)
            return {'error': error, 'details': simulation_result}
            
        @self.celery_app.task(name='optimize_params')
        def optimize_params_task(current_params, error_info):
            """优化参数"""
            from ml.reinforcement_learning_optimizer import ReinforcementLearningOptimizer
            
            # 基于误差更新参数
            optimizer = ReinforcementLearningOptimizer(current_params)
            new_params = optimizer.step(error_info)
            
            return new_params
            
        self.tasks = {
            'generate_params': generate_params_task,
            'run_lammps': run_lammps_task,
            'evaluate_error': evaluate_error_task,
            'optimize_params': optimize_params_task
        }
        
    def create_workflow(self, name: str, steps: List[Dict]) -> str:
        """创建工作流
        
        Args:
            name: 工作流名称
            steps: 步骤列表，每个步骤包含任务名和参数
            
        Returns:
            工作流ID
        """
        workflow_id = f"{name}_{datetime.now().timestamp()}"
        
        self.workflows[workflow_id] = {
            'name': name,
            'steps': steps,
            'status': 'created',
            'created_at': datetime.now()
        }
        
        return workflow_id
        
    def execute_workflow(self, workflow_id: str) -> Dict:
        """执行工作流"""
        workflow = self.workflows[workflow_id]
        workflow['status'] = 'running'
        results = {}
        
        try:
            for i, step in enumerate(workflow['steps']):
                task_name = step['task']
                params = step.get('params', {})
                
                # 参数中可以引用之前步骤的结果
                for key, value in params.items():
                    if isinstance(value, str) and value.startswith('$'):
                        # 引用之前的结果
                        ref = value[1:]  # 去掉$
                        if ref in results:
                            params[key] = results[ref]
                            
                # 执行任务
                task = self.tasks[task_name]
                result = task.delay(**params).get()
                
                # 保存结果
                step_name = step.get('name', f'step_{i}')
                results[step_name] = result
                
            workflow['status'] = 'completed'
            workflow['results'] = results
            
        except Exception as e:
            workflow['status'] = 'failed'
            workflow['error'] = str(e)
            logger.error(f"工作流执行失败: {e}")
            
        return workflow


class KubernetesResourceManager:
    """Kubernetes资源管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        if config_file:
            kubernetes.config.load_kube_config(config_file)
        else:
            kubernetes.config.load_incluster_config()
            
        self.v1 = kubernetes.client.CoreV1Api()
        self.batch_v1 = kubernetes.client.BatchV1Api()
        
    def create_job(self, job_name: str, image: str, 
                  command: List[str], resources: Dict) -> str:
        """创建Kubernetes作业
        
        Args:
            job_name: 作业名称
            image: 容器镜像
            command: 执行命令
            resources: 资源需求
            
        Returns:
            作业ID
        """
        # 构建作业配置
        container = kubernetes.client.V1Container(
            name=job_name,
            image=image,
            command=command,
            resources=kubernetes.client.V1ResourceRequirements(
                requests=resources.get('requests', {}),
                limits=resources.get('limits', {})
            )
        )
        
        # GPU支持
        if 'gpu' in resources:
            container.resources.limits['nvidia.com/gpu'] = resources['gpu']
            
        # Pod模板
        pod_template = kubernetes.client.V1PodTemplateSpec(
            metadata=kubernetes.client.V1ObjectMeta(labels={"app": job_name}),
            spec=kubernetes.client.V1PodSpec(
                restart_policy="Never",
                containers=[container]
            )
        )
        
        # 作业规格
        job_spec = kubernetes.client.V1JobSpec(
            template=pod_template,
            backoff_limit=3
        )
        
        # 创建作业
        job = kubernetes.client.V1Job(
            api_version="batch/v1",
            kind="Job",
            metadata=kubernetes.client.V1ObjectMeta(name=job_name),
            spec=job_spec
        )
        
        response = self.batch_v1.create_namespaced_job(
            namespace="default",
            body=job
        )
        
        return response.metadata.name
        
    def get_job_status(self, job_name: str) -> Dict:
        """获取作业状态"""
        job = self.batch_v1.read_namespaced_job(
            name=job_name,
            namespace="default"
        )
        
        return {
            'active': job.status.active,
            'succeeded': job.status.succeeded,
            'failed': job.status.failed,
            'conditions': job.status.conditions
        }
        
    def allocate_resources(self, task_type: str, requirements: Dict) -> Dict:
        """分配计算资源
        
        Args:
            task_type: 任务类型
            requirements: 资源需求
            
        Returns:
            分配的资源信息
        """
        # 获取可用节点
        nodes = self.v1.list_node()
        available_nodes = []
        
        for node in nodes.items:
            # 检查节点资源
            allocatable = node.status.allocatable
            
            cpu_available = int(allocatable.get('cpu', '0'))
            memory_available = self._parse_memory(allocatable.get('memory', '0'))
            gpu_available = int(allocatable.get('nvidia.com/gpu', '0'))
            
            # 检查是否满足需求
            if (cpu_available >= requirements.get('cpu', 0) and
                memory_available >= requirements.get('memory_gb', 0) and
                gpu_available >= requirements.get('gpu', 0)):
                
                available_nodes.append({
                    'name': node.metadata.name,
                    'cpu': cpu_available,
                    'memory_gb': memory_available,
                    'gpu': gpu_available,
                    'labels': node.metadata.labels
                })
                
        if not available_nodes:
            raise RuntimeError("没有可用的节点满足资源需求")
            
        # 选择最合适的节点
        if task_type == 'quantum':
            # 优先选择有量子计算标签的节点
            quantum_nodes = [n for n in available_nodes 
                           if n['labels'].get('quantum', 'false') == 'true']
            if quantum_nodes:
                return quantum_nodes[0]
                
        elif task_type == 'gpu':
            # 选择GPU最多的节点
            available_nodes.sort(key=lambda x: x['gpu'], reverse=True)
            
        return available_nodes[0]
        
    def _parse_memory(self, memory_str: str) -> float:
        """解析内存字符串（如'8Gi'）为GB"""
        if memory_str.endswith('Gi'):
            return float(memory_str[:-2])
        elif memory_str.endswith('Mi'):
            return float(memory_str[:-2]) / 1024
        else:
            return float(memory_str) / (1024 ** 3)


class TensorCoreOptimizer:
    """张量核心优化器 - 优化ReaxFF计算"""
    
    def __init__(self, device: str = 'cuda'):
        self.device = torch.device(device if torch.cuda.is_available() else 'cpu')
        
        # 检查张量核心支持
        if torch.cuda.is_available():
            capability = torch.cuda.get_device_capability()
            self.tensor_cores_available = capability[0] >= 7  # Volta架构及以上
        else:
            self.tensor_cores_available = False
            
        logger.info(f"张量核心可用: {self.tensor_cores_available}")
        
    def optimize_bond_order_calculation(self, positions: np.ndarray, 
                                      params: Dict) -> torch.Tensor:
        """优化键级计算
        
        将键级计算重构为矩阵运算以利用张量核心
        E_bond = Tr(P · H_eff)
        """
        n_atoms = len(positions)
        positions_tensor = torch.FloatTensor(positions).to(self.device)
        
        # 计算距离矩阵
        dist_matrix = self._compute_distance_matrix_optimized(positions_tensor)
        
        # 构建有效哈密顿量矩阵（混合精度）
        with torch.cuda.amp.autocast(enabled=self.tensor_cores_available):
            # 键级矩阵 P
            P = self._compute_bond_order_matrix(dist_matrix, params)
            
            # 有效哈密顿量 H_eff
            H_eff = self._compute_effective_hamiltonian(dist_matrix, params)
            
            # 矩阵乘法（利用张量核心）
            E_bond = torch.trace(torch.matmul(P, H_eff))
            
        return E_bond
        
    def _compute_distance_matrix_optimized(self, positions: torch.Tensor) -> torch.Tensor:
        """优化的距离矩阵计算"""
        # 使用广播计算所有原子对的距离
        diff = positions.unsqueeze(0) - positions.unsqueeze(1)
        distances = torch.norm(diff, dim=2)
        
        return distances
        
    def _compute_bond_order_matrix(self, distances: torch.Tensor, 
                                  params: Dict) -> torch.Tensor:
        """计算键级矩阵"""
        # ReaxFF键级公式的简化版本
        r_sigma = params.get('r_sigma', 1.5)
        r_pi = params.get('r_pi', 2.5)
        
        # 使用半精度加速（张量核心）
        if self.tensor_cores_available:
            distances = distances.half()
            
        # 键级衰减
        BO_sigma = torch.exp(-distances / r_sigma)
        BO_pi = torch.exp(-distances / r_pi)
        
        # 总键级
        BO = BO_sigma + BO_pi
        
        # 归一化
        BO = BO / (1 + BO)
        
        if self.tensor_cores_available:
            BO = BO.float()  # 转回单精度
            
        return BO
        
    def _compute_effective_hamiltonian(self, distances: torch.Tensor, 
                                     params: Dict) -> torch.Tensor:
        """计算有效哈密顿量"""
        n_atoms = distances.shape[0]
        H_eff = torch.zeros_like(distances)
        
        # 对角元素（原子能量）
        E_atom = params.get('E_atom', -100.0)
        H_eff.diagonal().fill_(E_atom)
        
        # 非对角元素（相互作用）
        V_sigma = params.get('V_sigma', -50.0)
        V_pi = params.get('V_pi', -20.0)
        
        # 相互作用势
        V = V_sigma * torch.exp(-distances) + V_pi * torch.exp(-2 * distances)
        H_eff = H_eff + V
        
        return H_eff
        
    def parallel_force_calculation(self, positions: np.ndarray, 
                                  params: Dict, n_threads: int = 32) -> np.ndarray:
        """并行化力计算（CUDA Warp级别）"""
        if not torch.cuda.is_available():
            return self._cpu_force_calculation(positions, params)
            
        n_atoms = len(positions)
        positions_tensor = torch.FloatTensor(positions).to(self.device)
        forces = torch.zeros_like(positions_tensor)
        
        # 自定义CUDA核函数（这里用PyTorch模拟）
        # 实际实现中应该使用CUDA C++
        
        # 分块计算以适应Warp大小（32线程）
        block_size = 32
        n_blocks = (n_atoms + block_size - 1) // block_size
        
        for block_idx in range(n_blocks):
            start_idx = block_idx * block_size
            end_idx = min(start_idx + block_size, n_atoms)
            
            # 每个线程计算一个原子的力
            for i in range(start_idx, end_idx):
                force_i = torch.zeros(3).to(self.device)
                
                for j in range(n_atoms):
                    if i != j:
                        # 计算原子对相互作用
                        r_ij = positions_tensor[j] - positions_tensor[i]
                        r = torch.norm(r_ij)
                        
                        # 力的大小
                        f_mag = self._compute_force_magnitude(r, params)
                        
                        # 力的方向
                        force_i += f_mag * r_ij / r
                        
                forces[i] = force_i
                
        return forces.cpu().numpy()
        
    def _compute_force_magnitude(self, r: torch.Tensor, params: Dict) -> torch.Tensor:
        """计算力的大小"""
        # 简化的力场导数
        k = params.get('force_constant', 100.0)
        r0 = params.get('equilibrium_distance', 1.5)
        
        return k * (r - r0)
        
    def _cpu_force_calculation(self, positions: np.ndarray, params: Dict) -> np.ndarray:
        """CPU版本的力计算（备用）"""
        n_atoms = len(positions)
        forces = np.zeros_like(positions)
        
        for i in range(n_atoms):
            for j in range(n_atoms):
                if i != j:
                    r_ij = positions[j] - positions[i]
                    r = np.linalg.norm(r_ij)
                    
                    # 简化的力计算
                    k = params.get('force_constant', 100.0)
                    r0 = params.get('equilibrium_distance', 1.5)
                    f_mag = k * (r - r0)
                    
                    forces[i] += f_mag * r_ij / r
                    
        return forces


class IntelligentPostProcessor:
    """智能后处理系统"""
    
    def __init__(self):
        self.trajectory_analyzer = TrajectoryAnalyzer()
        self.visualization_engine = VisualizationEngine()
        
    def analyze_trajectory(self, trajectory: np.ndarray, 
                         method: str = 'cnn') -> Dict:
        """分析MD轨迹
        
        Args:
            trajectory: MD轨迹数据
            method: 分析方法 ('cnn', 'transformer', 'hybrid')
            
        Returns:
            分析结果
        """
        if method == 'cnn':
            return self.trajectory_analyzer.cnn_analysis(trajectory)
        elif method == 'transformer':
            return self.trajectory_analyzer.transformer_analysis(trajectory)
        elif method == 'hybrid':
            cnn_results = self.trajectory_analyzer.cnn_analysis(trajectory)
            transformer_results = self.trajectory_analyzer.transformer_analysis(trajectory)
            return self._merge_results(cnn_results, transformer_results)
        else:
            raise ValueError(f"未知的分析方法: {method}")
            
    def generate_ar_visualization(self, trajectory: np.ndarray, 
                                output_format: str = 'hololens') -> bytes:
        """生成AR可视化
        
        Args:
            trajectory: MD轨迹
            output_format: 输出格式 ('hololens', 'arcore', 'arkit')
            
        Returns:
            AR数据包
        """
        # 提取关键帧
        key_frames = self.trajectory_analyzer.extract_key_frames(trajectory)
        
        # 生成3D模型
        models_3d = []
        for frame in key_frames:
            model = self.visualization_engine.create_3d_model(frame)
            models_3d.append(model)
            
        # 打包为AR格式
        ar_package = self.visualization_engine.package_for_ar(
            models_3d, 
            format=output_format
        )
        
        return ar_package
        
    def _merge_results(self, *results) -> Dict:
        """合并多个分析结果"""
        merged = {}
        
        for result in results:
            for key, value in result.items():
                if key not in merged:
                    merged[key] = value
                elif isinstance(value, list):
                    merged[key].extend(value)
                elif isinstance(value, dict):
                    merged[key].update(value)
                    
        return merged


class TrajectoryAnalyzer:
    """轨迹分析器"""
    
    def __init__(self):
        # 初始化CNN模型
        self.cnn_model = self._build_cnn_model()
        
    def _build_cnn_model(self) -> nn.Module:
        """构建CNN模型用于轨迹分析"""
        class TrajectoryCNN(nn.Module):
            def __init__(self):
                super().__init__()
                self.conv1 = nn.Conv3d(1, 32, kernel_size=3, padding=1)
                self.conv2 = nn.Conv3d(32, 64, kernel_size=3, padding=1)
                self.conv3 = nn.Conv3d(64, 128, kernel_size=3, padding=1)
                self.pool = nn.MaxPool3d(2)
                self.fc1 = nn.Linear(128 * 8 * 8 * 8, 256)
                self.fc2 = nn.Linear(256, 5)  # 5种事件类型
                
            def forward(self, x):
                x = self.pool(F.relu(self.conv1(x)))
                x = self.pool(F.relu(self.conv2(x)))
                x = self.pool(F.relu(self.conv3(x)))
                x = x.view(x.size(0), -1)
                x = F.relu(self.fc1(x))
                x = self.fc2(x)
                return x
                
        return TrajectoryCNN()
        
    def cnn_analysis(self, trajectory: np.ndarray) -> Dict:
        """使用CNN分析轨迹"""
        # 预处理轨迹数据
        trajectory_tensor = self._preprocess_trajectory(trajectory)
        
        # CNN推理
        self.cnn_model.eval()
        with torch.no_grad():
            predictions = self.cnn_model(trajectory_tensor)
            
        # 解析结果
        events = self._parse_cnn_predictions(predictions)
        
        return {
            'detected_events': events,
            'event_statistics': self._compute_event_statistics(events)
        }
        
    def transformer_analysis(self, trajectory: np.ndarray) -> Dict:
        """使用Transformer分析轨迹"""
        from ml.multimodal_llm import ReactionPathTransformer
        
        transformer = ReactionPathTransformer()
        trajectory_tensor = torch.FloatTensor(trajectory).unsqueeze(0)
        
        events = transformer.identify_reaction_events(trajectory_tensor)
        
        return {
            'reaction_events': events,
            'mechanism_insights': self._extract_mechanism_insights(events)
        }
        
    def extract_key_frames(self, trajectory: np.ndarray, 
                          n_frames: int = 10) -> List[np.ndarray]:
        """提取关键帧"""
        # 使用能量变化识别关键帧
        energies = self._compute_frame_energies(trajectory)
        energy_changes = np.abs(np.diff(energies))
        
        # 找到能量变化最大的帧
        key_indices = np.argsort(energy_changes)[-n_frames:]
        key_indices = np.sort(key_indices)
        
        return [trajectory[i] for i in key_indices]
        
    def _preprocess_trajectory(self, trajectory: np.ndarray) -> torch.Tensor:
        """预处理轨迹数据为CNN输入"""
        # 假设轨迹形状为 [n_frames, n_atoms, 3]
        # 转换为 [1, 1, n_frames, n_atoms, 3] 的5D张量
        trajectory = trajectory.reshape(1, 1, *trajectory.shape)
        
        # 插值或截断到固定大小
        target_shape = (1, 1, 64, 64, 3)  # 固定大小
        
        # 这里简化处理，实际应该使用更好的插值方法
        if trajectory.shape[2] > 64:
            trajectory = trajectory[:, :, :64]
        if trajectory.shape[3] > 64:
            trajectory = trajectory[:, :, :, :64]
            
        # 填充到目标大小
        padded = np.zeros(target_shape)
        padded[:, :, :trajectory.shape[2], :trajectory.shape[3]] = trajectory
        
        return torch.FloatTensor(padded)
        
    def _parse_cnn_predictions(self, predictions: torch.Tensor) -> List[Dict]:
        """解析CNN预测结果"""
        event_types = ['bond_break', 'bond_form', 'rotation', 'vibration', 'collision']
        events = []
        
        # 获取预测的事件类型
        _, predicted_classes = torch.max(predictions, 1)
        
        for i, pred_class in enumerate(predicted_classes):
            events.append({
                'frame': i,
                'event_type': event_types[pred_class.item()],
                'confidence': torch.softmax(predictions[i], dim=0)[pred_class].item()
            })
            
        return events
        
    def _compute_event_statistics(self, events: List[Dict]) -> Dict:
        """计算事件统计信息"""
        stats = {}
        
        for event in events:
            event_type = event['event_type']
            if event_type not in stats:
                stats[event_type] = 0
            stats[event_type] += 1
            
        return stats
        
    def _extract_mechanism_insights(self, events: List[Dict]) -> List[str]:
        """提取机理洞察"""
        insights = []
        
        # 分析事件序列
        if len(events) > 1:
            # 检查是否有连续的键断裂和形成
            for i in range(len(events) - 1):
                if (events[i]['type'] == 'bond_breaking' and 
                    events[i+1]['type'] == 'bond_formation'):
                    insights.append("检测到协同反应机理")
                    
        # 检查中间体
        intermediates = [e for e in events if e.get('is_intermediate', False)]
        if intermediates:
            insights.append(f"发现{len(intermediates)}个反应中间体")
            
        return insights
        
    def _compute_frame_energies(self, trajectory: np.ndarray) -> np.ndarray:
        """计算每帧的能量（简化版）"""
        n_frames = trajectory.shape[0]
        energies = np.zeros(n_frames)
        
        for i in range(n_frames):
            # 简化的能量计算
            distances = self._compute_pairwise_distances(trajectory[i])
            energies[i] = np.sum(np.exp(-distances))  # 简化的势能
            
        return energies
        
    def _compute_pairwise_distances(self, positions: np.ndarray) -> np.ndarray:
        """计算原子对距离"""
        n_atoms = len(positions)
        distances = np.zeros((n_atoms, n_atoms))
        
        for i in range(n_atoms):
            for j in range(i + 1, n_atoms):
                dist = np.linalg.norm(positions[i] - positions[j])
                distances[i, j] = distances[j, i] = dist
                
        return distances


class VisualizationEngine:
    """可视化引擎"""
    
    def create_3d_model(self, molecular_config: Dict) -> Dict:
        """创建3D分子模型"""
        model = {
            'vertices': [],
            'faces': [],
            'colors': [],
            'metadata': molecular_config
        }
        
        # 为每个原子创建球体
        for i, (atom, pos) in enumerate(zip(molecular_config['atoms'], 
                                           molecular_config['coordinates'])):
            sphere = self._create_sphere(pos, self._get_atom_radius(atom))
            model['vertices'].extend(sphere['vertices'])
            model['faces'].extend(sphere['faces'])
            model['colors'].extend([self._get_atom_color(atom)] * len(sphere['vertices']))
            
        return model
        
    def package_for_ar(self, models: List[Dict], format: str = 'hololens') -> bytes:
        """打包模型为AR格式"""
        if format == 'hololens':
            return self._package_for_hololens(models)
        elif format == 'arcore':
            return self._package_for_arcore(models)
        elif format == 'arkit':
            return self._package_for_arkit(models)
        else:
            raise ValueError(f"不支持的AR格式: {format}")
            
    def _create_sphere(self, center: np.ndarray, radius: float, 
                      resolution: int = 20) -> Dict:
        """创建球体网格"""
        vertices = []
        faces = []
        
        # 生成球面顶点
        for i in range(resolution):
            theta = i * np.pi / (resolution - 1)
            for j in range(resolution):
                phi = j * 2 * np.pi / (resolution - 1)
                
                x = center[0] + radius * np.sin(theta) * np.cos(phi)
                y = center[1] + radius * np.sin(theta) * np.sin(phi)
                z = center[2] + radius * np.cos(theta)
                
                vertices.append([x, y, z])
                
        # 生成面
        for i in range(resolution - 1):
            for j in range(resolution - 1):
                v1 = i * resolution + j
                v2 = v1 + 1
                v3 = (i + 1) * resolution + j
                v4 = v3 + 1
                
                faces.append([v1, v2, v3])
                faces.append([v2, v4, v3])
                
        return {'vertices': vertices, 'faces': faces}
        
    def _get_atom_radius(self, atom: str) -> float:
        """获取原子半径"""
        radii = {
            'H': 0.3, 'C': 0.7, 'N': 0.65,
            'O': 0.6, 'F': 0.5, 'S': 1.0
        }
        return radii.get(atom, 0.7)
        
    def _get_atom_color(self, atom: str) -> str:
        """获取原子颜色"""
        colors = {
            'H': '#FFFFFF', 'C': '#808080', 'N': '#0000FF',
            'O': '#FF0000', 'F': '#00FF00', 'S': '#FFFF00'
        }
        return colors.get(atom, '#808080')
        
    def _package_for_hololens(self, models: List[Dict]) -> bytes:
        """打包为HoloLens格式（简化）"""
        # 实际实现需要使用Unity或其他3D引擎
        package = {
            'format': 'hololens',
            'version': '2.0',
            'models': models,
            'interactions': {
                'gesture_controls': True,
                'voice_commands': ['rotate', 'zoom', 'reset', 'play', 'pause'],
                'spatial_anchors': True
            }
        }
        
        return json.dumps(package).encode('utf-8')
        
    def _package_for_arcore(self, models: List[Dict]) -> bytes:
        """打包为ARCore格式"""
        package = {
            'format': 'arcore',
            'version': '1.0',
            'models': models
        }
        return json.dumps(package).encode('utf-8')
        
    def _package_for_arkit(self, models: List[Dict]) -> bytes:
        """打包为ARKit格式"""
        package = {
            'format': 'arkit',
            'version': '1.0',
            'models': models
        }
        return json.dumps(package).encode('utf-8') 