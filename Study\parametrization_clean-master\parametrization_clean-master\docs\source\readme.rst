===============================
GA + ANN ReaxFF Parametrization
===============================


.. image:: https://img.shields.io/pypi/v/parametrization_clean.svg
        :target: https://pypi.python.org/pypi/parametrization_clean

.. image:: https://img.shields.io/travis/cdaksha/parametrization_clean.svg
        :target: https://travis-ci.com/cdaksha/parametrization_clean

.. image:: https://readthedocs.org/projects/parametrization-clean/badge/?version=latest
        :target: https://parametrization-clean.readthedocs.io/en/latest/?badge=latest
        :alt: Documentation Status




Refactored ReaxFF parametrization project that uses genetic algorithm and artificial neural network, also moreso complying with clean architecture.[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[D[A[B[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[A[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[C[, that uses genetic algorithm and artificial neural network, so that it is moreso compliant with clean architecture.


* Free software: MIT license
* Documentation: https://parametrization-clean.readthedocs.io.


Features
--------

* TODO

Credits
-------

This package was created with Cookiecutter_ and the `audreyr/cookiecutter-pypackage`_ project template.

.. _Cookiecutter: https://github.com/audreyr/cookiecutter
.. _`audreyr/cookiecutter-pypackage`: https://github.com/audreyr/cookiecutter-pypackage
