import jax
import jax.numpy as jnp
from typing import Dict, Any, Optional, Tuple
import flax.linen as nn

class BaseModel(nn.Module):
    """所有机器学习模型的基础类"""
    
    def __init__(self):
        super().__init__()
        self.params = None
    
    def initialize(self, rng: jax.random.PRNGKey, input_shape: Tuple[int, ...]) -> Dict[str, Any]:
        """
        初始化模型参数
        
        Args:
            rng: 随机数生成器
            input_shape: 输入张量的形状
            
        Returns:
            params: 模型参数字典
        """
        raise NotImplementedError
    
    def __call__(self, inputs: jnp.ndarray, training: bool = False) -> jnp.ndarray:
        """
        模型前向传播
        
        Args:
            inputs: 输入数据
            training: 是否处于训练模式
            
        Returns:
            outputs: 模型输出
        """
        raise NotImplementedError
    
    def predict(self, inputs: jnp.ndarray) -> jnp.ndarray:
        """
        模型预测
        
        Args:
            inputs: 输入数据
            
        Returns:
            predictions: 模型预测结果
        """
        return self(inputs, training=False)
    
    def get_metrics(self) -> Dict[str, float]:
        """
        获取模型指标
        
        Returns:
            metrics: 模型指标字典
        """
        return {}
    
    def save(self, path: str):
        """
        保存模型参数
        
        Args:
            path: 保存路径
        """
        raise NotImplementedError
    
    def load(self, path: str):
        """
        加载模型参数
        
        Args:
            path: 加载路径
        """
        raise NotImplementedError 