#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自然梯度下降优化器

该模块提供了自然梯度下降(NGD)的实现，该方法利用参数空间的黎曼几何结构
加速模型训练，特别适合优化ReaxFF和GNN模型的参数。
"""

import jax
import jax.numpy as jnp
import optax
from typing import Any, Callable, Dict, List, NamedTuple, Optional, Tuple, Union
import numpy as np

class NGDState(NamedTuple):
    """自然梯度下降优化器状态"""
    count: jnp.ndarray  # 步数计数器
    fisher_estimate: jnp.ndarray  # Fisher信息矩阵的估计(或其逆)
    opt_state: optax.OptState  # 基础优化器状态

class FisherEstimateState(NamedTuple):
    """Fisher信息矩阵估计状态"""
    estimate: jnp.ndarray  # 当前估计
    momentum: jnp.ndarray  # 用于滑动平均

def fisher_diagonal(grad: jnp.ndarray, damping: float = 1e-5) -> jnp.ndarray:
    """
    计算Fisher信息矩阵的对角线近似
    
    Args:
        grad: 参数梯度
        damping: 阻尼因子，防止数值不稳定
        
    Returns:
        fisher_diag: Fisher矩阵的对角线估计
    """
    # 对角线估计是梯度的平方
    fisher_diag = jnp.square(grad)
    
    # 添加阻尼项以确保数值稳定性
    fisher_diag = fisher_diag + damping
    
    return fisher_diag

def fisher_empirical(model_fn: Callable, params: Any, inputs: jnp.ndarray, 
                    damping: float = 1e-5, n_samples: int = 10) -> jnp.ndarray:
    """
    计算Fisher信息矩阵的经验估计
    
    Args:
        model_fn: 模型函数，输入为参数和数据，输出为概率分布
        params: 模型参数
        inputs: 输入数据
        damping: 阻尼因子
        n_samples: 用于估计的样本数
        
    Returns:
        fisher_matrix: Fisher信息矩阵的估计
    """
    # 获取模型输出
    outputs = model_fn(params, inputs)
    
    # 采样来自模型的输出分布
    # 这里假设输出是Gaussian分布的参数(均值和方差)
    if isinstance(outputs, tuple) and len(outputs) == 2:
        means, log_vars = outputs
        distribution = lambda: means + jnp.exp(0.5 * log_vars) * jax.random.normal(
            jax.random.PRNGKey(int(jnp.sum(means) * 1000)), means.shape)
    else:
        # 如果不是概率分布，使用标准梯度替代
        return fisher_diagonal(jax.grad(lambda p: jnp.sum(model_fn(p, inputs)))(params))
    
    # 计算对数似然梯度的外积的平均值
    fisher_matrix = jnp.zeros_like(params)
    
    def body_fn(i, fisher_acc):
        # 从模型分布中采样
        sample = distribution()
        
        # 计算对数似然关于参数的梯度
        log_likelihood_grad = jax.grad(
            lambda p: jnp.sum(jax.scipy.stats.norm.logpdf(
                sample, model_fn(p, inputs)[0], jnp.exp(0.5 * model_fn(p, inputs)[1])
            ))
        )(params)
        
        # 计算梯度的外积并累加
        return fisher_acc + jnp.outer(log_likelihood_grad, log_likelihood_grad)
    
    fisher_matrix = jax.lax.fori_loop(0, n_samples, body_fn, fisher_matrix)
    fisher_matrix = fisher_matrix / n_samples
    
    # 添加阻尼项
    fisher_matrix = fisher_matrix + damping * jnp.eye(fisher_matrix.shape[0])
    
    return fisher_matrix

def _init_fisher_estimate(params_shape: Tuple, method: str = 'diagonal', 
                          init_value: float = 1.0) -> jnp.ndarray:
    """
    初始化Fisher信息矩阵估计
    
    Args:
        params_shape: 参数形状
        method: 估计方法，'diagonal'或'full'
        init_value: 初始值
        
    Returns:
        initial_estimate: 初始Fisher矩阵估计
    """
    if method == 'diagonal':
        return jnp.ones(params_shape) * init_value
    else:  # 'full'
        flat_size = np.prod(params_shape)
        return jnp.eye(flat_size) * init_value

def natural_gradient_optimizer(
    learning_rate: float = 0.01,
    fisher_method: str = 'diagonal',
    damping: float = 1e-5,
    momentum: float = 0.9,
    update_fisher_every: int = 1,
    base_optimizer: Optional[optax.GradientTransformation] = None,
) -> optax.GradientTransformation:
    """
    创建自然梯度下降优化器
    
    Args:
        learning_rate: 学习率
        fisher_method: Fisher矩阵估计方法，'diagonal'或'full'
        damping: 阻尼因子
        momentum: Fisher估计的动量系数
        update_fisher_every: 更新Fisher矩阵的频率(每n步)
        base_optimizer: 基础优化器，用于应用缩放后的梯度
        
    Returns:
        optimizer: 自然梯度优化器
    """
    if base_optimizer is None:
        base_optimizer = optax.sgd(learning_rate=learning_rate)
    
    def init_fn(params):
        # 初始化Fisher估计
        if fisher_method == 'diagonal':
            fisher_estimate = jax.tree_map(
                lambda p: jnp.ones_like(p),
                params
            )
        else:
            # 将参数展平用于全矩阵估计
            # 注：这仅适用于小型模型，大型模型应该使用对角估计
            flat_params, tree_def = jax.tree_util.tree_flatten(params)
            flat_shapes = [p.shape for p in flat_params]
            flat_sizes = [np.prod(s) for s in flat_shapes]
            total_size = sum(flat_sizes)
            
            fisher_estimate = jnp.eye(total_size)
        
        # 初始化Fisher估计状态
        fisher_state = FisherEstimateState(
            estimate=fisher_estimate,
            momentum=jnp.zeros_like(fisher_estimate)
        )
        
        # 初始化基础优化器
        base_state = base_optimizer.init(params)
        
        return NGDState(
            count=jnp.zeros([], jnp.int32),
            fisher_estimate=fisher_state,
            opt_state=base_state
        )
    
    def update_fn(updates, state, params=None):
        # 递增计数器
        count = state.count + 1
        
        # 更新Fisher估计
        def update_fisher(fisher_state, updates):
            if fisher_method == 'diagonal':
                # 计算对角线Fisher估计
                new_estimate = jax.tree_map(
                    lambda g: fisher_diagonal(g, damping=damping),
                    updates
                )
            else:
                # 计算全矩阵Fisher估计(对于小模型)
                # 展平梯度
                flat_grads, tree_def = jax.tree_util.tree_flatten(updates)
                flat_grads = [g.reshape(-1) for g in flat_grads]
                flat_grad = jnp.concatenate(flat_grads)
                
                # 计算Fisher矩阵
                new_estimate = jnp.outer(flat_grad, flat_grad) + damping * jnp.eye(len(flat_grad))
            
            # 应用动量更新
            if momentum > 0:
                if isinstance(new_estimate, jnp.ndarray):
                    new_estimate = momentum * fisher_state.estimate + (1 - momentum) * new_estimate
                else:
                    new_estimate = jax.tree_map(
                        lambda old, new: momentum * old + (1 - momentum) * new,
                        fisher_state.estimate, new_estimate
                    )
            
            return FisherEstimateState(
                estimate=new_estimate,
                momentum=fisher_state.momentum
            )
        
        # 仅在指定步数更新Fisher
        fisher_state = jax.lax.cond(
            count % update_fisher_every == 0,
            lambda s: update_fisher(s, updates),
            lambda s: s,
            state.fisher_estimate
        )
        
        # 应用自然梯度
        def apply_ngd(updates, fisher_estimate):
            if fisher_method == 'diagonal':
                # 对角线情况：直接除以Fisher对角线
                return jax.tree_map(
                    lambda g, f: g / (f + damping),
                    updates, fisher_estimate
                )
            else:
                # 全矩阵情况：乘以Fisher逆矩阵
                # 展平梯度
                flat_grads, tree_def = jax.tree_util.tree_flatten(updates)
                flat_grads = [g.reshape(-1) for g in flat_grads]
                flat_grad = jnp.concatenate(flat_grads)
                
                # 应用Fisher逆矩阵
                # 使用Cholesky分解求解线性系统，更稳定
                L = jnp.linalg.cholesky(fisher_estimate)
                natural_flat_grad = jax.scipy.linalg.cho_solve((L, True), flat_grad)
                
                # 重构梯度树
                idx = 0
                natural_grads = []
                for g in flat_grads:
                    size = g.size
                    natural_grads.append(natural_flat_grad[idx:idx+size].reshape(g.shape))
                    idx += size
                
                return jax.tree_util.tree_unflatten(tree_def, natural_grads)
        
        # 计算自然梯度
        natural_grads = apply_ngd(updates, fisher_state.estimate)
        
        # 应用基础优化器
        base_updates, base_state = base_optimizer.update(natural_grads, state.opt_state, params)
        
        return base_updates, NGDState(
            count=count,
            fisher_estimate=fisher_state,
            opt_state=base_state
        )
    
    return optax.GradientTransformation(init_fn, update_fn)


class KFAC:
    """
    使用K-FAC算法的自然梯度优化器
    
    Kronecker-Factored Approximate Curvature(K-FAC)是一种更高效的
    Fisher信息矩阵近似方法，特别适用于深度神经网络。
    """
    
    def __init__(
        self,
        model_fn: Callable,
        learning_rate: float = 0.01,
        damping: float = 1e-5,
        momentum: float = 0.9,
        update_freq: int = 1,
        norm_constraint: Optional[float] = None,
    ):
        """
        初始化K-FAC优化器
        
        Args:
            model_fn: 模型函数
            learning_rate: 学习率
            damping: 阻尼因子
            momentum: 动量系数
            update_freq: 更新Kronecker因子的频率
            norm_constraint: 自然梯度范数约束
        """
        self.model_fn = model_fn
        self.learning_rate = learning_rate
        self.damping = damping
        self.momentum = momentum
        self.update_freq = update_freq
        self.norm_constraint = norm_constraint
        
    def _register_layers(self, params):
        """注册网络层以便于K-FAC计算"""
        # 这里需要识别不同类型的层并为其创建适当的Kronecker因子存储
        # 为简洁起见，我们假设所有参数都是线性层
        registered_layers = []
        
        # 遍历参数树
        flat_params, tree_def = jax.tree_util.tree_flatten(params)
        for i, param in enumerate(flat_params):
            if param.ndim == 2:  # 假设是权重矩阵
                registered_layers.append({
                    'index': i,
                    'shape': param.shape,
                    'A': jnp.eye(param.shape[1]),  # 输入协方差的Kronecker因子
                    'G': jnp.eye(param.shape[0]),  # 梯度协方差的Kronecker因子
                })
        
        return registered_layers
    
    def init(self, params):
        """初始化优化器状态"""
        # 注册层
        layers = self._register_layers(params)
        
        # 初始化基础SGD优化器
        sgd_state = optax.sgd(self.learning_rate, self.momentum).init(params)
        
        return {
            'count': 0,
            'layers': layers,
            'sgd_state': sgd_state,
            'params': params,
        }
    
    def _compute_layer_stats(self, layer, acts, grads):
        """为单个层计算Kronecker因子"""
        # 计算输入协方差(A)
        A = jnp.matmul(acts.T, acts) / acts.shape[0]
        A = A + self.damping * jnp.eye(A.shape[0])
        
        # 计算梯度协方差(G)
        G = jnp.matmul(grads.T, grads) / grads.shape[0]
        G = G + self.damping * jnp.eye(G.shape[0])
        
        return A, G
    
    def _update_factor(self, state, batch):
        """更新Kronecker因子"""
        # 提取输入和标签
        inputs, targets = batch
        
        # 获取模型当前参数
        params = state['params']
        
        # 定义计算梯度和激活的函数
        def grad_fn(p):
            # 前向传播
            outputs = self.model_fn(p, inputs)
            # 计算损失
            loss = jnp.mean((outputs - targets)**2)
            return loss
        
        # 获取梯度
        grads = jax.grad(grad_fn)(params)
        
        # 获取激活值(前向传播中间结果)
        # 这需要对模型结构有所了解，这里简化处理
        # 在实际应用中，您需要从模型中提取激活值
        
        # 更新每一层的Kronecker因子
        updated_layers = []
        
        for layer in state['layers']:
            # 这里应该获取该层的实际激活值和梯度
            # 为简化，我们使用随机值代替
            act_shape = (inputs.shape[0], layer['shape'][1])
            acts = jax.random.normal(jax.random.PRNGKey(0), act_shape)
            
            grad_shape = (inputs.shape[0], layer['shape'][0])
            layer_grads = jax.random.normal(jax.random.PRNGKey(1), grad_shape)
            
            # 计算Kronecker因子
            A, G = self._compute_layer_stats(layer, acts, layer_grads)
            
            # 更新层状态
            updated_layer = dict(layer)
            updated_layer['A'] = A
            updated_layer['G'] = G
            updated_layers.append(updated_layer)
        
        # 更新状态
        new_state = dict(state)
        new_state['layers'] = updated_layers
        new_state['count'] += 1
        
        return new_state
    
    def _compute_natural_gradient(self, state, grads):
        """使用K-FAC近似计算自然梯度"""
        # 展平梯度
        flat_grads, tree_def = jax.tree_util.tree_flatten(grads)
        
        # 应用K-FAC近似到每一层
        natural_flat_grads = list(flat_grads)  # 复制原梯度
        
        for layer in state['layers']:
            idx = layer['index']
            param_grad = flat_grads[idx]
            
            if param_grad.ndim == 2:  # 权重矩阵
                # K-FAC自然梯度计算
                # natural_grad = G^{-1} * grad * A^{-1}
                A_inv = jnp.linalg.inv(layer['A'])
                G_inv = jnp.linalg.inv(layer['G'])
                
                # 重塑梯度以便矩阵操作
                grad_mat = param_grad
                
                # 应用Kronecker因子逆矩阵
                nat_grad = jnp.matmul(G_inv, jnp.matmul(grad_mat, A_inv))
                
                # 保存自然梯度
                natural_flat_grads[idx] = nat_grad
        
        # 重构梯度树
        natural_grads = jax.tree_util.tree_unflatten(tree_def, natural_flat_grads)
        
        # 应用范数约束(如果指定)
        if self.norm_constraint is not None:
            param_norm = optax.global_norm(grads)
            natural_grad_norm = optax.global_norm(natural_grads)
            
            # 如果自然梯度范数超过约束，进行缩放
            scale = jnp.minimum(1.0, self.norm_constraint / (natural_grad_norm + 1e-6))
            natural_grads = jax.tree_map(lambda ng: ng * scale, natural_grads)
        
        return natural_grads
    
    def update(self, grads, state, params=None):
        """更新参数"""
        # 更新计数器
        count = state['count'] + 1
        
        # 决定是否更新Kronecker因子
        # (这部分在实际使用中需要传入batch数据)
        if count % self.update_freq == 0:
            # 模拟一个batch
            batch_size = 32
            feature_dim = 10
            inputs = jnp.ones((batch_size, feature_dim))
            targets = jnp.ones((batch_size, 1))
            
            state = self._update_factor(state, (inputs, targets))
        
        # 计算自然梯度
        natural_grads = self._compute_natural_gradient(state, grads)
        
        # 应用学习率
        scaled_nat_grads = jax.tree_map(
            lambda ng: ng * self.learning_rate,
            natural_grads
        )
        
        # 应用基础SGD更新
        sgd_updates, new_sgd_state = optax.sgd(
            self.learning_rate, self.momentum
        ).update(scaled_nat_grads, state['sgd_state'], params)
        
        # 更新参数
        new_params = optax.apply_updates(params, sgd_updates)
        
        # 更新状态
        new_state = dict(state)
        new_state['count'] = count
        new_state['sgd_state'] = new_sgd_state
        new_state['params'] = new_params
        
        return sgd_updates, new_state 